pluginManagement {
    repositories {
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://wdnexus.autoai.com/content/repositories/autoai-AVS/' }
        maven { url "https://wdnexus.autoai.com/content/repositories/releases/" }
        maven { url 'https://jitpack.io' }
        maven {
            allowInsecureProtocol true
            url   'http://syshome.autoai.com/artifactory/repository/sw-release/'
            credentials {
                username 'swdeveloper'
                password 'swdeveloper'
            }
        }
        maven {
            allowInsecureProtocol true
            url   'http://syshome.autoai.com/artifactory/repository/sw-snapshot/'
            credentials {
                username 'swdeveloper'
                password 'swdeveloper'
            }
        }
        google()
        mavenCentral()
        maven { url 'https://maven.aliyun.com/repository/public' }
    }
}
def mavenUsername = "maven_reader"
def mavenPassword = "cmVmdGtuOjAxOjE3NTUyMzMyNDQ6N0htNWpaelhnNXlvWmhsWlJNQ3VTS0J3UGtH"
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_SETTINGS)
    repositories {
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://wdnexus.autoai.com/content/repositories/autoai-AVS/' }
        maven { url "https://wdnexus.autoai.com/content/repositories/releases/" }
        maven {
            allowInsecureProtocol true
            url   'http://syshome.autoai.com/artifactory/repository/sw-release/'
            credentials {
                username 'swdeveloper'
                password 'swdeveloper'
            }
        }
        maven {
            allowInsecureProtocol true
            url   'http://syshome.autoai.com/artifactory/repository/sw-snapshot/'
            credentials {
                username 'swdeveloper'
                password 'swdeveloper'
            }
        }
        maven {
            allowInsecureProtocol true
            url 'https://Jfrog.sgmw.com.cn/artifactory/android-maven-release/'
            credentials {
                username mavenUsername
                password mavenPassword
            }
        }
        maven { url 'https://jitpack.io' }
        google()
        mavenCentral()
    }
}

rootProject.name = "Ksongs"
include ':app'
include ':common'
include ':skin:autoinflater'
include ':skin:skincore:adapters:skinandroidx'
include ':skin:skincore:adapters:skinglideadapter'
include ':skin:skincore:skinframework'
