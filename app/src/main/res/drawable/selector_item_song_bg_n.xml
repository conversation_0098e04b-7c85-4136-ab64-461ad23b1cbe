<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/item_song_bg_select_color_n"/>
            <corners android:radius="@dimen/dp_20"/>
        </shape>
    </item>

    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/item_song_bg_press_color_n"/>
            <corners android:radius="@dimen/dp_20"/>
        </shape>
    </item>

    <item android:state_selected="false">
        <shape android:shape="rectangle">
            <solid android:color="@color/item_song_bg_normal_color_n"/>
        </shape>
    </item>

</selector>