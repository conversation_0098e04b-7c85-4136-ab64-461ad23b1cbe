<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#35CDD5DE" />
            <corners android:radius="@dimen/dp_20" />
        </shape>
    </item>
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="#35CDD5DE" />
            <corners android:radius="@dimen/dp_20" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#B3CDD5DE" />
            <corners android:radius="@dimen/dp_20" />
        </shape>
    </item>
</selector>
