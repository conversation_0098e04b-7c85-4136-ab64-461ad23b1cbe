<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 背景（外层圆角） -->
    <item android:id="@android:id/background">
        <shape android:shape="rectangle">
            <corners android:radius="20dip" />  <!-- 设置背景圆角 -->
            <gradient
                android:angle="270"
                android:centerColor="#4DFFFFFF"
                android:centerY="0.75"
                android:endColor="#4DFFFFFF"
                android:startColor="#4DFFFFFF" />
        </shape>
    </item>

    <!-- 次进度条（外层圆角） -->
    <item android:id="@android:id/secondaryProgress">
        <scale
            android:scaleHeight="100%"
            android:scaleGravity="bottom">
            <shape>
                <corners android:radius="20dip" />  <!-- 设置次进度条圆角 -->
                <gradient
                    android:angle="90"
                    android:endColor="#FFFFFF"
                    android:startColor="#FFFFFF" />
            </shape>
        </scale>
    </item>

    <!-- 进度条（内层圆角） -->
    <item android:id="@android:id/progress">
        <scale
            android:scaleHeight="100%"
            android:scaleGravity="bottom">
            <shape>
                <corners android:radius="20dip" />  <!-- 设置进度条圆角 -->
                <gradient
                    android:angle="90"
                    android:endColor="#ffffffff"
                    android:startColor="#ffffffff" />
            </shape>
        </scale>
    </item>

</layer-list>
