<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 正在播放，优先级最高 -->
    <item android:drawable="@mipmap/icon_correct_56_dis_playing" android:state_enabled="false" />
    <!-- 已添加 -->
    <item android:drawable="@mipmap/icon_correct_56_dis" android:state_enabled="true" android:state_pressed="false" android:state_selected="true" />
    <!-- 已添加 按下效果 -->
    <item android:drawable="@mipmap/icon_correct_56_dis_p" android:state_enabled="true" android:state_pressed="true" android:state_selected="true" />
    <!-- 按下效果 -->
    <item android:drawable="@mipmap/icon_add_56_p" android:state_enabled="true" android:state_pressed="true" android:state_selected="false" />
    <!-- 可添加 正常状态 -->
    <item android:drawable="@mipmap/icon_add_56" android:state_enabled="true" android:state_pressed="false" android:state_selected="false" />
</selector>