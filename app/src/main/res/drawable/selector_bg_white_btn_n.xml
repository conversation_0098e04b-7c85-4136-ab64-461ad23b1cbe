<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="false">
        <shape android:shape="rectangle">
            <solid android:color="#CCFFFFFF" />
            <corners android:radius="20dp" />
        </shape>
    </item>

    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#3DFFFFFF" />
            <corners android:radius="20dp" />
        </shape>
    </item>
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="#3DFFFFFF" />
            <corners android:radius="20dp" />
        </shape>
    </item>
</selector>