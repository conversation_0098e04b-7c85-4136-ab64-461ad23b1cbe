<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/bg_shadow"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/dialog_mask" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/dialog_content"
        android:layout_width="@dimen/dp_592"
        android:layout_height="@dimen/dp_366"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_312"
        android:background="@drawable/bg_dialog"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_20"
            android:background="@mipmap/icon64_close"
            android:importantForAccessibility="no"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="sans-serif-medium"
            android:text="@string/dialog_confirm_title"
            android:textColor="@color/normal_text_color"
            android:textSize="@dimen/sp_32"
            app:layout_constraintBottom_toBottomOf="@+id/iv_back"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/iv_back" />

        <TextView
            android:id="@+id/tv_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_60"
            android:text="@string/dialog_confirm_message"
            android:textColor="@color/normal_text_color"
            android:textSize="@dimen/sp_28"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_back" />

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="@dimen/dp_180"
            android:layout_height="@dimen/dp_72"
            android:layout_marginStart="@dimen/dp_98"
            android:layout_marginBottom="@dimen/dp_32"
            android:background="@drawable/selector_btn_bg_rect_20_1c7dff"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/dialog_confirm_button"
            android:textColor="@drawable/selector_btn_color_ffffff"
            android:textSize="@dimen/sp_26"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="@dimen/dp_180"
            android:layout_height="@dimen/dp_72"
            android:layout_marginEnd="@dimen/dp_98"
            android:layout_marginBottom="@dimen/dp_32"
            android:background="@drawable/selector_btn_bg_rect_20_b3cdd5de_66909ead"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/dialog_confirm_cancel"
            android:textColor="@drawable/selector_btn_color_262e33_ffffff"
            android:textSize="@dimen/sp_26"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>