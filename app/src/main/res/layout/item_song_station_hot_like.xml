<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tv_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:text="@string/home_title_like"
        android:textSize="28sp"
        android:fontFamily="sans-serif-medium"
        android:textColor="@color/normal_text_color"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_40"/>

    <ImageView
        android:id="@+id/iv_icon"
        app:layout_constraintLeft_toRightOf="@id/tv_title"
        app:layout_constraintTop_toTopOf="parent"
        android:src="@drawable/icon_genreal_more"
        android:layout_width="@dimen/dp_40"
        android:layout_height="@dimen/dp_40"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_like"
        android:layout_marginTop="@dimen/dp_24"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        android:background="@drawable/bg_home_like"
        android:paddingTop="@dimen/dp_40"
        android:paddingStart="@dimen/dp_40"
        android:paddingEnd="@dimen/dp_40"
        android:layout_width="@dimen/dp_1792"
        android:layout_height="@dimen/dp_412"/>

</androidx.constraintlayout.widget.ConstraintLayout>