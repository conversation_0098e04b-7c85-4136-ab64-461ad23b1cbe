<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/dp_80">

    <TextView
        android:id="@+id/tv_search_history"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_64"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@drawable/bg_item_search_history"
        android:ellipsize="end"
        android:gravity="center"
        android:maxWidth="@dimen/dp_290"
        android:maxLength="11"
        android:maxLines="1"
        android:paddingLeft="@dimen/dp_24"
        android:paddingTop="@dimen/dp_13"
        android:paddingRight="@dimen/dp_24"
        android:paddingBottom="@dimen/dp_15"
        android:textColor="@color/normal_text_color"
        android:textSize="@dimen/sp_24"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="七里香" />

    <ImageView
        android:id="@+id/iv_search_history_delete"
        android:layout_width="@dimen/dp_32"
        android:layout_height="@dimen/dp_32"
        android:background="@mipmap/icon_item_search_history_delete"
        android:importantForAccessibility="no"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>