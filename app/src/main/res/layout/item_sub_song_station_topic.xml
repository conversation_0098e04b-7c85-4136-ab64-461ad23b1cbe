<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/dp_424"
    android:layout_height="@dimen/dp_180">

  <ImageView
      android:id="@+id/iv_icon"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:scaleType="fitXY"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintLeft_toLeftOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_marginTop="@dimen/dp_64"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:textSize="@dimen/sp_32"
        android:fontFamily="sans-serif-medium"
        android:textColor="@color/white"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_48"/>


</androidx.constraintlayout.widget.ConstraintLayout>