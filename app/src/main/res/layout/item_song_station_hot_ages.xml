<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_40"
        android:text="@string/home_title_age"
        android:textSize="@dimen/sp_28"
        android:fontFamily="sans-serif-medium"
        android:textColor="@color/normal_text_color"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/ll_age_60"
        android:layout_width="@dimen/dp_332"
        android:layout_height="@dimen/dp_180"
        android:layout_marginTop="@dimen/dp_24"
        android:background="@mipmap/bg_1960s"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title">

        <TextView
            android:id="@+id/tv_age_60"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_64"
            android:gravity="center"
            android:fontFamily="sans-serif-medium"
            android:text="@string/home_age_content_60"
            android:textColor="@color/home_age_item"
            android:textSize="@dimen/sp_32" />

        <TextView
            android:id="@+id/tv_age_dis"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:visibility="gone"
            android:text="经典老歌，岁月情怀"
            android:textColor="@color/black"
            android:textSize="24sp" />
    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/ll_age_70"
        android:layout_width="@dimen/dp_332"
        android:layout_height="@dimen/dp_180"
        android:layout_marginStart="@dimen/dp_32"
        android:background="@mipmap/bg_1970s"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        app:layout_constraintLeft_toRightOf="@id/ll_age_60"
        app:layout_constraintTop_toTopOf="@id/ll_age_60">

        <TextView
            android:id="@+id/tv_age_70"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_64"
            android:gravity="center"
            android:fontFamily="sans-serif-medium"
            android:text="@string/home_age_content_70"
            android:textColor="@color/home_age_item"
            android:textSize="@dimen/sp_32" />

        <TextView
            android:id="@+id/tv_age_70_des"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:visibility="gone"
            android:text="怀旧旋律，回忆激昂"
            android:textColor="@color/black"
            android:textSize="24sp" />
    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/ll_age_80"
        android:layout_width="@dimen/dp_332"
        android:layout_height="@dimen/dp_180"
        android:layout_marginStart="@dimen/dp_32"
        android:background="@mipmap/bg_1980s"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        app:layout_constraintLeft_toRightOf="@id/ll_age_70"
        app:layout_constraintTop_toTopOf="@id/ll_age_60">

        <TextView
            android:id="@+id/tv_age_80"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_64"
            android:gravity="center"
            android:fontFamily="sans-serif-medium"
            android:text="@string/home_age_content_80"
            android:textColor="@color/home_age_item"
            android:textSize="@dimen/sp_32" />

        <TextView
            android:id="@+id/tv_age_80_des"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:visibility="gone"
            android:text="流行电子，时代印记"
            android:textColor="@color/black"
            android:textSize="24sp" />
    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/ll_age_90"
        android:layout_width="@dimen/dp_332"
        android:layout_height="@dimen/dp_180"
        android:layout_marginStart="@dimen/dp_32"
        android:background="@mipmap/bg_1990s"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        app:layout_constraintLeft_toRightOf="@id/ll_age_80"
        app:layout_constraintTop_toTopOf="@id/ll_age_60">

        <TextView
            android:id="@+id/tv_age_90"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_64"
            android:gravity="center"
            android:fontFamily="sans-serif-medium"
            android:text="@string/home_age_content_90"
            android:textColor="@color/home_age_item"
            android:textSize="@dimen/sp_32" />

        <TextView
            android:id="@+id/tv_age_90_des"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:visibility="gone"
            android:gravity="center"
            android:text="嘻哈节奏，个性张扬"
            android:textColor="@color/black"
            android:textSize="24sp" />
    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/ll_age_00"
        android:layout_width="@dimen/dp_332"
        android:layout_height="@dimen/dp_180"
        android:layout_marginStart="@dimen/dp_32"
        android:background="@mipmap/bg_2000s"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        app:layout_constraintLeft_toRightOf="@id/ll_age_90"
        app:layout_constraintTop_toTopOf="@id/ll_age_60">

        <TextView
            android:id="@+id/tv_age_00"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_64"
            android:gravity="center"
            android:fontFamily="sans-serif-medium"
            android:text="@string/home_age_content_00"
            android:textColor="@color/home_age_item"
            android:textSize="@dimen/sp_32" />

        <TextView
            android:id="@+id/tv_age_00_des"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:visibility="gone"
            android:text="快节奏，潮流前卫"
            android:textColor="@color/black"
            android:textSize="24sp" />
    </androidx.appcompat.widget.LinearLayoutCompat>


</androidx.constraintlayout.widget.ConstraintLayout>