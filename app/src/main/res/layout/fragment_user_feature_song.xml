<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/dp_56"
        android:layout_height="@dimen/dp_56"
        android:layout_marginStart="@dimen/dp_64"
        android:contentDescription="@string/back_content_description"
        android:src="@drawable/selector_icon_56_back"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvTitle" />

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_100"
        android:layout_marginStart="@dimen/dp_14"
        android:fontFamily="sans-serif-medium"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:lineSpacingExtra="@dimen/dp_48"
        android:text="@string/user_feature_song_like"
        android:textColor="@color/title_color"
        android:textSize="@dimen/sp_32"
        app:layout_constraintLeft_toRightOf="@id/iv_back"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        layout="@layout/layout_app_logo"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_80"
        android:layout_marginEnd="@dimen/dp_64_5"
        app:layout_constraintBottom_toBottomOf="@+id/iv_back"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iv_back" />

    <com.sgmw.common.widget.StateLayout
        android:id="@+id/stateLayout"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        app:emptyIvTopMargin="@dimen/dp_176"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        app:loadingIvTopMargin="@dimen/dp_257">

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/srl"
            style="@style/BaseRefreshStyle"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.scwang.smart.refresh.header.ClassicsHeader
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_124" />

            <com.sgmw.common.widget.CustomRecyclerView
                android:id="@+id/rvUserFeatureSong"
                style="@style/recy_vertical_style"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="@dimen/dp_64"
                android:layout_marginTop="@dimen/dp_24"
                android:background="@color/transparent"
                android:overScrollMode="never" />

            <com.sgmw.ksongs.widget.CustomClassicsFooter
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_124"
                app:srlAccentColor="@color/settings_text_color"
                app:srlDrawableMarginRight="@dimen/dp_8"
                app:srlDrawableProgress="@mipmap/icon48_load_more"
                app:srlDrawableProgressSize="@dimen/dp_48"
                app:srlTextSizeTitle="@dimen/sp_24" />

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    </com.sgmw.common.widget.StateLayout>


</androidx.constraintlayout.widget.ConstraintLayout>