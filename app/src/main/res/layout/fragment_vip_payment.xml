<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_80"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_64_5"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/dp_56"
            android:layout_height="@dimen/dp_56"
            android:contentDescription="@string/back_content_description"
            android:scaleType="centerInside"
            android:src="@drawable/selector_icon_56_back" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_48"
            android:layout_weight="1"
            android:fontFamily="sans-serif-medium"
            android:gravity="center_vertical"
            android:includeFontPadding="false"
            android:paddingStart="@dimen/dp_14"
            android:text="@string/vip_payment_title"
            android:textColor="@color/normal_text_color"
            android:textSize="@dimen/sp_32" />

        <include
            android:id="@+id/layout_icon"
            layout="@layout/layout_app_logo"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_80" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_650"
        android:layout_marginLeft="@dimen/dp_64"
        android:layout_marginTop="@dimen/dp_32"
        android:layout_marginRight="@dimen/dp_64"
        android:layout_marginBottom="@dimen/dp_32"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="@dimen/dp_808"
            android:layout_height="match_parent"
            android:background="@drawable/bg_vip_pay"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:paddingLeft="@dimen/dp_48"
            android:paddingTop="@dimen/dp_40"
            android:paddingRight="@dimen/dp_48">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/dp_40">

                <ImageView
                    android:id="@+id/iv_avatar"
                    android:layout_width="@dimen/dp_100"
                    android:layout_height="@dimen/dp_100"
                    android:importantForAccessibility="no"
                    android:src="@mipmap/ksongs_useravator_default" />

                <TextView
                    android:id="@+id/tv_user_name"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_45"
                    android:layout_gravity="center_vertical"
                    android:fontFamily="sans-serif-medium"
                    android:gravity="center_vertical"
                    android:paddingStart="@dimen/dp_20"
                    tools:text="-"
                    android:textColor="@color/normal_text_color"
                    android:textSize="@dimen/sp_30" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:background="@drawable/bg_vip_view" />

            <TextView
                android:id="@+id/tv_price"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_54"
                android:layout_marginTop="@dimen/dp_7"
                android:textColor="#FF00B2E3"
                android:textSize="@dimen/sp_36"
                android:textStyle="bold"
                tools:text="¥20" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="@dimen/dp_332"
                android:layout_height="@dimen/dp_332"
                android:layout_marginTop="@dimen/dp_6">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_qrcode"
                    android:layout_width="@dimen/dp_290"
                    android:layout_height="@dimen/dp_290"
                    android:background="@drawable/bg_qr"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.tme.ktv.qrcode.QRCodeView
                        android:id="@+id/iv_payment_qr"
                        android:layout_width="@dimen/dp_240"
                        android:layout_height="@dimen/dp_240"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <LinearLayout
                    android:id="@+id/layout_load_status"
                    android:layout_width="@dimen/dp_322"
                    android:layout_height="@dimen/dp_332"
                    android:background="@mipmap/ksongs_qrcode_mark"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:id="@+id/iv_load_status"
                        android:layout_width="@dimen/dp_140"
                        android:layout_height="@dimen/dp_140"
                        android:importantForAccessibility="no" />

                    <TextView
                        android:id="@+id/tv_load_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_10"
                        android:visibility="gone" />

                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_42"
                android:text="@string/vip_payment_content"
                android:textColor="@color/normal_text_color"
                android:textSize="@dimen/sp_28" />

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerview"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/dp_32"
            android:layout_weight="1" />

    </LinearLayout>

</LinearLayout>