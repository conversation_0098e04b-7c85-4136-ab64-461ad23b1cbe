<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">
  <androidx.appcompat.widget.LinearLayoutCompat
      android:id="@+id/ll_title"
      app:layout_constraintLeft_toLeftOf="parent"
      app:layout_constraintTop_toTopOf="parent"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:orientation="horizontal">
      <TextView
          android:id="@+id/tv_title"
          android:text="@string/home_title_rangks"
          android:textSize="28sp"
          android:fontFamily="sans-serif-medium"
          android:textColor="@color/normal_text_color"
          android:layout_width="@dimen/dp_112"
          android:layout_height="@dimen/dp_40"/>

      <ImageView
          android:id="@+id/iv_icon"
          android:src="@drawable/icon_genreal_more"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"/>

  </androidx.appcompat.widget.LinearLayoutCompat>

  <androidx.appcompat.widget.LinearLayoutCompat
      android:id="@+id/ll_recycler"
      android:layout_marginTop="@dimen/dp_24"
      app:layout_constraintLeft_toLeftOf="parent"
      app:layout_constraintTop_toBottomOf="@id/ll_title"
      android:orientation="horizontal"
      android:layout_width="match_parent"
      android:layout_height="wrap_content">
     <androidx.appcompat.widget.LinearLayoutCompat
         android:layout_width="@dimen/dp_880"
         android:background="@drawable/bg_home_hot_ranks"
         android:layout_height="@dimen/dp_480"
         android:orientation="vertical">
         <androidx.appcompat.widget.LinearLayoutCompat
             android:id="@+id/ll_hot_title"
             android:layout_width="wrap_content"
             android:layout_height="@dimen/dp_56"
             android:layout_marginTop="@dimen/dp_36"
             android:layout_marginStart="@dimen/dp_40"
             android:layout_gravity="center_vertical"
             android:orientation="horizontal">
             <TextView
                 android:id="@+id/tv_hot_title"
                 android:text="@string/home_title_hot_ranks"
                 android:textSize="32sp"
                 android:includeFontPadding="false"
                 android:fontFamily="sans-serif-medium"
                 android:gravity="center_vertical"
                 android:textColor="@color/normal_text_color"
                 android:layout_width="wrap_content"
                 android:layout_height="@dimen/dp_56"/>

             <ImageView
                 android:id="@+id/iv_hot_icon"
                 android:layout_gravity="center"
                 android:src="@drawable/icon_56_more"
                 android:layout_marginStart="@dimen/dp_8"
                 android:layout_width="@dimen/dp_56"
                 android:layout_height="@dimen/dp_56"/>

         </androidx.appcompat.widget.LinearLayoutCompat>

         <androidx.recyclerview.widget.RecyclerView
             android:id="@+id/rv_Hot"
             android:layout_marginTop="@dimen/dp_16"
             android:layout_marginStart="@dimen/dp_40"
             android:layout_width="match_parent"
             android:layout_height="wrap_content"/>

     </androidx.appcompat.widget.LinearLayoutCompat>
      <androidx.appcompat.widget.LinearLayoutCompat
          android:layout_width="@dimen/dp_880"
          android:layout_marginStart="@dimen/dp_32"
          android:layout_height="@dimen/dp_480"
          android:background="@drawable/bg_home_hot_ranks"
          android:orientation="vertical">
          <androidx.appcompat.widget.LinearLayoutCompat
              android:id="@+id/ll_weekly_title"
              android:layout_width="wrap_content"
              android:layout_height="@dimen/dp_56"
              android:layout_marginTop="@dimen/dp_36"
              android:layout_marginStart="@dimen/dp_40"
              android:layout_gravity="center_vertical"
              android:orientation="horizontal">
              <TextView
                  android:id="@+id/tv_weekly_title"
                  android:text="@string/home_title_weekly"
                  android:textSize="32sp"
                  android:fontFamily="sans-serif-medium"
                  android:gravity="center_vertical"
                  android:includeFontPadding="false"
                  android:textColor="@color/normal_text_color"
                  android:layout_width="wrap_content"
                  android:layout_height="@dimen/dp_56"/>

              <ImageView
                  android:id="@+id/iv_weekly_icon"
                  android:src="@drawable/icon_56_more"
                  android:layout_gravity="center"
                  android:layout_marginStart="@dimen/dp_8"
                  android:layout_width="@dimen/dp_56"
                  android:layout_height="@dimen/dp_56" />

          </androidx.appcompat.widget.LinearLayoutCompat>

          <androidx.recyclerview.widget.RecyclerView
              android:id="@+id/rv_Hot_weekly"
              android:layout_marginTop="@dimen/dp_16"
              android:layout_marginStart="@dimen/dp_40"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"/>

      </androidx.appcompat.widget.LinearLayoutCompat>



  </androidx.appcompat.widget.LinearLayoutCompat>>


</androidx.constraintlayout.widget.ConstraintLayout>