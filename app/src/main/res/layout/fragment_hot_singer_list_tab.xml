<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        style="@style/BigCustomTabLayout"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_80"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginTop="@dimen/dp_15"
        app:layout_constraintLeft_toLeftOf="@+id/iv_back"
        app:layout_constraintTop_toTopOf="parent"
        app:tabIndicatorAnimationDuration="160" />

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/dp_56"
        android:layout_height="@dimen/dp_56"
        android:layout_marginStart="@dimen/dp_64"
        android:layout_marginTop="@dimen/dp_22"
        android:background="@drawable/selector_icon_56_back"
        android:contentDescription="@string/back_content_description"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        layout="@layout/layout_app_logo"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_80"
        android:layout_marginEnd="@dimen/dp_64_5"
        app:layout_constraintBottom_toBottomOf="@+id/iv_back"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iv_back" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_64"
        android:layout_marginTop="@dimen/dp_20"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tabLayout" />

</androidx.constraintlayout.widget.ConstraintLayout>