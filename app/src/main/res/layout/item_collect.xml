<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_120"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/selector_item_song_bg">


    <TextView
        android:id="@+id/tvNum"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="@drawable/selector_item_song_name_color"
        android:textSize="@dimen/sp_28"
        android:maxLines="1"
        android:singleLine="true"
        android:ellipsize="middle"
        app:layout_constraintStart_toStartOf="@+id/ivPlayStatus"
        app:layout_constraintEnd_toEndOf="@+id/ivPlayStatus"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="1000" />

    <ImageView
        android:id="@+id/ivPlayStatus"
        android:layout_width="@dimen/dp_80"
        android:layout_height="@dimen/dp_80"
        android:src="@mipmap/icon_playing_80"
        android:scaleType="centerInside"
        android:layout_marginStart="@dimen/dp_12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="invisible"
        tools:visibility="visible"/>

    <ImageView
        android:id="@+id/ivCover"
        android:layout_width="@dimen/dp_88"
        android:layout_height="@dimen/dp_88"
        android:src="@mipmap/no_album_player"
        android:layout_marginStart="@dimen/dp_12"
        app:layout_constraintStart_toEndOf="@+id/ivPlayStatus"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/tvSongName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@drawable/selector_item_song_name_color"
        android:textSize="@dimen/sp_28"
        app:layout_constraintStart_toEndOf="@id/ivCover"
        app:layout_constraintTop_toTopOf="@+id/ivCover"
        app:layout_constrainedWidth="true"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        android:layout_marginStart="@dimen/dp_24"
        android:layout_marginTop="@dimen/dp_2"
        tools:text="歌曲名称"/>

    <View
        android:id="@+id/guidLine"
        app:layout_constraintStart_toEndOf="@+id/ivCover"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginStart="@dimen/dp_20"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"/>


    <ImageView
        android:id="@+id/ivVip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@mipmap/icon_vip_tag"
        android:visibility="visible"
        tools:visibility="visible"
        android:scaleType="centerInside"
        android:layout_marginStart="@dimen/dp_4"
        app:layout_constraintStart_toEndOf="@id/guidLine"
        app:layout_constraintBottom_toBottomOf="@id/ivCover"
        android:layout_marginBottom="@dimen/dp_13"/>

    <ImageView
        android:id="@+id/ivMv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:src="@mipmap/icon_mv_tag"
        android:visibility="gone"
        tools:visibility="visible"
        android:scaleType="centerInside"
        app:layout_constraintStart_toEndOf="@id/ivVip"
        app:layout_constraintBottom_toBottomOf="@id/ivCover"
        android:layout_marginBottom="@dimen/dp_13"/>

    <ImageView
        android:id="@+id/ivScore"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@mipmap/icon_score_tag"
        android:visibility="gone"
        tools:visibility="visible"
        android:scaleType="centerInside"
        android:layout_marginStart="@dimen/dp_4"
        app:layout_constraintStart_toEndOf="@id/ivMv"
        app:layout_constraintBottom_toBottomOf="@id/ivCover"
        android:layout_marginBottom="@dimen/dp_13"/>


    <TextView
        android:id="@+id/tvSongAuthor"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_36"
        android:textColor="@drawable/selector_item_song_author_color"
        android:textSize="@dimen/sp_24"
        android:fontFamily="sans-serif"
        android:lineSpacingExtra="@dimen/dp_36"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="@id/ivCover"
        app:layout_constraintStart_toEndOf="@id/ivScore"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginBottom="@dimen/dp_5"
        tools:text="作者"/>


    <ImageView
        android:id="@+id/ivUp"
        android:layout_width="@dimen/dp_72"
        android:layout_height="@dimen/dp_72"
        android:layout_marginEnd="@dimen/dp_16"
        android:gravity="center_vertical"
        android:src="@drawable/icon_up_72"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/ivAdd"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivAdd"
        android:layout_width="@dimen/dp_56"
        android:layout_height="@dimen/dp_56"
        android:layout_marginEnd="@dimen/dp_40"
        android:gravity="center_vertical"
        android:src="@drawable/selector_icon_add_toggle"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/ivSelect"
        app:layout_constraintTop_toTopOf="parent" />


    <ImageView
        android:id="@+id/ivSelect"
        android:src="@drawable/selector_item_collect_select"
        android:layout_width="@dimen/dp_56"
        android:layout_height="@dimen/dp_56"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_marginEnd="@dimen/dp_40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />



</androidx.constraintlayout.widget.ConstraintLayout>