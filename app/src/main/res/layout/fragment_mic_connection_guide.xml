<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_100"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/dp_48"
        android:paddingRight="@dimen/dp_64_5">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/dp_56"
            android:layout_height="@dimen/dp_56"
            android:background="@drawable/selector_icon_56_back"></ImageView>

        <TextView
            style="@style/TitleStyle"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:text="@string/settings_mic_connecttion_guide"></TextView>

        <include
            layout="@layout/layout_app_logo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

        </include>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_650"
        android:orientation="horizontal"
        android:layout_marginTop="@dimen/dp_32"
        android:layout_marginLeft="@dimen/dp_64"
        android:layout_marginRight="@dimen/dp_64">

        <LinearLayout
            android:layout_width="@dimen/dp_500"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:background="@color/mic_connection_guide_left_bg_color">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="@dimen/dp_300"
                android:layout_height="@dimen/dp_300"
                android:background="@drawable/bg_qr"
                android:layout_marginTop="@dimen/dp_18">

                <com.tme.ktv.qrcode.QRCodeView
                    android:id="@+id/iv_payment_qr"
                    android:layout_width="@dimen/dp_240"
                    android:layout_height="@dimen/dp_240"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    />

                <LinearLayout
                    android:id="@+id/layout_load_status"
                    android:layout_width="@dimen/dp_240"
                    android:layout_height="@dimen/dp_240"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:background="@color/black_opacity_60"
                    android:visibility="gone">

                    <ImageView
                        android:id="@+id/iv_load_status"
                        android:layout_width="@dimen/dp_120"
                        android:layout_height="@dimen/dp_120">

                    </ImageView>

                    <TextView
                        android:id="@+id/tv_load_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_10">

                    </TextView>

                </LinearLayout>


            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/mic_connection_guide_payment"
                android:textColor="@color/normal_text_color"
                android:textSize="@dimen/sp_28"
                android:layout_marginTop="@dimen/dp_14">

            </TextView>
        </LinearLayout>

        <LinearLayout
            android:layout_width="@dimen/dp_1260"
            android:layout_height="match_parent"
            android:gravity="center"
            android:layout_marginLeft="@dimen/dp_32"
            android:background="@color/mic_connection_guide_right_bg_color">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/mic_connection_guide_text"
                android:textColor="@color/normal_text_color"
                android:textSize="@dimen/sp_28">

            </TextView>

        </LinearLayout>

    </LinearLayout>


</LinearLayout>