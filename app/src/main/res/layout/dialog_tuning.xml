<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/bg_shadow"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/dialog_mask" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/dialog_content"
        android:layout_width="@dimen/dp_1160"
        android:layout_height="@dimen/dp_464"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_263"
        android:background="@drawable/bg_vehicle">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/dp_64"
            android:layout_height="@dimen/dp_64"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_20"
            android:background="@mipmap/icon64_close"
            android:contentDescription="@string/back_content_description"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_song_name"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_48"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:text="@string/tuning_title"
            android:textColor="@color/normal_text_color"
            android:textSize="@dimen/sp_32"
            app:layout_constraintBottom_toBottomOf="@+id/iv_back"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_back"
            app:layout_constraintTop_toTopOf="@+id/iv_back" />

        <TextView
            android:id="@+id/tv_up_down_tons"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_42"
            android:layout_marginStart="@dimen/dp_132"
            android:layout_marginTop="@dimen/dp_117"
            android:text="@string/tuning_up_down_tons"
            android:textColor="@color/font_main_color"
            android:textSize="@dimen/sp_28"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_accompany_voice"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_42"
            android:layout_marginStart="@dimen/dp_104"
            android:layout_marginTop="@dimen/dp_70"
            android:text="@string/tuning_accompany_voice"
            android:textColor="@color/font_main_color"
            android:textSize="@dimen/sp_28"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_up_down_tons" />

        <TextView
            android:id="@+id/tv_mic_voice"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_42"
            android:layout_marginStart="@dimen/dp_104"
            android:layout_marginTop="@dimen/dp_70"
            android:text="@string/tuning_mic_voice"
            android:textColor="@color/font_main_color"
            android:textSize="@dimen/sp_28"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_accompany_voice" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_tuning"
            android:layout_width="@dimen/dp_800"
            android:layout_height="@dimen/dp_72"
            android:layout_marginStart="@dimen/dp_256"
            android:layout_marginTop="@dimen/dp_104"
            android:layout_marginEnd="@dimen/dp_104"
            android:background="@drawable/bg_tuning_seek"
            android:gravity="center_vertical"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_tuning_current"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/number_0"
                android:textColor="@color/font_main_color"
                android:textSize="@dimen/sp_26"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@+id/seekBar_tuning"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toStartOf="@+id/seekBar_tuning"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_tuning_min"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/number_navigate_12"
                android:textColor="@color/font_main_color"
                android:textSize="@dimen/sp_26"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatSeekBar
                android:id="@+id/seekBar_tuning"
                android:layout_width="800dp"
                android:layout_height="@dimen/dp_64"
                android:background="@null"
                android:max="12"
                android:min="-12"
                android:paddingStart="@dimen/dp_4"
                android:paddingEnd="@dimen/dp_4"
                android:progressDrawable="@drawable/progress_tuning"
                android:thumb="@null"
                android:thumbOffset="@dimen/dp_0"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:progress="0" />

            <TextView
                android:id="@+id/tv_tuning_max"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_39"
                android:layout_marginEnd="@dimen/dp_24"
                android:text="@string/number_0"
                android:textColor="@color/settings_text_color"
                android:textSize="@dimen/sp_26"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_accompany"
            android:layout_width="@dimen/dp_800"
            android:layout_height="@dimen/dp_72"
            android:layout_marginStart="@dimen/dp_256"
            android:layout_marginTop="@dimen/dp_40"
            android:layout_marginEnd="@dimen/dp_104"
            android:background="@drawable/bg_tuning_seek"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cl_tuning">

            <TextView
                android:id="@+id/tv_accompany_min"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/number_0"
                android:textColor="@color/settings_text_color"
                android:textSize="@dimen/sp_26"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatSeekBar
                android:id="@+id/seekBar_accompany"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp_64"
                android:background="@null"
                android:max="39"
                android:min="0"
                android:paddingStart="@dimen/dp_4"
                android:paddingEnd="@dimen/dp_4"
                android:progress="15"
                android:progressDrawable="@drawable/progress_tuning"
                android:thumb="@null"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_accompany_max"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_42"
                android:layout_marginEnd="@dimen/dp_24"
                android:text="@string/number_15"
                android:textColor="@color/settings_text_color"
                android:textSize="@dimen/sp_26"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_mic"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_72"
            android:layout_marginStart="@dimen/dp_256"
            android:layout_marginTop="@dimen/dp_40"
            android:layout_marginEnd="@dimen/dp_104"
            android:background="@drawable/bg_tuning_seek"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp_4"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cl_accompany">

            <TextView
                android:id="@+id/tv_mic_min"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/number_1"
                android:textColor="@color/font_main_color"
                android:textSize="@dimen/sp_26"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatSeekBar
                android:id="@+id/seekBar_mic"
                android:layout_width="@dimen/dp_800"
                android:layout_height="@dimen/dp_64"
                android:background="@null"
                android:max="39"
                android:min="1"
                android:paddingStart="@dimen/dp_4"
                android:paddingEnd="@dimen/dp_4"
                android:progress="20"
                android:progressDrawable="@drawable/progress_tuning"
                android:thumb="@null"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_mic_max"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_39"
                android:layout_marginEnd="@dimen/dp_24"
                android:text="@string/number_20"
                android:textColor="@color/settings_text_color"
                android:textSize="@dimen/sp_26"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


</FrameLayout>