<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/dp_424"
    android:layout_height="@dimen/dp_140"
    android:background="@mipmap/bg_item_suggestion_search_singer">

    <com.sgmw.ksongs.widget.CircleImageView
        android:id="@+id/iv_singer_icon"
        android:layout_width="@dimen/dp_96"
        android:layout_height="@dimen/dp_96"
        android:layout_marginStart="@dimen/dp_40"
        android:importantForAccessibility="no"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_singer_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_58"
        android:layout_marginTop="@dimen/dp_4"
        android:ellipsize="end"
        android:maxEms="10"
        android:maxLines="1"
        android:textColor="@color/normal_text_color"
        android:textSize="@dimen/sp_26"
        app:layout_constraintLeft_toRightOf="@+id/iv_singer_icon"
        app:layout_constraintTop_toTopOf="@+id/iv_singer_icon"
        tools:text="ssssssssss" />

    <TextView
        android:id="@+id/tv_songs_num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_16"
        android:ellipsize="end"
        android:maxEms="10"
        android:maxLines="1"
        android:textColor="@color/settings_text_color"
        android:textSize="@dimen/sp_24"
        app:layout_constraintLeft_toLeftOf="@+id/tv_singer_name"
        app:layout_constraintTop_toBottomOf="@+id/tv_singer_name"
        tools:text="sssss" />

</androidx.constraintlayout.widget.ConstraintLayout>