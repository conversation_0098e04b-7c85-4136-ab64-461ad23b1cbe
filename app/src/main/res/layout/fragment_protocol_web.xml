<?xml version="1.0" encoding="utf-8"?>
<com.sgmw.common.widget.StateLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/stateLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:loadingIvTopMargin="@dimen/dp_253"
    app:errorIvTopMargin="@dimen/dp_172"
    android:background="@color/mic_connection_guide_right_bg_color">

    <WebView
        android:id="@+id/webView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layerType="software">

    </WebView>

</com.sgmw.common.widget.StateLayout>