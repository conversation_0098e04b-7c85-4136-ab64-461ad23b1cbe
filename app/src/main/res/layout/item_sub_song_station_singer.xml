<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/dp_200"
    android:layout_height="@dimen/dp_234">

    <ImageView
        android:id="@+id/ivSingerIcon"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="@dimen/dp_160"
        android:layout_height="@dimen/dp_160"
        android:layout_marginTop="@dimen/dp_16"
        android:scaleType="fitCenter"/>

    <TextView
        android:id="@+id/tv_singer_name"
        app:layout_constraintLeft_toLeftOf="@id/ivSingerIcon"
        app:layout_constraintRight_toRightOf="@id/ivSingerIcon"
        app:layout_constraintTop_toBottomOf="@id/ivSingerIcon"
        android:text="周杰伦"
        android:layout_marginTop="@dimen/dp_14"
        android:textSize="@dimen/sp_28"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center"
        android:textColor="@color/normal_text_color"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_42"/>


</androidx.constraintlayout.widget.ConstraintLayout>