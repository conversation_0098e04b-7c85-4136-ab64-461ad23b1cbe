<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools">

    <ImageView
        android:id="@+id/ivThemeBg"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintDimensionRatio="h,1:1"/>

    <TextView
        android:id="@+id/tvThemeName"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_14"
        android:textSize="@dimen/sp_32"
        tools:text="二级分类名称"
        android:gravity="center_horizontal"
        android:maxLines="2"
        android:ellipsize="end"
        android:fontFamily="sans-serif-medium"
        android:textColor="@color/color_B3262E33_B3FFFFFF"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivThemeBg"/>


</androidx.constraintlayout.widget.ConstraintLayout>