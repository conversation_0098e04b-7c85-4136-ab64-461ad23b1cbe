<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <!--  播放背景  <-->
    <FrameLayout
        android:id="@+id/play_background_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <!--  视频面板  -->
    <FrameLayout
        android:id="@+id/play_mv_area_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <!--  播放区域  -->
    <FrameLayout
        android:id="@+id/play_area_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    </FrameLayout>

    <!-- 互动信息  -->
    <FrameLayout
        android:id="@+id/play_interactive_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

    <!--  弹窗区域  -->
    <FrameLayout
        android:id="@+id/play_popup_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</FrameLayout>