<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/bg_shadow"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/dialog_mask" />

    <LinearLayout
        android:id="@+id/dialog_content"
        android:layout_width="@dimen/dp_592"
        android:layout_height="@dimen/dp_364"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_360"
        android:background="@drawable/bg_dialog"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:layout_width="@dimen/dp_156"
            android:layout_height="@dimen/dp_156"
            android:background="@mipmap/loading_00"
            android:importantForAccessibility="no" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_8"
            android:text="@string/clearing_cache"
            android:textColor="@color/normal_text_color"
            android:textSize="@dimen/sp_28" />

    </LinearLayout>

</FrameLayout>