<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/bg_shadow"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/dialog_mask" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/dialog_content"
        android:layout_width="@dimen/dp_804"
        android:layout_height="@dimen/dp_450"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_270"
        android:background="@drawable/bg_vehicle">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/dp_64"
            android:layout_height="@dimen/dp_64"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_20"
            android:background="@mipmap/icon64_close"
            android:importantForAccessibility="no"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_song_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawablePadding="@dimen/dp_10"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:textColor="@color/normal_text_color"
            android:textSize="@dimen/sp_32"
            app:drawableStartCompat="@mipmap/icon48_music_score"
            app:layout_constraintBottom_toBottomOf="@+id/iv_back"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_back"
            app:layout_constraintTop_toTopOf="@+id/iv_back"
            tools:text="晴天" />

        <TextView
            android:id="@+id/tv_level"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_54"
            android:fontFamily="sans-serif-medium"
            android:textColor="@color/font_score_color"
            android:textSize="@dimen/sp_60"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_song_name"
            tools:text="SSS"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/iv_level"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_74"
            android:importantForAccessibility="no"
            android:src="@mipmap/icon_sss"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_song_name"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_score"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_64"
            android:textColor="@color/normal_text_color"
            android:textSize="@dimen/sp_28"
            app:layout_constraintBottom_toTopOf="@+id/btn_next"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="获得总分: 779分" />

        <TextView
            android:id="@+id/btn_next"
            android:layout_width="@dimen/dp_180"
            android:layout_height="@dimen/dp_72"
            android:layout_marginBottom="@dimen/dp_32"
            android:background="@drawable/selector_btn_bg_rect_20_1c7dff"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/score_confirm_btn"
            android:textColor="@drawable/selector_btn_color_ffffff"
            android:textSize="@dimen/sp_26"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/btn_replay"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/btn_replay"
            android:layout_width="@dimen/dp_180"
            android:layout_height="@dimen/dp_72"
            android:layout_marginStart="@dimen/dp_36"
            android:background="@drawable/selector_btn_bg_rect_20_b3cdd5de_66909ead"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/score_replay"
            android:textColor="@drawable/selector_btn_color_262e33_ffffff"
            android:textSize="@dimen/sp_26"
            app:layout_constraintBottom_toBottomOf="@+id/btn_next"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/btn_next"
            app:layout_constraintTop_toTopOf="@+id/btn_next" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</FrameLayout>