<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_48"
    android:gravity="center">

    <ImageView
        android:id="@+id/iv_loading_progress"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:background="@mipmap/icon48_load_more"
        >

    </ImageView>

    <TextView
        android:id="@+id/tv_load_text"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_36"
        android:textSize="@dimen/sp_24"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_gravity="center"
        android:textColor="@color/settings_text_color"
        android:text="加载中...">

    </TextView>

</LinearLayout>