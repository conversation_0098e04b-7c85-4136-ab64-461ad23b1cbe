<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_100"
        android:layout_marginStart="@dimen/dp_72"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_222"
        android:paddingBottom="@dimen/dp_24"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tabGravity="start"
        app:tabIndicator="@drawable/tab_indicator"
        app:tabIndicatorAnimationDuration="160"
        app:tabIndicatorColor="@color/c_1990FF"
        app:tabIndicatorFullWidth="false"
        app:tabMaxWidth="@dimen/dp_500"
        app:tabMode="scrollable"
        app:tabPaddingEnd="@dimen/dp_80"
        app:tabPaddingStart="@dimen/dp_80"
        app:tabPaddingTop="@dimen/dp_14"
        app:tabRippleColor="@null"
        app:tabSelectedTextColor="@color/tab_text_color_selected"
        app:tabTextAppearance="@style/TabLayoutTextStyle"
        app:tabTextColor="@color/tab_text_color_normal" />

    <View
        android:layout_width="@dimen/dp_152"
        android:layout_height="@dimen/dp_0"
        android:background="@color/bg_main_color"
        app:layout_constraintBottom_toBottomOf="@id/tabLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tabLayout" />

    <View
        android:layout_width="@dimen/dp_302"
        android:layout_height="@dimen/dp_0"
        android:background="@color/bg_main_color"
        app:layout_constraintBottom_toBottomOf="@id/tabLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tabLayout" />

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/dp_56"
        android:layout_height="@dimen/dp_56"
        android:layout_marginStart="@dimen/dp_64"
        android:layout_marginTop="@dimen/dp_22"
        android:contentDescription="@string/back_content_description"
        android:src="@drawable/selector_icon_56_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        layout="@layout/layout_app_logo"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_80"
        android:layout_marginEnd="@dimen/dp_64_5"
        app:layout_constraintBottom_toBottomOf="@+id/iv_back"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iv_back" />

    <com.sgmw.common.widget.StateLayout
        android:id="@+id/stateLayout"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        android:overScrollMode="never"
        app:errorIvTopMargin="@dimen/dp_166"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tabLayout"
        app:loadingIvTopMargin="@dimen/dp_247">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never" />

    </com.sgmw.common.widget.StateLayout>


</androidx.constraintlayout.widget.ConstraintLayout>