<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_main_color"
    android:paddingTop="@dimen/dp_10">

    <include
        android:id="@+id/layout_logo"
        layout="@layout/layout_app_logo"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_80"
        android:layout_marginEnd="@dimen/dp_64_5"
        app:layout_constraintRight_toRightOf="parent"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_parent_qrcode"
        android:layout_width="@dimen/dp_332"
        android:layout_height="@dimen/dp_332"
        android:layout_marginTop="@dimen/dp_62"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layout_logo">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_qrcode"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@mipmap/ksong_qrcode_background_nomal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.tme.ktv.qrcode.QRCodeView
                android:id="@+id/iv_qrcode"
                android:layout_width="@dimen/dp_240"
                android:layout_height="@dimen/dp_240"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <FrameLayout
            android:id="@+id/layout_load_status"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@mipmap/ksongs_qrcode_mark"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_qrcode_tip"
                android:layout_width="@dimen/dp_140"
                android:layout_height="@dimen/dp_140"
                android:layout_gravity="center" />

            <ImageView
                android:id="@+id/iv_login_status"
                android:layout_width="@dimen/dp_72"
                android:layout_height="@dimen/dp_72"
                android:layout_gravity="center"
                />

            <TextView
                android:id="@+id/tv_load_status"
                android:layout_width="@dimen/dp_300"
                android:layout_height="@dimen/dp_300"
                android:layout_gravity="center"
                android:gravity="center"
                android:lineHeight="@dimen/dp_48"
                android:contentDescription="(@hide_uc(value=[true]))"
                android:text="@string/qr_code_agree_text"
                android:textColor="@color/normal_text_color"
                android:textSize="32sp"
                android:visibility="gone" />

        </FrameLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_scan_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_18"
        android:fontFamily="sans-serif-medium"
        android:text="@string/login_scan_tip"
        android:textColor="@color/normal_text_color"
        android:textSize="@dimen/sp_32"
        android:includeFontPadding="false"
        android:contentDescription="(@hide_uc(value=[true]))"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cl_parent_qrcode" />

    <CheckBox
        android:id="@+id/cb_agree"
        android:layout_width="@dimen/dp_64"
        android:layout_height="@dimen/dp_64"
        android:layout_marginTop="@dimen/dp_34"
        android:button="@drawable/bg_login_checkbox"
        android:gravity="center"
        android:checked="false"
        android:contentDescription="@string/content_description_agree"
        app:layout_constraintEnd_toStartOf="@+id/tv_agree"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_scan_tip" />

    <TextView
        android:id="@+id/tv_agree"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_3"
        android:text="@string/login_privacy_tip"
        android:textColor="@color/normal_text_color"
        android:textSize="@dimen/sp_28"
        android:visibility="visible"
        android:includeFontPadding="false"
        android:contentDescription="(@hide_uc(value=[true]))"
        app:layout_constraintBottom_toBottomOf="@+id/cb_agree"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/cb_agree"
        app:layout_constraintTop_toTopOf="@+id/cb_agree" />

    <Button
        android:id="@+id/bt_agree"
        android:layout_width="@dimen/dp_180"
        android:layout_height="@dimen/dp_72"
        android:layout_marginEnd="@dimen/dp_40"
        style="@style/BlueButton"
        android:text="@string/login_agree"
        android:textSize="@dimen/sp_26"
        android:includeFontPadding="false"
        android:visibility="visible"
        android:contentDescription="@string/login_agree"
        app:layout_constraintEnd_toStartOf="@+id/bt_exit"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/bt_exit" />

    <Button
        android:id="@+id/bt_exit"
        android:layout_width="@dimen/dp_180"
        android:layout_height="@dimen/dp_72"
        android:layout_marginTop="@dimen/dp_42"
        style="@style/WhiteButton"
        android:text="@string/login_disagree"
        android:contentDescription="退出应用;退出全民K歌"
        android:textSize="@dimen/sp_26"
        android:includeFontPadding="false"
        app:layout_constraintBottom_toBottomOf="@+id/bt_agree"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/bt_agree"
        app:layout_constraintTop_toBottomOf="@+id/tv_agree"
        app:layout_goneMarginTop="@dimen/dp_116" />

</androidx.constraintlayout.widget.ConstraintLayout>