<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.home.HomeFragment">


    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/ll_tab_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_100"
        android:layout_marginStart="@dimen/dp_64"
        android:orientation="horizontal"
        app:layout_constraintBottom_toTopOf="@id/viewPager"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_search"
            android:layout_width="@dimen/dp_500"
            android:layout_height="@dimen/dp_80"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginEnd="@dimen/dp_80"
            android:includeFontPadding="false"
            android:background="@drawable/bg_home_search_title"
            android:drawableLeft="@mipmap/icon64_search"
            android:paddingLeft="@dimen/dp_12"
            android:gravity="center_vertical"
            android:contentDescription="@string/search_content_description"
            android:text="@string/home_search_title_content"
            android:textColor="@color/home_search_title"
            android:textSize="28sp" />

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayout"
            style="@style/CustomTabLayout"
            android:layout_width="0dp"
            android:layout_height="100dp"
            android:layout_gravity="start"
            android:layout_weight="1"
            app:tabGravity="start"
            app:tabMinWidth="@dimen/dp_80"
            app:tabMode="scrollable"
            android:paddingBottom="@dimen/dp_20"
            app:tabIndicatorAnimationDuration="160"
            app:tabIndicatorFullWidth="false"
            app:tabPaddingEnd="@dimen/dp_80"
            app:tabPaddingStart="@dimen/dp_80"
            app:tabRippleColor="@null" />

        <include
            layout="@layout/layout_app_logo"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_80"
            android:layout_marginEnd="@dimen/dp_64_5"
            android:layout_marginTop="@dimen/dp_10"
            app:layout_constraintRight_toRightOf="parent"/>

    </androidx.appcompat.widget.LinearLayoutCompat>


    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/dp_64"
        android:layout_marginTop="@dimen/dp_34"
        android:orientation="horizontal" />


</androidx.appcompat.widget.LinearLayoutCompat>