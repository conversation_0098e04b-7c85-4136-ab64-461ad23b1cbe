<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/dp_116"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/toast_day"
    android:gravity="center"
    android:orientation="horizontal"
    android:paddingHorizontal="@dimen/dp_40"
    android:paddingVertical="@dimen/dp_18"
    android:minWidth="@dimen/dp_304">

    <TextView
        android:id="@+id/toast_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        tools:visibility="visible"
        android:text=""
        android:textColor="@color/color_FAFCFF"
        android:textSize="@dimen/sp_32"/>


</LinearLayout>
