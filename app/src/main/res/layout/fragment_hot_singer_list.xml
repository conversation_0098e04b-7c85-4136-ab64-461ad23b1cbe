<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        style="@style/SmallCustomTabLayout"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_60"
        app:tabIndicatorAnimationDuration="0"
        android:layout_marginLeft="@dimen/dp_48">

    </com.google.android.material.tabs.TabLayout>

    <com.sgmw.common.widget.StateLayout
        android:id="@+id/stateLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toBottomOf="@id/tabLayout"
        app:errorIvTopMargin="@dimen/dp_176"
        app:loadingIvTopMargin="@dimen/dp_257"
        app:emptyIvTopMargin="@dimen/dp_255"
        app:showEmptyBtn="false">

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            style="@style/BaseRefreshStyle"
            android:layout_marginTop="@dimen/dp_28">

            <com.sgmw.common.widget.CustomRecyclerView
                android:id="@+id/recyclerview"
                android:overScrollMode="never"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

            </com.sgmw.common.widget.CustomRecyclerView>
            <com.sgmw.ksongs.widget.CustomClassicsFooter
                android:layout_width="match_parent"
                app:srlDrawableProgress="@mipmap/icon48_load_more"
                app:srlDrawableProgressSize="@dimen/dp_48"
                app:srlDrawableMarginRight="@dimen/dp_8"
                app:srlTextSizeTitle="@dimen/sp_24"
                app:srlAccentColor="@color/settings_text_color"
                android:layout_height="@dimen/dp_124"/>
        </com.scwang.smart.refresh.layout.SmartRefreshLayout>
    </com.sgmw.common.widget.StateLayout>



</LinearLayout>