<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/dp_72"
    android:layout_height="@dimen/dp_480"
    android:background="@drawable/bg_bright_setting">

    <ProgressBar
        android:id="@+id/pb_brightness"
        android:layout_width="@dimen/dp_8"
        android:layout_height="@dimen/dp_356"
        android:layout_marginTop="@dimen/dp_32"
        android:indeterminateOnly="false"
        android:max="100"
        android:progressDrawable="@drawable/progress_bar_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:progress="20" />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_20"
        android:src="@mipmap/icon_56_bright_setting"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/pb_brightness" />

</androidx.constraintlayout.widget.ConstraintLayout>