<?xml version="1.0" encoding="utf-8"?>
<com.sgmw.common.widget.StateLayout  xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/stateLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:loadingIvTopMargin="@dimen/dp_247"
    app:emptyIvTopMargin="@dimen/dp_166">

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/srl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        style="@style/BaseRefreshStyle">

        <com.scwang.smart.refresh.header.ClassicsHeader
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_124"/>

        <com.sgmw.common.widget.CustomRecyclerView
            android:id="@+id/rvTopicSongList"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            style="@style/recy_vertical_style"
            android:overScrollMode="never"
            android:background="@color/transparent"
            android:layout_marginHorizontal="@dimen/dp_64"
            android:layout_marginTop="@dimen/dp_24"/>


        <com.sgmw.ksongs.widget.CustomClassicsFooter
            android:layout_width="match_parent"
            app:srlDrawableProgress="@mipmap/icon48_load_more"
            app:srlDrawableProgressSize="@dimen/dp_48"
            app:srlDrawableMarginRight="@dimen/dp_8"
            app:srlTextSizeTitle="@dimen/sp_24"
            app:srlAccentColor="@color/settings_text_color"
            android:layout_height="@dimen/dp_124"/>

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

</com.sgmw.common.widget.StateLayout>