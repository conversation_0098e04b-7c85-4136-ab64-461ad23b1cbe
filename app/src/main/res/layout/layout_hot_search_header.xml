<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_history_search"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_40"
        android:fontFamily="sans-serif-medium"
        android:includeFontPadding="false"
        android:text="@string/search_history"
        android:textColor="@color/search_title_color"
        android:textSize="@dimen/sp_28"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_history_clear"
        android:layout_width="@dimen/dp_56"
        android:layout_height="@dimen/dp_56"
        android:background="@drawable/icon_search_history_clear"
        android:importantForAccessibility="no"
        app:layout_constraintBottom_toBottomOf="@+id/tv_history_search"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_history_search" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerview_history_search"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_17"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_history_search" />

    <View
        android:id="@+id/view_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_24"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/recyclerview_history_search" />

    <TextView
        android:id="@+id/tv_hot_search"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_40"
        android:fontFamily="sans-serif-medium"
        android:includeFontPadding="false"
        android:text="@string/hot_search"
        android:textColor="@color/search_title_color"
        android:textSize="@dimen/sp_28"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_divider" />

    <View
        android:id="@+id/view_divider2"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_20"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_hot_search" />

</androidx.constraintlayout.widget.ConstraintLayout>