<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="match_parent">
    <TextView
        android:id="@+id/tv_suggestion_singer"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_40"
        android:text="@string/singer"
        android:textSize="@dimen/sp_28"
        android:fontFamily="sans-serif-medium"
        android:textColor="@color/search_title_color"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent" />


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerview_suggestion_singers"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_140"
        android:layout_marginTop="@dimen/dp_24"
        app:layout_constraintTop_toBottomOf="@id/tv_suggestion_singer"
        />


    <TextView
        android:id="@+id/tv_suggestion_songs"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_40"
        android:layout_marginTop="@dimen/dp_24"
        android:fontFamily="sans-serif-medium"
        android:text="@string/song"
        android:textSize="@dimen/sp_28"
        android:textColor="@color/search_title_color"
        android:layout_marginBottom="@dimen/dp_24"
        app:layout_constraintTop_toBottomOf="@id/recyclerview_suggestion_singers"
        />

</androidx.constraintlayout.widget.ConstraintLayout>