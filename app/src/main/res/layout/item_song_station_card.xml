<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_category"
        android:layout_width="@dimen/dp_424"
        android:layout_height="@dimen/dp_180"
        android:background="@mipmap/bg_classification"
        android:contentDescription="@string/content_description_category"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:text="@string/home_card_classification"
        android:textColor="@color/home_card_item_color"
        android:textSize="32sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_record"
        android:layout_width="@dimen/dp_424"
        android:layout_height="@dimen/dp_180"
        android:layout_marginStart="@dimen/dp_32"
        android:background="@mipmap/bg_record"
        android:contentDescription="@string/content_description_category"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:text="@string/home_card_ksong_title"
        android:textColor="@color/home_card_item_color"
        android:textSize="32sp"
        app:layout_constraintLeft_toRightOf="@id/tv_category"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_collect"
        android:layout_width="@dimen/dp_424"
        android:layout_height="@dimen/dp_180"
        android:layout_marginStart="@dimen/dp_32"
        android:background="@mipmap/bg_collect"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:text="@string/home_card_title_collect"
        android:textColor="@color/home_card_item_color"
        android:textSize="32sp"
        app:layout_constraintLeft_toRightOf="@id/tv_record"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_vip"
        android:layout_width="@dimen/dp_424"
        android:layout_height="@dimen/dp_180"
        android:layout_marginStart="@dimen/dp_32"
        android:background="@mipmap/bg_vip_buy"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:text="@string/home_card_title_vip"
        android:textColor="@color/home_card_item_color"
        android:textSize="32sp"
        app:layout_constraintLeft_toRightOf="@id/tv_collect"
        app:layout_constraintTop_toTopOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>