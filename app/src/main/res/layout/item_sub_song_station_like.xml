<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_collect_music"
    android:paddingStart="11dp"
    android:layout_width="match_parent"
    android:layout_marginEnd="18dp"
    android:layout_height="148dp">
    <ImageView
        android:id="@+id/iv_line"
        android:layout_width="match_parent"
        android:layout_height="2dp"
        app:layout_constraintTop_toTopOf="parent"
        android:src="@mipmap/line_long_x"/>


    <TextView
        android:id="@+id/tv_media_no"
        android:layout_width="72dp"
        android:layout_height="0dp"
        android:layout_marginStart="20dp"
        android:gravity="center"
        android:textColor="@color/black"
        android:textSize="32sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="01" />

<!--    app:src_animation="@drawable/onlinemedia_frame_animation_now_playing"-->

    <com.sgmw.ksongs.widget.FrameAnimationView
        android:id="@+id/iv_is_playing"
        android:layout_width="72dp"
        android:layout_height="0dp"
        android:src="@drawable/ksong_playericon_status_00000"
        android:scaleType="centerInside"
        android:layout_marginStart="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="invisible"/>

    <ImageView
        android:id="@+id/iv_album"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginStart="34dp"
        android:src="@mipmap/no_album_player"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/tv_media_no"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/tv_media_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="#FF333333"
        android:textSize="32sp"
        app:layout_constraintStart_toEndOf="@id/iv_album"
        app:layout_constraintEnd_toStartOf="@id/iv_vip"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constrainedWidth="true"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        android:layout_marginStart="32dp"
        android:layout_marginTop="28dp"/>

    <ImageView
        android:id="@+id/iv_vip"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginStart="8dp"
        android:src="@mipmap/icon_48_media_label_qqmusic_vip"
        android:visibility="visible"
        android:scaleType="centerInside"
        app:layout_constraintStart_toEndOf="@id/tv_media_title"
        app:layout_constraintEnd_toStartOf="@id/barrier"
        app:layout_constraintTop_toTopOf="@id/tv_media_title"
        app:layout_constraintBottom_toBottomOf="@id/tv_media_title"/>
    <ImageView
        android:id="@+id/iv_quality"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:src="@mipmap/icon_48_media_label_aiquting_sq"
        app:layout_constraintStart_toEndOf="@id/iv_vip"
        app:layout_constraintTop_toTopOf="@id/tv_media_title"
        app:layout_constraintBottom_toBottomOf="@id/tv_media_title"
        android:layout_marginStart="@dimen/dp_4" />
    <ImageView
        android:id="@+id/iv_Score"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:src="@mipmap/icon_48_media_label_aiquting_sq"
        app:layout_constraintStart_toEndOf="@id/iv_quality"
        app:layout_constraintTop_toTopOf="@id/tv_media_title"
        app:layout_constraintBottom_toBottomOf="@id/tv_media_title"
        android:layout_marginStart="@dimen/dp_4" />

    <TextView
        android:id="@+id/tv_media_author"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#FF42484B"
        android:textSize="28sp"
        android:maxLines="1"
        android:ellipsize="end"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_album"
        app:layout_constraintEnd_toStartOf="@id/barrier"
        app:layout_constrainedWidth="true"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        android:layout_marginStart="32dp"
        android:layout_marginBottom="28dp"/>

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="start"
        app:constraint_referenced_ids="view_space"/>

    <View
        android:id="@+id/view_space"
        android:layout_width="30dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_add"/>

    <ImageView
        android:id="@+id/iv_add"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginEnd="30dp"
        android:gravity="center_vertical"
        android:src="@mipmap/icons_plus"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>