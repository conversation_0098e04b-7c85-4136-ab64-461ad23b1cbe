<?xml version="1.0" encoding="utf-8"?>
<com.sgmw.common.widget.StateLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/stateLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:showEmptyBtn="false"
    app:errorIvTopMargin="@dimen/dp_162"
    app:loadingIvTopMargin="@dimen/dp_243"
    app:emptyIvTopMargin="@dimen/dp_221">

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/refreshLayout_result"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        style="@style/BaseRefreshStyle">

        <com.sgmw.common.widget.CustomRecyclerView
            android:id="@+id/recyclerview_result"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

        </com.sgmw.common.widget.CustomRecyclerView>
        <com.sgmw.ksongs.widget.CustomClassicsFooter
            android:layout_width="match_parent"
            app:srlDrawableProgress="@mipmap/icon48_load_more"
            app:srlDrawableProgressSize="@dimen/dp_48"
            app:srlDrawableMarginRight="@dimen/dp_8"
            app:srlTextSizeTitle="@dimen/sp_24"
            app:srlAccentColor="@color/settings_text_color"
            android:layout_height="@dimen/dp_124"/>

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

</com.sgmw.common.widget.StateLayout>