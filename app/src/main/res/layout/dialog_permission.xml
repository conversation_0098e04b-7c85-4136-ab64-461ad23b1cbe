<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/bg_shadow"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/dialog_mask" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/dialog_content"
        android:layout_width="@dimen/dp_1160"
        android:layout_height="@dimen/dp_660"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_165"
        android:background="@drawable/bg_dialog"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_20"
            android:background="@mipmap/icon64_close"
            android:contentDescription="@string/back_content_description"
            android:importantForAccessibility="no"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="sans-serif-medium"
            android:includeFontPadding="false"
            android:text="@string/permission_title"
            android:textColor="@color/normal_text_color"
            android:textSize="@dimen/sp_32"
            app:layout_constraintBottom_toBottomOf="@+id/iv_back"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/iv_back" />

        <TextView
            android:id="@+id/tv_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_151"
            android:includeFontPadding="false"
            android:text="@string/permission_content"
            android:textColor="@color/normal_text_color"
            android:textSize="@dimen/sp_28"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_back" />

        <com.sgmw.ksongs.widget.NewsRadioGroup
            android:id="@+id/rg_permission"
            android:layout_width="@dimen/dp_1000"
            android:layout_height="@dimen/dp_72"
            android:layout_gravity="center_vertical"
            android:layout_marginTop="@dimen/dp_42"
            android:background="@drawable/bg_radiogroup"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_message">

            <RadioButton
                android:id="@+id/rb_close"
                style="@style/CustomRadioButton"
                android:layout_width="@dimen/dp_242"
                android:layout_height="@dimen/dp_64"
                android:layout_marginHorizontal="@dimen/dp_4"
                android:checked="true"
                android:contentDescription="@string/permission_close"
                android:fontFamily="sans-serif-medium"
                android:includeFontPadding="false"
                android:text="@string/permission_close" />

            <RadioButton
                android:id="@+id/rb_once"
                style="@style/CustomRadioButton"
                android:layout_width="@dimen/dp_242"
                android:layout_height="@dimen/dp_64"
                android:layout_marginHorizontal="@dimen/dp_4"
                android:checked="false"
                android:contentDescription="@string/permission_once"
                android:includeFontPadding="false"
                android:text="@string/permission_once" />

            <RadioButton
                android:id="@+id/rb_three_month"
                style="@style/CustomRadioButton"
                android:layout_width="@dimen/dp_242"
                android:layout_height="@dimen/dp_64"
                android:layout_marginHorizontal="@dimen/dp_4"
                android:checked="false"
                android:contentDescription="@string/permission_three_month"
                android:includeFontPadding="false"
                android:text="@string/permission_three_month" />

            <RadioButton
                android:id="@+id/rb_one_year"
                style="@style/CustomRadioButton"
                android:layout_width="@dimen/dp_242"
                android:layout_height="@dimen/dp_64"
                android:layout_marginHorizontal="@dimen/dp_4"
                android:checked="false"
                android:contentDescription="@string/permission_one_year"
                android:includeFontPadding="false"
                android:text="@string/permission_one_year" />

        </com.sgmw.ksongs.widget.NewsRadioGroup>

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="@dimen/dp_180"
            android:layout_height="@dimen/dp_72"
            android:layout_marginStart="@dimen/dp_382"
            android:layout_marginTop="@dimen/dp_165"
            android:background="@drawable/selector_btn_bg_rect_20_1c7dff"
            android:contentDescription="@string/permission_confirm_button"
            android:gravity="center"
            android:text="@string/permission_confirm_button"
            android:textColor="@drawable/selector_btn_color_ffffff"
            android:textSize="@dimen/sp_26"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/rg_permission" />

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="@dimen/dp_180"
            android:layout_height="@dimen/dp_72"
            android:layout_marginEnd="@dimen/dp_382"
            android:background="@drawable/selector_btn_bg_rect_20_b3cdd5de_66909ead"
            android:contentDescription="@string/permission_confirm_cancel"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:text="@string/permission_confirm_cancel"
            android:textColor="@drawable/selector_btn_color_262e33_ffffff"
            android:textSize="@dimen/sp_26"
            app:layout_constraintBottom_toBottomOf="@+id/tv_confirm"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_confirm" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</FrameLayout>