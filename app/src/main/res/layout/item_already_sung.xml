<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_120"
    android:background="@drawable/selector_item_song_bg">

    <TextView
        android:id="@+id/tvNum"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:ellipsize="middle"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@drawable/selector_item_song_index_color"
        android:textSize="@dimen/sp_28"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/ivPlayStatus"
        app:layout_constraintStart_toStartOf="@+id/ivPlayStatus"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="1" />

    <ImageView
        android:id="@+id/ivPlayStatus"
        android:layout_width="@dimen/dp_80"
        android:layout_height="@dimen/dp_80"
        android:layout_marginStart="@dimen/dp_12"
        android:scaleType="centerInside"
        android:src="@mipmap/icon_playing_80"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivCover"
        android:layout_width="@dimen/dp_88"
        android:layout_height="@dimen/dp_88"
        android:layout_marginStart="@dimen/dp_12"
        android:src="@mipmap/no_album_player"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivPlayStatus"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/tvSongName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_24"
        android:layout_marginTop="@dimen/dp_2"
        android:ellipsize="end"
        android:fontFamily="sans-serif-medium"
        android:maxLines="1"
        android:textColor="@drawable/selector_item_song_name_color"
        android:textSize="@dimen/sp_28"
        app:layout_constrainedWidth="true"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/ivCover"
        app:layout_constraintTop_toTopOf="@+id/ivCover"
        tools:text="歌曲名称" />

    <View
        android:id="@+id/guidLine"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        android:layout_marginStart="@dimen/dp_20"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivCover"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivVip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:layout_marginBottom="@dimen/dp_13"
        android:scaleType="centerInside"
        android:src="@mipmap/icon_vip_tag"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@id/ivCover"
        app:layout_constraintStart_toEndOf="@id/guidLine"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivMv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:layout_marginBottom="@dimen/dp_13"
        android:scaleType="centerInside"
        android:src="@mipmap/icon_mv_tag"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/ivCover"
        app:layout_constraintStart_toEndOf="@id/ivVip"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivScore"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:layout_marginBottom="@dimen/dp_13"
        android:scaleType="centerInside"
        android:src="@mipmap/icon_score_tag"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/ivCover"
        app:layout_constraintStart_toEndOf="@id/ivMv"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvSongAuthor"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_36"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginBottom="@dimen/dp_5"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@drawable/selector_item_song_author_color"
        android:textSize="@dimen/sp_24"
        app:layout_constraintBottom_toBottomOf="@id/ivCover"
        app:layout_constraintStart_toEndOf="@id/ivScore"
        tools:text="作者" />


    <ImageView
        android:id="@+id/ivAdd"
        android:layout_width="@dimen/dp_56"
        android:layout_height="@dimen/dp_56"
        android:layout_marginEnd="@dimen/dp_40"
        android:contentDescription="@string/add_content_description"
        android:gravity="center_vertical"
        android:src="@drawable/selector_icon_add_toggle"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>