<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/dp_56"
        android:layout_height="@dimen/dp_56"
        android:layout_marginStart="@dimen/dp_64"
        android:contentDescription="@string/back_content_description"
        android:src="@drawable/selector_icon_56_back"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvTitle" />

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_100"
        android:layout_marginStart="@dimen/dp_14"
        android:fontFamily="sans-serif-medium"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:lineSpacingExtra="@dimen/dp_48"
        android:text="@string/collect"
        android:textColor="@color/title_color"
        android:textSize="@dimen/sp_32"
        app:layout_constraintLeft_toRightOf="@id/iv_back"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvSelectAll"
        android:layout_width="@dimen/dp_184"
        android:layout_height="@dimen/dp_52"
        android:layout_marginEnd="@dimen/dp_32"
        android:background="@drawable/selector_btn_bg_rect_12_1c7dff"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/select_all"
        android:textColor="@drawable/selector_btn_color_ffffff"
        android:textSize="@dimen/sp_24"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
        app:layout_constraintRight_toLeftOf="@+id/tvDelete"
        app:layout_constraintTop_toTopOf="@+id/tvTitle" />

    <TextView
        android:id="@+id/tvDelete"
        android:layout_width="@dimen/dp_184"
        android:layout_height="@dimen/dp_52"
        android:layout_marginEnd="@dimen/dp_32"
        android:background="@drawable/selector_btn_bg_rect_12_f76666"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/delete"
        android:textColor="@drawable/selector_btn_color_ffffff"
        android:textSize="@dimen/sp_24"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
        app:layout_constraintRight_toLeftOf="@+id/tvCancel"
        app:layout_constraintTop_toTopOf="@+id/tvTitle" />

    <TextView
        android:id="@+id/tvCancel"
        android:layout_width="@dimen/dp_184"
        android:layout_height="@dimen/dp_52"
        android:layout_marginEnd="@dimen/dp_32"
        android:background="@drawable/selector_btn_bg_rect_12_b3cdd5de_66909ead"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/cancel"
        android:textColor="@drawable/selector_btn_color_262e33_ffffff"
        android:textSize="@dimen/sp_24"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
        app:layout_constraintRight_toLeftOf="@+id/layout_icon"
        app:layout_constraintTop_toTopOf="@+id/tvTitle" />

    <TextView
        android:id="@+id/tvEdit"
        android:layout_width="@dimen/dp_184"
        android:layout_height="@dimen/dp_52"
        android:layout_marginEnd="@dimen/dp_32"
        android:background="@drawable/selector_btn_bg_rect_12_b3cdd5de_66909ead"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/edit"
        android:textColor="@drawable/selector_btn_color_262e33_ffffff"
        android:textSize="@dimen/sp_24"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
        app:layout_constraintRight_toLeftOf="@+id/layout_icon"
        app:layout_constraintTop_toTopOf="@+id/tvTitle" />

    <include
        android:id="@+id/layout_icon"
        layout="@layout/layout_app_logo"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_80"
        android:layout_marginEnd="@dimen/dp_64_5"
        app:layout_constraintBottom_toBottomOf="@+id/iv_back"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iv_back" />

    <com.sgmw.common.widget.StateLayout
        android:id="@+id/stateLayout"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        app:emptyIvTopMargin="@dimen/dp_176"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        app:loadingIvTopMargin="@dimen/dp_257">

        <com.sgmw.common.widget.CustomRecyclerView
            android:id="@+id/rvCollect"
            style="@style/recy_vertical_style"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/dp_64"
            android:layout_marginTop="@dimen/dp_24"
            android:background="@color/transparent"
            android:overScrollMode="never" />

    </com.sgmw.common.widget.StateLayout>

</androidx.constraintlayout.widget.ConstraintLayout>