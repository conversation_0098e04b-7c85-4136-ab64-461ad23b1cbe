<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tv_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:text="@string/home_title_hot_topics"
        android:textSize="@dimen/sp_28"
        android:fontFamily="sans-serif-medium"
        android:textColor="@color/normal_text_color"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_40"/>

    <ImageView
        android:id="@+id/iv_icon"
        app:layout_constraintLeft_toRightOf="@id/tv_title"
        app:layout_constraintTop_toTopOf="parent"
        android:src="@drawable/icon_genreal_more"
        android:layout_width="@dimen/dp_40"
        android:layout_height="@dimen/dp_40"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_topics"
        android:layout_marginTop="@dimen/dp_24"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>


</androidx.constraintlayout.widget.ConstraintLayout>