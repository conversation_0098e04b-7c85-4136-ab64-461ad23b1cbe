<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="ResourceName,SpUsage" xmlns:app="http://schemas.android.com/apk/res-auto">

    <style name="recy_vertical_style">
        <item name="android:scrollbars">none</item>
<!--        <item name="android:scrollbarSize">8dp</item>-->
<!--        <item name="android:scrollbarThumbVertical">@color/scrollbar_thumb_color</item>-->
    </style>

    <style name="BaseRefreshStyle" parent="SmartRefreshStyle">
        <item name="srlEnableRefresh">false</item>
        <item name="srlEnableLoadMore">true</item>
        <item name="srlDisableContentWhenLoading">true</item>
        <item name="srlDisableContentWhenRefresh">true</item>
    </style>

    <style name="SmallTabLayoutTextStyle"  parent="TextAppearance.Design.Tab">
        <item name="android:textSize">@dimen/sp_28</item>
    </style>

    <style name="TabLayoutTextStyle" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">@dimen/sp_32</item>
    </style>

    <style name="BigCustomTabLayout" parent="CustomTabLayout">
        <item name="tabPaddingStart">@dimen/dp_80</item>
        <item name="tabPaddingEnd">@dimen/dp_80</item>
    </style>

    <style name="SmallCustomTabLayout" parent="CustomTabLayout">
        <item name="tabPaddingStart">@dimen/dp_40</item>
        <item name="tabPaddingEnd">@dimen/dp_40</item>
        <item name="tabTextAppearance">@style/SmallTabLayoutTextStyle</item>
    </style>


    <style name="CustomTabLayout" parent="Widget.Design.TabLayout">
        <item name="tabGravity">start</item>
        <item name="tabIndicatorHeight">@dimen/dp_18</item>
        <item name="tabIndicator">@drawable/tab_indicator</item>
        <item name="tabIndicatorColor">@color/c_1990FF</item>
        <item name="tabIndicatorFullWidth">false</item>
        <item name="tabMode">fixed</item>
        <item name="tabRippleColor">@null</item>
        <item name="tabBackground">@drawable/selector_tab_bg</item>
        <item name="tabTextAppearance">@style/TabLayoutTextStyle</item>
        <item name="tabSelectedTextColor">@color/tab_text_color_selected</item>
        <item name="tabTextColor">@color/tab_text_color_normal</item>
        <item name="tabPaddingTop">@dimen/dp_0</item>
        <item name="tabPaddingBottom">@dimen/dp_2</item>
        <item name="tabMinWidth">@dimen/dp_128</item>
        <item name="tabMaxWidth">@dimen/dp_288</item>
    </style>

    <style name="CustomRadioButton" parent="Widget.AppCompat.CompoundButton.RadioButton">
        <item name="android:button">@null</item>
        <item name="android:background">@drawable/selector_bg_radiobutton</item>
        <item name="android:textColor">@drawable/selector_radiobutton_text_color</item>
        <item name="android:textSize">@dimen/sp_24</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="WhiteButton" parent="Widget.AppCompat.Button">
        <item name="android:textSize">@dimen/sp_26</item>
        <item name="android:textColor">@drawable/selector_white_button_text_color</item>
        <item name="android:background">@drawable/selector_bg_white_btn</item>
        <item name="android:stateListAnimator">@null</item>
    </style>

    <style name="GrayButton" parent="Widget.AppCompat.Button">
        <item name="android:textSize">@dimen/sp_26</item>
        <item name="android:textColor">@drawable/selector_gray_button_text_color</item>
        <item name="android:background">@drawable/selector_bg_gray_btn</item>
        <item name="android:stateListAnimator">@null</item>
    </style>

    <style name="BlueButton" parent="Widget.AppCompat.Button">
        <item name="android:textSize">@dimen/sp_32</item>
        <item name="android:textColor">@drawable/selector_blue_button_text_color</item>
        <item name="android:background">@drawable/selector_btn_bg_rect_20_1c7dff</item>
        <item name="android:stateListAnimator">@null</item>
    </style>

    <style name="TitleStyle" parent="Widget.AppCompat.TextView">
        <item name="android:textSize">@dimen/sp_32</item>
        <item name="android:textColor">@color/normal_text_color</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:layout_weight">1</item>
        <item name="android:layout_marginLeft">@dimen/dp_14</item>
    </style>
</resources>