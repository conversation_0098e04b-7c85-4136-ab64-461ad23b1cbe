<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="ResourceName">
    <declare-styleable name="FrameAnimationView">
        <attr name="src_animation" format="reference" />
    </declare-styleable>

    <declare-styleable name="CircularProgressButton">
        <attr name="backgroundWidth" format="dimension" />
        <attr name="progressWidth" format="dimension"/>
    </declare-styleable>

    <declare-styleable name="LyricDotView">
        <attr name="dotColor" format="color"/>
    </declare-styleable>

    <declare-styleable name="PlaySettingItem">
        <attr name="itemIcon" format="reference"/>
        <attr name="itemText" format="string"/>
        <attr name="iconWidth" format="dimension"/>
        <attr name="iconHeight" format="dimension"/>
        <attr name="iconMarginTop" format="dimension"/>
    </declare-styleable>

    <declare-styleable name="PureVerticalSeekBar">
        <attr name="circle_color" format="color" />
        <attr name="vertical_color_start" format="color" />
        <attr name="vertical_color_end" format="color" />
        <attr name="dragable" format="boolean" />
        <attr name="image_background" format="reference" />
    </declare-styleable>


    <declare-styleable name="StateLayout">
        <attr name="loadingResId" format="reference" />
        <attr name="emptyResId" format="reference" />
        <attr name="errorResId" format="reference" />
        <attr name="noNetWorkResId" format="reference" />
        <attr name="defaultShowLoading" format="boolean" />
        <attr name="loadingIvTopMargin" format="dimension" />
        <attr name="emptyIvTopMargin" format="dimension" />
        <attr name="errorIvTopMargin" format="dimension" />
        <attr name="emptyDataHintTxt" format="reference" />
        <attr name="showEmptyBtn" format="boolean" />

    </declare-styleable>



</resources>