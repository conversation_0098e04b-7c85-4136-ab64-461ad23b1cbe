<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mobile_navigation"
    app:startDestination="@+id/navigation_home">

    <action
        android:id="@+id/action_to_login"
        app:launchSingleTop="true"
        app:destination="@id/navigation_login" />

    <fix_fragment
        android:id="@+id/navigation_home"
        android:name="com.sgmw.ksongs.ui.home.HomeFragment"
        android:label="@string/title_home"
        tools:layout="@layout/fragment_home">

        <action
            android:id="@+id/action_home_to_search"
            app:destination="@id/navigation_search" />

        <action
            android:id="@+id/action_home_to_settings"
            app:destination="@+id/navigation_settings" />

        <action
            android:id="@+id/action_home_to_collect"
            app:destination="@id/navigation_collect" />

        <action
            android:id="@+id/action_home_to_play_record"
            app:destination="@id/navigation_play_record" />

        <action
            android:id="@+id/action_home_to_rank"
            app:destination="@id/navigation_rank" />

        <action
            android:id="@+id/action_home_to_hot_singer_list"
            app:destination="@+id/navigation_hot_singer_list" />

        <action
            android:id="@+id/action_home_to_song_by_singer"
            app:destination="@+id/navigation_fragment_song_list_by_singer" />

        <action
            android:id="@+id/action_home_to_user_feature_song"
            app:destination="@id/navigation_user_feature_song" />

        <action
            android:id="@+id/action_home_to_hot_topic"
            app:destination="@id/navigation_hot_topic_home" />

        <action
            android:id="@+id/action_home_to_category"
            app:destination="@id/navigation_category_home" />

        <action android:id="@+id/action_home_to_vip_payment"
            app:destination="@+id/navigation_vip_payment" />


    </fix_fragment>

    <fix_fragment
        android:id="@+id/navigation_search"
        android:name="com.sgmw.ksongs.ui.search.SearchHomeFragment"
        android:label="@string/title_search"
        tools:layout="@layout/fragment_search_home">

        <action
            android:id="@+id/action_search_to_song_by_singer"
            app:destination="@+id/navigation_fragment_song_list_by_singer" />

    </fix_fragment>

    <fix_fragment
        android:label="Login"
        android:id="@+id/navigation_login"
        android:name="com.sgmw.ksongs.ui.login.LoginFragment"
        tools:layout="@layout/fragment_login" >
        <action
            android:id="@+id/action_login_to_protocol"
            app:destination="@+id/navigation_fragment_protocol"/>
        <action
            android:id="@+id/action_login_to_main"
            app:destination="@+id/navigation_home" />
    </fix_fragment>

    <fix_fragment
        android:id="@+id/navigation_agree_privacy"
        android:name="com.sgmw.ksongs.ui.privacy.AgreePrivacyFragment"
        tools:layout="@layout/fragment_agree_privacy" >
        <action
            android:id="@+id/action_privacy_to_protocol"
            app:destination="@+id/navigation_fragment_protocol"/>
        <action
            android:id="@+id/action_privacy_to_login"
            app:destination="@+id/navigation_login"/>
    </fix_fragment>

    <fix_fragment
        android:id="@+id/navigation_settings"
        android:name="com.sgmw.ksongs.ui.settings.SettingsFragment"
        android:label="@string/title_settings"
        tools:layout="@layout/fragment_settings">
        <action
            android:id="@+id/action_settings_to_mic_connection_guide"
            app:destination="@+id/navigation_mic_connection_guide" />

        <action
            android:id="@+id/action_settings_to_protocol"
            app:destination="@+id/navigation_fragment_protocol"/>

        <action android:id="@+id/action_settings_to_vip_payment"
            app:destination="@+id/navigation_vip_payment" />

    </fix_fragment>

    <fix_fragment
        android:id="@+id/navigation_mic_connection_guide"
        android:name="com.sgmw.ksongs.ui.settings.MicConnectionGuideFragment"
        android:label="@string/settings_mic_connecttion_guide"
        tools:layout="@layout/fragment_mic_connection_guide">
    </fix_fragment>

    <fix_fragment
        android:id="@+id/navigation_collect"
        android:name="com.sgmw.ksongs.ui.collect.CollectFragment"
        android:label="@string/collect"
        tools:layout="@layout/fragment_collect" >

        <action
            android:id="@+id/action_collect_to_search"
            app:destination="@id/navigation_search" />

    </fix_fragment>

    <fix_fragment
        android:id="@+id/navigation_play_record"
        android:name="com.sgmw.ksongs.ui.record.PlayRecordFragment"
        android:label="@string/play_record"
        tools:layout="@layout/fragment_play_record" >


        <action
            android:id="@+id/action_record_to_search"
            app:destination="@id/navigation_search" />

    </fix_fragment>

    <fix_fragment
        android:id="@+id/navigation_rank"
        android:name="com.sgmw.ksongs.ui.ranklist.RankHomeFragment"
        android:label="@string/rank"
        tools:layout="@layout/fragment_rank_home" />

    <fix_fragment
        android:id="@+id/navigation_hot_singer_list"
        android:name="com.sgmw.ksongs.ui.singerlist.HotSingerListTabFragment"
        android:label="@string/title_hot_singer"
        tools:layout="@layout/fragment_hot_singer_list_tab">

        <action android:id="@+id/action_hot_singer_list_to_song_list_by_singer"
            app:destination="@+id/navigation_fragment_song_list_by_singer"
            />

    </fix_fragment>

    <fix_fragment
        android:id="@+id/navigation_fragment_song_list_by_singer"
        android:name="com.sgmw.ksongs.ui.singerlist.SongListBySingerFragment"
        tools:layout="@layout/fragment_song_list_by_singer">
    </fix_fragment>

    <fix_fragment
        android:id="@+id/navigation_fragment_protocol"
        android:name="com.sgmw.ksongs.ui.protocol.ProtocolHomeFragment"
        android:label="@string/service_term"
        tools:layout="@layout/fragment_protocol_home">
    </fix_fragment>

    <fix_fragment
        android:id="@+id/navigation_user_feature_song"
        android:name="com.sgmw.ksongs.ui.user.feature.UserFeatureSongFragment"
        android:label="@string/user_feature_song_like"
        tools:layout="@layout/fragment_user_feature_song" />

    <fix_fragment
        android:id="@+id/navigation_hot_topic_home"
        android:name="com.sgmw.ksongs.ui.hottopic.HotTopicHomeFragment"
        android:label="@string/title_hot_topics"
        tools:layout="@layout/fragment_hot_topic_home" />

    <fix_fragment
        android:id="@+id/navigation_category_home"
        android:name="com.sgmw.ksongs.ui.category.CategoryHomeFragment"
        android:label="@string/title_category"
        tools:layout="@layout/fragment_category_home" >

        <action
            android:id="@+id/action_to_category_song_list"
            app:destination="@+id/navigation_category_song_list"/>

    </fix_fragment>

    <fix_fragment
        android:id="@+id/navigation_category_song_list"
        android:name="com.sgmw.ksongs.ui.category.CategorySongListFragment"
        android:label="@string/title_category"
        tools:layout="@layout/fragment_category_song_list" />

    <fix_fragment android:id="@+id/navigation_vip_payment"
        android:name="com.sgmw.ksongs.ui.vip.VipPaymentFragment"
        tools:layout="@layout/fragment_vip_payment" />

</navigation>