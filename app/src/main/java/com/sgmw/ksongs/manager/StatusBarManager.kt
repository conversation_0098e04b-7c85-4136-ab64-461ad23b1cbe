package com.sgmw.ksongs.manager

import android.graphics.Color
import android.os.Build
import android.view.View
import android.view.WindowManager
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.MainActivity
import com.sgmw.ksongs.databinding.ActivityMainBinding

/**
 * @author: 董俊帅
 * @time: 2025/4/24
 * @desc: 状态栏管理类
 */
object StatusBarManager {

    private const val TAG = "StatusBarManager"

    /**
     * 状态栏是否隐藏
     */
    var currentStatusBarIsHide = false

    /**
     * 设置状态栏展示状态
     */
    fun setUpStatusBar(isPlayViewShow: Boolean, context: MainActivity, mMainBinding: ActivityMainBinding) {
        Log.d(TAG, "setUpStatusBar isPlayViewShow: $isPlayViewShow")
        if (isPlayViewShow) {
            hideStatusBar(context, mMainBinding)
        } else {
            transparentNavBar(context, mMainBinding)
        }
    }

    /**
     * 隐藏状态栏，全屏
     */
    private fun hideStatusBar(context: MainActivity, mMainBinding: ActivityMainBinding) {
        Log.d(TAG, "hideStatusBar: $currentStatusBarIsHide")
        currentStatusBarIsHide = true
        context.window.decorView.apply {
            val uiOption =
                View.SYSTEM_UI_FLAG_FULLSCREEN or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            systemUiVisibility = uiOption
        }
        mMainBinding.topBarView.visibility = View.GONE
        mMainBinding.bottomBarView.visibility = View.GONE
    }

    /**
     * 不是全屏，状态栏透明
     */
    private fun transparentNavBar(context: MainActivity, mMainBinding: ActivityMainBinding) {
        Log.d(TAG, "transparentNavBar: $currentStatusBarIsHide")
        currentStatusBarIsHide = false
        context.window.clearFlags(
            WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS
                    or WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION
        )
        context.window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
        context.window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            context.window.isNavigationBarContrastEnforced = false
        }
        context.window.statusBarColor = Color.TRANSPARENT
        context.window.navigationBarColor = Color.TRANSPARENT

        mMainBinding.topBarView.visibility = View.VISIBLE
        mMainBinding.bottomBarView.visibility = View.VISIBLE
    }

}