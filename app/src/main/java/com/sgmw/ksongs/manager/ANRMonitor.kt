package com.sgmw.ksongs.manager

import android.os.Handler
import android.os.Looper
import android.os.SystemClock
import android.util.Log
import com.sgmw.ksongs.BuildConfig
import kotlinx.coroutines.*
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong

/**
 * @author: 董俊帅
 * @time: 2025/8/2
 * @desc: ANR监控工具,用于检测主线程阻塞，预防ANR问题
 * 优化版本：减少主线程阻塞，提高检测精度
 */
object ANRMonitor {

    private const val TAG = "ANRMonitor"
    private const val DEFAULT_ANR_TIMEOUT = 5000L // 5秒ANR超时
    private const val CHECK_INTERVAL = 1000L // 检查间隔1秒（降低频率）
    private const val WARNING_THRESHOLD = 200L // 警告阈值200ms（提高阈值）
    
    private val isMonitoring = AtomicBoolean(false)
    private val lastCheckTime = AtomicLong(0)
    private val mainHandler = Handler(Looper.getMainLooper())
    private var monitorRunnable: Runnable? = null
    private var backgroundScope: CoroutineScope? = null

    // 用于减少重复报告的计数器
    private var consecutiveWarnings = 0
    private var lastReportTime = 0L
    
    /**
     * 开始ANR监控
     */
    fun startMonitoring() {
        if (!BuildConfig.DEBUG) return // 只在Debug模式下启用

        if (isMonitoring.compareAndSet(false, true)) {
            Log.d(TAG, "开始ANR监控")
            backgroundScope = CoroutineScope(Dispatchers.Default + SupervisorJob())
            scheduleNextCheck()
        }
    }

    /**
     * 停止ANR监控
     */
    fun stopMonitoring() {
        if (isMonitoring.compareAndSet(true, false)) {
            Log.d(TAG, "停止ANR监控")
            monitorRunnable?.let { mainHandler.removeCallbacks(it) }
            backgroundScope?.cancel()
            backgroundScope = null
        }
    }
    
    /**
     * 安排下一次检查
     */
    private fun scheduleNextCheck() {
        if (!isMonitoring.get()) return
        
        monitorRunnable = Runnable {
            checkMainThreadBlocking()
            scheduleNextCheck()
        }
        
        // 记录当前时间
        lastCheckTime.set(SystemClock.uptimeMillis())
        
        // 延迟执行检查
        mainHandler.postDelayed(monitorRunnable!!, CHECK_INTERVAL)
    }
    
    /**
     * 检查主线程阻塞
     */
    private fun checkMainThreadBlocking() {
        val currentTime = SystemClock.uptimeMillis()
        val lastTime = lastCheckTime.get()
        val actualInterval = currentTime - lastTime

        // 如果实际间隔远大于预期间隔，说明主线程被阻塞
        val blockingTime = actualInterval - CHECK_INTERVAL

        when {
            blockingTime > DEFAULT_ANR_TIMEOUT -> {
                // 严重阻塞，可能导致ANR
                Log.e(TAG, "检测到严重主线程阻塞: ${blockingTime}ms，可能导致ANR")
                reportANRRiskAsync(blockingTime, "CRITICAL")
                consecutiveWarnings = 0 // 重置计数器
            }
            blockingTime > WARNING_THRESHOLD -> {
                // 轻微阻塞，发出警告（但要限制频率）
                val now = System.currentTimeMillis()
                if (now - lastReportTime > 5000) { // 5秒内最多报告一次
                    Log.w(TAG, "检测到主线程阻塞: ${blockingTime}ms")
                    reportANRRiskAsync(blockingTime, "WARNING")
                    lastReportTime = now
                    consecutiveWarnings++
                } else {
                    consecutiveWarnings++
                }
            }
            else -> {
                consecutiveWarnings = 0 // 重置计数器
            }
        }

        lastCheckTime.set(currentTime)
    }
    
    /**
     * 异步报告ANR风险（避免在主线程执行耗时操作）
     */
    private fun reportANRRiskAsync(blockingTime: Long, level: String) {
        backgroundScope?.launch {
            try {
                // 在后台线程获取堆栈信息，避免阻塞主线程
                val stackTrace = Thread.currentThread().stackTrace
                val stackInfo = stackTrace.take(8).joinToString("\n") {
                    "    at ${it.className}.${it.methodName}(${it.fileName}:${it.lineNumber})"
                }

                // 简化日志输出，减少主线程阻塞
                withContext(Dispatchers.Main) {
                    if (level == "CRITICAL") {
                        Log.e(TAG, "ANR风险[$level]: ${blockingTime}ms - 连续警告:$consecutiveWarnings")
                    } else {
                        Log.w(TAG, "主线程阻塞[$level]: ${blockingTime}ms")
                    }
                }

                // 详细信息在后台线程记录
                Log.d(TAG, """
                    ANR详细报告:
                    级别: $level
                    阻塞时间: ${blockingTime}ms
                    连续警告次数: $consecutiveWarnings
                    线程: ${Thread.currentThread().name}
                    堆栈信息:
                    $stackInfo
                """.trimIndent())

                // 在生产环境中，这里可以上报到崩溃收集平台
                // 例如: Bugly.postCatchedException(ANRException(blockingTime, stackInfo))
            } catch (e: Exception) {
                Log.e(TAG, "报告ANR风险时发生异常", e)
            }
        }
    }
    
    /**
     * 检查当前是否在主线程
     */
    fun isMainThread(): Boolean {
        return Looper.myLooper() == Looper.getMainLooper()
    }
    
    /**
     * 确保在主线程执行
     */
    fun ensureMainThread(action: () -> Unit) {
        if (isMainThread()) {
            action()
        } else {
            mainHandler.post { action() }
        }
    }
    
    /**
     * 测量代码块执行时间（优化版本）
     */
    fun <T> measureTime(tag: String, block: () -> T): T {
        val startTime = SystemClock.uptimeMillis()
        val result = block()
        val duration = SystemClock.uptimeMillis() - startTime

        // 只在超过阈值时记录，并使用异步方式避免阻塞
        if (duration > WARNING_THRESHOLD) {
            backgroundScope?.launch {
                Log.w(TAG, "耗时操作检测 [$tag]: ${duration}ms")
            } ?: Log.w(TAG, "耗时操作检测 [$tag]: ${duration}ms")
        }

        return result
    }
    
    /**
     * 检查方法是否在主线程执行耗时操作（优化版本）
     */
    fun checkMainThreadOperation(methodName: String) {
        if (isMainThread()) {
            // 使用异步方式记录警告，避免阻塞主线程
            backgroundScope?.launch {
                Log.w(TAG, "警告: 方法 [$methodName] 在主线程执行，可能导致ANR")
            } ?: Log.w(TAG, "警告: 方法 [$methodName] 在主线程执行，可能导致ANR")
        }
    }

    /**
     * 获取当前监控状态
     */
    fun getMonitoringStatus(): String {
        return if (isMonitoring.get()) {
            "监控中 - 连续警告:$consecutiveWarnings"
        } else {
            "未监控"
        }
    }
}

/**
 * ANR异常类
 */
class ANRException(
    private val blockingTime: Long,
    private val stackInfo: String
) : Exception("ANR detected: blocked for ${blockingTime}ms\n$stackInfo")
