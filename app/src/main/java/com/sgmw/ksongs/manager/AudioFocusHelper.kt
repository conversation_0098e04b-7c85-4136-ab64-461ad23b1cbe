package com.sgmw.ksongs.manager

import android.content.Context
import android.media.AudioAttributes
import android.media.AudioDeviceInfo
import android.media.AudioFocusRequest
import android.media.AudioManager
import android.text.TextUtils
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.ui.songplay.KaraokePlayerManager

/**
 * @author: 董俊帅
 * @time: 2025/3/19
 * @desc: 音频焦点管理类
 */

object AudioFocusHelper {

    private const val TAG = "AudioFocusHelper"

    private var audioManager: AudioManager? = null
    private var audioFocusRequest: AudioFocusRequest? = null
    private var isAudioFocused = false

    private val focusChangeListener = AudioManager.OnAudioFocusChangeListener { focusChange ->
        Log.d(TAG, "焦点发生变化 focusChange: $focusChange")
        when (focusChange) {
            AudioManager.AUDIOFOCUS_GAIN -> {
                isAudioFocused = true
                CarManager.requestWheelKeyMode()
            }

            AudioManager.AUDIOFOCUS_LOSS -> {
                onLostAudioFocus()
            }

            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> {
                onLostAudioFocus()
            }

            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK -> {
                onLostAudioFocus()
            }

            AudioManager.AUDIOFOCUS_REQUEST_DELAYED -> {
                onLostAudioFocus()
            }

            else -> {
                onLostAudioFocus()
            }
        }
    }

    /**
     * 初始化 AudioManager
     */
    fun init(context: Context) {
        audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
    }

    /**
     * 申请音频焦点（避免重复申请）
     */
    fun requestAudioFocus(): Boolean {
        if (isAudioFocused) {
            Log.d(TAG, "已有焦点，不再重复申请")
            return true
        }

        if (audioManager == null) {
            Log.d(TAG, "AudioManager is null.")
            return false
        }

        val audioAttributes = AudioAttributes.Builder()
            .setUsage(AudioAttributes.USAGE_MEDIA) // 用于播放媒体音频
            .build()

        audioFocusRequest = AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN)
            .setAudioAttributes(audioAttributes)
            .setOnAudioFocusChangeListener(focusChangeListener)
            .build()

        val result = audioManager?.requestAudioFocus(audioFocusRequest!!)
        Log.d(TAG, "requestAudioFocus result: $result")
        isAudioFocused = result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED
        
        // 如果成功获得焦点，直接执行获得焦点后的操作
        if (isAudioFocused) {
            CarManager.requestWheelKeyMode()
        }

        Log.d(TAG, "焦点申请结果: $isAudioFocused")
        return isAudioFocused
    }

    /**
     * 释放音频焦点
     */
    fun abandonAudioFocus() {
        Log.d(TAG, "焦点释放")
        if (!isAudioFocused) return

        audioFocusRequest?.let {
            audioManager?.abandonAudioFocusRequest(it)
        }

        onLostAudioFocus()
    }


    /**
     * 当前是否拥有音频焦点
     */
    fun hasAudioFocus(): Boolean {
        return isAudioFocused
    }

    private fun onLostAudioFocus() {
        // 处理失去焦点的逻辑
        Log.d(TAG, "onLostAudioFocus")
        isAudioFocused = false
        KaraokePlayerManager.pause()
        CarManager.exitWheelKeyMode()
    }

    // 打开系统麦克风收音
    fun setKaraokeEnableOn() {
        Log.d(TAG, "setKaraokeEnableOn")
        audioManager?.setParameters("karaoke_enable=on")
    }

    // 关闭系统麦克风收音
    fun setKaraokeEnableOff() {
        Log.d(TAG, "setKaraokeEnableOff")
        audioManager?.setParameters("karaoke_enable=off")
    }

    /**
     * 获取AudioDeviceInfo，适配底软K歌算法
     */
    fun getAudioDeviceInfo(): AudioDeviceInfo? {
        val devices = audioManager?.getDevices(AudioManager.GET_DEVICES_INPUTS)
        devices?.let {
            for (info in devices) {
                Log.d(TAG, "getAudioDeviceInfo address: ${info.address}")
            }
            for (info in devices) {
                if (TextUtils.equals(info.address, "BUS14_INPUT_KTV")) {
                    return info
                }
            }
        }
        Log.d(TAG, "getAudioDeviceInfo: 未找到BUS14_INPUT_KTV")
        return null
    }
}

