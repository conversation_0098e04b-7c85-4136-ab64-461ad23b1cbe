package com.sgmw.ksongs.widget

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.scwang.smart.refresh.layout.api.RefreshFooter
import com.scwang.smart.refresh.layout.api.RefreshKernel
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.constant.RefreshState
import com.scwang.smart.refresh.layout.constant.SpinnerStyle
import com.sgmw.ksongs.R


class MyRefreshFooter @JvmOverloads constructor(
    context: Context?,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr), RefreshFooter {
    private val tvFooter: TextView
    private val ivLoading: ImageView

    init {
        val view = inflate(context, R.layout.layout_refresh_footer, this)
        tvFooter = view.findViewById<TextView>(R.id.tv_load_text)
        ivLoading = view.findViewById<ImageView>(R.id.iv_loading_progress)
    }

    override fun getView(): View {
        return this
    }

    override fun getSpinnerStyle(): SpinnerStyle {
        return SpinnerStyle.Translate
    }

    override fun setPrimaryColors(vararg p0: Int) {
    }

    override fun onInitialized(p0: RefreshKernel, p1: Int, p2: Int) {
    }

    override fun onMoving(p0: Boolean, p1: Float, p2: Int, p3: Int, p4: Int) {
    }

    override fun onReleased(p0: RefreshLayout, p1: Int, p2: Int) {
    }

    override fun onStartAnimator(p0: RefreshLayout, p1: Int, p2: Int) {
    }

    override fun onFinish(p0: RefreshLayout, p1: Boolean): Int {
       return 0
    }

    override fun onStateChanged(
        refreshLayout: RefreshLayout,
        oldState: RefreshState,
        newState: RefreshState
    ) {
        when (newState) {
            RefreshState.Loading -> {
                ivLoading.visibility = VISIBLE
                tvFooter.visibility = VISIBLE
                tvFooter.text = "正在加载..."
            }

            RefreshState.PullUpToLoad ->{
                ivLoading.visibility = GONE
                tvFooter.visibility = VISIBLE
                tvFooter.text = "无更多内容"
            }
            else ->{
                ivLoading.visibility = GONE
                tvFooter.visibility = GONE
            }
        }
    }

    override fun onHorizontalDrag(p0: Float, p1: Int, p2: Int) {

    }

    override fun isSupportHorizontalDrag(): Boolean {
       return false
    }

    override fun autoOpen(p0: Int, p1: Float, p2: Boolean): Boolean {
        return false
    }

    override fun setNoMoreData(p0: Boolean): Boolean {
        return true
    }
}