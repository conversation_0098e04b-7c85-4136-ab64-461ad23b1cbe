package com.sgmw.ksongs.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.appcompat.widget.AppCompatSeekBar

/**
 * @author: 董俊帅
 * @time: 2025/3/5
 * @desc: 竖向滑动AppCompatSeekBar
 */

class VerticalSeekBar : AppCompatSeekBar {

    private var startAndStopListener: StartAndStopListener? = null

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    override fun onSizeChanged(w: Int, h: Int, oldW: Int, oldH: Int) {
        super.onSizeChanged(h, w, oldW, oldH)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(heightMeasureSpec, widthMeasureSpec)
        setMeasuredDimension(measuredHeight, measuredWidth)
    }

    override fun onDraw(c: Canvas) {
        c.rotate(-90f)
        c.translate((-height).toFloat(), 0f)
        super.onDraw(c)
    }

    override fun setProgress(progress: Int) {
        super.setProgress(progress)
        onSizeChanged(width, height, 0, 0)
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (!isEnabled) {
            return false
        }
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                startAndStopListener?.startChange(this)
            }
            MotionEvent.ACTION_MOVE -> {
                val progress = (max - (max * event.y / height)).toInt()
                setProgress(progress)
            }
            MotionEvent.ACTION_UP -> {
                startAndStopListener?.stopChange(this, progress)
            }
            else -> return super.onTouchEvent(event)
        }
        return true
    }

    fun setStartAndStopListener(startAndStopListener: StartAndStopListener) {
        this.startAndStopListener = startAndStopListener
    }

    interface StartAndStopListener {
        fun startChange(seekBar: VerticalSeekBar)
        fun stopChange(seekBar: VerticalSeekBar, progress: Int)
    }
}
