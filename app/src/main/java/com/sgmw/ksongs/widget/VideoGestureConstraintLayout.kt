package com.sgmw.ksongs.widget

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.MotionEvent
import androidx.annotation.IntDef
import androidx.constraintlayout.widget.ConstraintLayout
import com.sgmw.common.utils.Log
import java.lang.annotation.Retention
import java.lang.annotation.RetentionPolicy

/**
 * @author: 董俊帅
 * @time: 2025/3/31
 * @desc: 播放器设置手势控制
 */

class VideoGestureConstraintLayout : ConstraintLayout {

    companion object {
        private const val TAG = "VideoGestureConstraintLayout"
        private const val NONE = 0
        private const val VOLUME = 1
        private const val BRIGHTNESS = 2
    }

    @Retention(RetentionPolicy.SOURCE)
    @IntDef(NONE, VOLUME, BRIGHTNESS)
    private annotation class ScrollMode

    @ScrollMode
    private var mScrollMode: Int = NONE

    private lateinit var mOnGestureListener: VideoPlayerOnGestureListener
    private lateinit var mGestureDetector: GestureDetector
    private var mVideoGestureListener: VideoGestureListener? = null
    // 横向偏移检测，让快进快退不那么敏感
    private var offsetX: Int = 1
//    private var isLongPress: Boolean = false

    private var hasChangeBright: Boolean = false
    private var hasChangeVolume: Boolean = false

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        init(context)
    }

    fun setVideoGestureListener(videoGestureListener: VideoGestureListener) {
        mVideoGestureListener = videoGestureListener
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun init(context: Context) {
        mOnGestureListener = VideoPlayerOnGestureListener(this)
        mGestureDetector = GestureDetector(context, mOnGestureListener)
        // 取消长按，不然会影响滑动
        // mGestureDetector.setIsLongpressEnabled(false)

        setOnTouchListener { v, event ->
            if (event.action == MotionEvent.ACTION_UP || event.action == MotionEvent.ACTION_CANCEL) {
//                if (isLongPress) {
//                    mVideoGestureListener?.onLongPressUP()
//                    isLongPress = false
//                }
                if (hasChangeBright) {
                    mVideoGestureListener?.onEndChangeBright()
                    hasChangeBright = false
                }
                if (hasChangeVolume) {
                    mVideoGestureListener?.onEndChangeVolume()
                    hasChangeVolume = false
                }
            }
            // 监听触摸事件
            mGestureDetector.onTouchEvent(event)
        }
    }

    inner class VideoPlayerOnGestureListener(private val videoGestureConstraintLayout: VideoGestureConstraintLayout) :
        GestureDetector.SimpleOnGestureListener() {

        override fun onDown(e: MotionEvent): Boolean {
            Log.d(TAG, "onDown: ")
//            isLongPress = false
            hasChangeBright = false
            hasChangeVolume = false
            // 每次按下都重置为NONE
            mScrollMode = NONE
            mVideoGestureListener?.onDown(e)
            return true
        }

        override fun onScroll(e1: MotionEvent, e2: MotionEvent, distanceX: Float, distanceY: Float): Boolean {
            when (mScrollMode) {
                NONE -> {
                    Log.d(TAG, "NONE: ")
                    // offset是让快进快退不要那么敏感的值
                    if (Math.abs(distanceX) - Math.abs(distanceY) > offsetX) {
                        mScrollMode = NONE
                    } else {
                        mScrollMode = if (e1.x < width / 2f) BRIGHTNESS else VOLUME
                    }
                    Log.d(TAG, "NONE ScrollMode: $mScrollMode")
                }
                VOLUME -> {
                    mVideoGestureListener?.onVolumeGesture(e1, e2, distanceX, distanceY)
                    hasChangeVolume = true
                }
                BRIGHTNESS -> {
                    mVideoGestureListener?.onBrightnessGesture(e1, e2, distanceX, distanceY)
                    hasChangeBright = true
                }
            }
            return true
        }

        override fun onContextClick(e: MotionEvent): Boolean {
            Log.d(TAG, "onContextClick: ")
            return true
        }

//        override fun onDoubleTap(e: MotionEvent): Boolean {
//            Log.d(TAG, "onDoubleTap: ")
//            mVideoGestureListener?.onDoubleTapGesture(e)
//            return super.onDoubleTap(e)
//        }

//        override fun onLongPress(e: MotionEvent) {
//            Log.d(TAG, "onLongPress: ")
//            mVideoGestureListener?.let {
//                isLongPress = true
//                it.onLongPress(e)
//            }
//        }

        override fun onDoubleTapEvent(e: MotionEvent): Boolean {
            Log.d(TAG, "onDoubleTapEvent: ")
            return super.onDoubleTapEvent(e)
        }

        override fun onSingleTapUp(e: MotionEvent): Boolean {
            Log.d(TAG, "onSingleTapUp: ")
            return super.onSingleTapUp(e)
        }

        override fun onFling(e1: MotionEvent, e2: MotionEvent, velocityX: Float, velocityY: Float): Boolean {
            Log.d(TAG, "onFling: ")
            return super.onFling(e1, e2, velocityX, velocityY)
        }

        override fun onShowPress(e: MotionEvent) {
            Log.d(TAG, "onShowPress: ${e.action}")
            super.onShowPress(e)
        }

        override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
            Log.d(TAG, "onSingleTapConfirmed: ")
            mVideoGestureListener?.onSingleTapGesture(e)
            return super.onSingleTapConfirmed(e)
        }
    }

    interface VideoGestureListener {
        // 亮度手势，手指在Layout左半部上下滑动时候调用
        fun onBrightnessGesture(e1: MotionEvent, e2: MotionEvent, distanceX: Float, distanceY: Float)

        // 音量手势，手指在Layout右半部上下滑动时候调用
        fun onVolumeGesture(e1: MotionEvent, e2: MotionEvent, distanceX: Float, distanceY: Float)

        // 单击手势，确认是单击的时候调用
        fun onSingleTapGesture(e: MotionEvent)

        // 双击手势，确认是双击的时候调用
        fun onDoubleTapGesture(e: MotionEvent)

        // 按下手势，第一根手指按下时候调用
        fun onDown(e: MotionEvent)

        fun onLongPress(e: MotionEvent)

        fun onLongPressUP()

        // 结束改变亮度
        fun onEndChangeBright()

        // 结束改变声音
        fun onEndChangeVolume()
    }
}