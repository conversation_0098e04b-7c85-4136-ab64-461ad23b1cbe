package com.sgmw.ksongs.widget;


import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.View;

import androidx.core.view.accessibility.AccessibilityNodeInfoCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.Objects;

public class AccessibilityGridLayoutManager extends GridLayoutManager {
    private String TAG = "AccessibilityGrid";
    RecyclerView mRecyclerView;

    public AccessibilityGridLayoutManager(Context context, int spanCount) {
        super(context, spanCount);
        mRecyclerView = null;
    }

    public AccessibilityGridLayoutManager(Context context, int spanCount, int footCount) {
        super(context, spanCount);
        mRecyclerView = null;
    }

    public AccessibilityGridLayoutManager(Context context, int splitCount, int orientation, boolean reverseLayout) {
        super(context, splitCount, orientation, reverseLayout);
        mRecyclerView = null;
    }

    @Override
    public void onAttachedToWindow(RecyclerView recyclerView) {
        super.onAttachedToWindow(recyclerView);
        mRecyclerView = recyclerView;
    }

    @Override
    public void onDetachedFromWindow(RecyclerView recyclerView, RecyclerView.Recycler recycler) {
        super.onDetachedFromWindow(recyclerView, recycler);
        mRecyclerView = null;
    }

    @SuppressLint("NewApi")
    @Override
    public void onInitializeAccessibilityNodeInfo(RecyclerView.Recycler recycler, RecyclerView.State state, AccessibilityNodeInfoCompat accessibilityNodeInfoCompat) {
        super.onInitializeAccessibilityNodeInfo(recycler, state, accessibilityNodeInfoCompat);
        int currentMin;
        int currentMax;
        int itemCount;
        RecyclerView recyclerView = mRecyclerView;
        if (recyclerView == null || recyclerView.getAdapter() == null) {
            return;
        }
        AccessibilityNodeInfoCompat.CollectionInfoCompat collectionInfo = accessibilityNodeInfoCompat.getCollectionInfo();
        if (collectionInfo != null) {
            int rowCount =collectionInfo.getRowCount();
            int columnCount = collectionInfo.getColumnCount();
            if (rowCount <= 0 && columnCount <= 0) {
                if(mRecyclerView!=null&& mRecyclerView.getAdapter()!=null){
                    itemCount = mRecyclerView.getAdapter().getItemCount();
                }else {
                    itemCount = getItemCount();
                }
            } else if (columnCount == 0 || rowCount == 0) {
                itemCount = Math.max(columnCount, rowCount);
            } else {
                itemCount = getItemCount();
                if(itemCount==0){
                    itemCount = Math.multiplyExact(columnCount, rowCount);
                }
            }
            Log.d(TAG,"onInitializeAccessibilityNodeInfo rowCount:"+rowCount+",columnCount:"+columnCount+",itemCount:"+itemCount+",adapterItemCount:"+getItemCount());

            accessibilityNodeInfoCompat.setCollectionInfo(AccessibilityNodeInfoCompat.CollectionInfoCompat.obtain(itemCount , itemCount , collectionInfo.isHierarchical(), collectionInfo.getSelectionMode()));
        }
        int childCount = getChildCount();
        if (childCount > 0) {
            View childAt = getChildAt(0);
            currentMin = childAt != null ? getPosition(childAt) : 0;
            View childAt2 = getChildAt(childCount - 1);
            currentMax = childAt2 != null ? getPosition(childAt2) : 0;
        } else {
            currentMax = 0;
            currentMin = 0;
        }
        accessibilityNodeInfoCompat.setRangeInfo(AccessibilityNodeInfoCompat.RangeInfoCompat.obtain(AccessibilityNodeInfoCompat.RangeInfoCompat.RANGE_TYPE_INT, currentMin, currentMax, currentMin));
    }

    @Override
    public boolean performAccessibilityAction(RecyclerView.Recycler recycler, RecyclerView.State state, int i, Bundle bundle) {
        if (i != AccessibilityNodeInfoCompat.AccessibilityActionCompat.ACTION_SCROLL_TO_POSITION.getId()) {
            return super.performAccessibilityAction(recycler, state, i, bundle);
        }
        RecyclerView recyclerView = mRecyclerView;
        if (recyclerView != null && recyclerView.getAdapter() != null && bundle != null) {
            int rowInt = bundle.getInt(AccessibilityNodeInfoCompat.ACTION_ARGUMENT_ROW_INT, -1);
            int min = Math.min(rowInt, Objects.requireNonNull(mRecyclerView.getAdapter()).getItemCount() - 1);
            if (rowInt >= 0) {
                mRecyclerView.smoothScrollToPosition(min);
                return true;
            }
        }
        return false;
    }

    @Override
    public void onLayoutChildren(RecyclerView.Recycler recycler, RecyclerView.State state) {
        try {
            super.onLayoutChildren(recycler, state);
        } catch (IndexOutOfBoundsException e) {
            e.printStackTrace();
        }
    }
}