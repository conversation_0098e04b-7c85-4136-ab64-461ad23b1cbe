package com.sgmw.ksongs.widget

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.DrawableRes
import com.sgmw.ksongs.R

/**
 * @author: 董俊帅
 * @time: 2025/3/3
 * @desc: 播放设置页面的item
 */
@SuppressLint("CustomViewStyleable")
class PlaySettingItemView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    val imageView: ImageView
    val textView: TextView

    init {
        // 加载布局
        LayoutInflater.from(context).inflate(R.layout.item_play_setting, this, true)

        // 获取子视图
        imageView = findViewById(R.id.iv_icon)
        textView = findViewById(R.id.tv_label)

        // 处理自定义属性
        initAttributes(context, attrs)
    }

    private fun initAttributes(context: Context, attrs: AttributeSet?) {
        context.obtainStyledAttributes(attrs, R.styleable.PlaySettingItem).apply {
            try {
                // 文本和图标
                setText(getString(R.styleable.PlaySettingItem_itemText))
                setIcon(getResourceId(R.styleable.PlaySettingItem_itemIcon, 0))

                // 图标尺寸参数
                val defaultSize = resources.getDimensionPixelSize(R.dimen.dp_48)
                val defaultMarginTop = resources.getDimensionPixelSize(R.dimen.dp_22)

                adjustIconParams(
                    width = getDimensionPixelSize(
                        R.styleable.PlaySettingItem_iconWidth,
                        defaultSize
                    ),
                    height = getDimensionPixelSize(
                        R.styleable.PlaySettingItem_iconHeight,
                        defaultSize
                    ),
                    marginTop = getDimensionPixelSize(
                        R.styleable.PlaySettingItem_iconMarginTop,
                        defaultMarginTop
                    )
                )
            } finally {
                recycle()
            }
        }
    }

    /*---------------- 公开方法 ----------------*/
    fun setText(text: String?) {
        textView.text = text ?: ""
    }

    fun setIcon(@DrawableRes resId: Int) {
        if (resId != 0) {
            imageView.setImageResource(resId)
        }
    }

    /*---------------- 内部方法 ----------------*/
    private fun adjustIconParams(width: Int, height: Int, marginTop: Int) {
        val params = imageView.layoutParams as LayoutParams
        params.width = width
        params.height = height
        params.topMargin = marginTop
        imageView.layoutParams = params
    }
}