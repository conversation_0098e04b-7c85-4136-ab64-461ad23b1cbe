package com.sgmw.ksongs.widget

import android.content.Context
import android.util.Log
import androidx.annotation.IdRes
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentTransaction
import androidx.lifecycle.Lifecycle
import androidx.navigation.NavBackStackEntry
import androidx.navigation.NavOptions
import androidx.navigation.Navigator
import androidx.navigation.fragment.FragmentNavigator
import com.sgmw.ksongs.ui.home.HomeFragment
import com.sgmw.ksongs.ui.login.LoginFragment


@Navigator.Name("fix_fragment")
class FixFragmentNavigator(
    private val context: Context,
    private val fragmentManager: FragmentManager,
    private val containerId: Int
) : FragmentNavigator(context, fragmentManager, containerId) {

    private val TAG = "FragmentNavigatorHideSh"
    override fun navigate(
        entries: List<NavBackStackEntry>,
        navOptions: NavOptions?,
        navigatorExtras: Navigator.Extras?
    ) {
        if (fragmentManager.isStateSaved) {
            Log.i(
                TAG, "Ignoring navigate() call: FragmentManager has already saved its state"
            )
            return
        }
        for (entry in entries) {
            navigateOver(entry, navOptions, navigatorExtras)
        }
    }

    private fun navigateOver(
        entry: NavBackStackEntry,
        navOptions: NavOptions?,
        navigatorExtras: Navigator.Extras?
    ) {
        val initialNavigation = state.backStack.value.isEmpty()
        val backStack = state.backStack.value
        val destination = entry.destination as Destination
        @IdRes val destId = destination.id
        var mBackStack: LinkedHashSet<String>?= null
        try {
            val field = FragmentNavigator::class.java.getDeclaredField("savedIds")
            field.isAccessible = true
            mBackStack = field[this] as LinkedHashSet<String>
        } catch (e: NoSuchFieldException) {
            e.printStackTrace()
        } catch (e: IllegalAccessException) {
            e.printStackTrace()
        }
        val restoreState = (
                navOptions != null && !initialNavigation &&
                        navOptions.shouldRestoreState() &&
                        mBackStack!!.remove(entry.id)
                )
        if (restoreState) {
            // Restore back stack does all the work to restore the entry
            fragmentManager.restoreBackStack(entry.id)
            state.push(entry)
            return
        }
        val ft = createFragmentTransactionOver(entry, navOptions)
        if (!initialNavigation) {
            ft.addToBackStack(entry.id)
        }
        if (navigatorExtras is Extras) {
            for ((key, value) in navigatorExtras.sharedElements) {
                ft.addSharedElement(key, value)
            }
        }
        ft.commit()
        state.push(entry)

    }

    private fun createFragmentTransactionOver(
        entry: NavBackStackEntry,
        navOptions: NavOptions?
    ): FragmentTransaction {
        val destination = entry.destination as Destination
        val args = entry.arguments
        var className = destination.className
        if (className[0] == '.') {
            className = context.packageName + className
        }
        val ft = fragmentManager.beginTransaction()
        var enterAnim = navOptions?.enterAnim ?: -1
        var exitAnim = navOptions?.exitAnim ?: -1
        var popEnterAnim = navOptions?.popEnterAnim ?: -1
        var popExitAnim = navOptions?.popExitAnim ?: -1
        if (enterAnim != -1 || exitAnim != -1 || popEnterAnim != -1 || popExitAnim != -1) {
            enterAnim = if (enterAnim != -1) enterAnim else 0
            exitAnim = if (exitAnim != -1) exitAnim else 0
            popEnterAnim = if (popEnterAnim != -1) popEnterAnim else 0
            popExitAnim = if (popExitAnim != -1) popExitAnim else 0
            ft.setCustomAnimations(enterAnim, exitAnim, popEnterAnim, popExitAnim)
        }

        val fragment = fragmentManager.primaryNavigationFragment
        Log.d(TAG,"fragment == $fragment")
        if (fragment is LoginFragment || className.contains("LoginFragment")){
            Log.d(TAG, "Navigating to LoginFragment, clearing all existing fragments")
            // 清理所有现有的Fragment，避免replace操作导致的状态异常
            clearAllFragments()

            val frag = fragmentManager.fragmentFactory.instantiate(context.classLoader, className)
            frag.arguments = args
            ft.replace(containerId, frag)
            ft.setPrimaryNavigationFragment(frag)
            ft.setReorderingAllowed(true)
            return ft
        }
        if (fragment != null) {
            ft.setMaxLifecycle(fragment, Lifecycle.State.STARTED)
            ft.hide(fragment)
        }
        var frag: Fragment? = null
        val tag = destination.id.toString()
        Log.d(TAG,"fragment2 == $tag")
        frag = fragmentManager.findFragmentByTag(tag)
        Log.d(TAG,"fragment3== $frag")
        if (frag != null) {
            // 检查Fragment状态，确保Fragment处于正常状态
            // 特别检查View是否存在，因为replace操作会销毁View但保留Fragment对象
            val hasValidView = frag.view != null && frag.isAdded && !frag.isDetached && !frag.isRemoving

            if (hasValidView) {
                frag.arguments = args
                ft.setMaxLifecycle(frag, Lifecycle.State.RESUMED)
                ft.show(frag)
                Log.d(TAG, "Fragment is in valid state, showing existing fragment: ${frag.javaClass.simpleName}")
            } else {
                Log.w(TAG, "Fragment is in invalid state: hasView=${frag.view != null}, isAdded=${frag.isAdded}, isDetached=${frag.isDetached}, isRemoving=${frag.isRemoving}")
                // 如果Fragment状态异常（特别是View被销毁但对象还存在），移除并重新创建
                if (frag.isAdded) {
                    ft.remove(frag)
                }
                frag = instantiateFragment(context, fragmentManager, className, args)
                frag.arguments = args
                ft.add(containerId, frag, tag)
                Log.d(TAG, "Recreated fragment due to invalid state: ${frag.javaClass.simpleName}")
            }
        } else {
            frag = instantiateFragment(context, fragmentManager, className, args)
            frag.arguments = args
            ft.add(containerId, frag, tag)
            Log.d(TAG, "Created new fragment: ${frag.javaClass.simpleName}")
        }

        ft.setPrimaryNavigationFragment(frag)
        ft.setReorderingAllowed(true)
        return ft
    }

    override fun popBackStack(popUpTo: NavBackStackEntry, savedState: Boolean) {
        if (fragmentManager.isStateSaved) {
            Log.i(
                TAG, "Ignoring popBackStack() call: FragmentManager has already saved its state"
            )
            return
        }
        if (savedState) {
            val beforePopList = state.backStack.value
            val initialEntry = beforePopList.first()
            // Get the set of entries that are going to be popped
            val poppedList = beforePopList.subList(
                beforePopList.indexOf(popUpTo),
                beforePopList.size
            )
            // Now go through the list in reversed order (i.e., started from the most added)
            // and save the back stack state of each.
            for (entry in poppedList.reversed()) {
                if (entry == initialEntry) {
                    Log.i(
                        TAG,
                        "FragmentManager cannot save the state of the initial destination $entry"
                    )
                } else {
                    fragmentManager.saveBackStack(entry.id)
                    var mBackStack: LinkedHashSet<String>?= null
                    try {
                        val field = FragmentNavigator::class.java.getDeclaredField("savedIds")
                        field.isAccessible = true
                        mBackStack = field[this] as LinkedHashSet<String>
                    } catch (e: NoSuchFieldException) {
                        Log.e(TAG, "Failed to access savedIds field: NoSuchFieldException", e)
                    } catch (e: IllegalAccessException) {
                        Log.e(TAG, "Failed to access savedIds field: IllegalAccessException", e)
                    } catch (e: Exception) {
                        Log.e(TAG, "Unexpected error accessing savedIds field", e)
                    }
                    if (mBackStack != null) {
                        mBackStack += entry.id
                    }
                }
            }
        } else {
            fragmentManager.popBackStack(
                popUpTo.id,
                FragmentManager.POP_BACK_STACK_INCLUSIVE
            )
        }
        state.popWithTransition(popUpTo, savedState)
    }

    /**
     * 清理所有现有的Fragment，避免replace操作导致的状态异常
     * 当跳转到LoginFragment时调用，确保所有Fragment都被正确清理
     */
    private fun clearAllFragments() {
        try {
            Log.d(TAG, "clearAllFragments: Starting to clear all fragments")

            // 获取所有已添加的Fragment
            val fragments = fragmentManager.fragments.toList()
            Log.d(TAG, "clearAllFragments: Found ${fragments.size} fragments to clear")

            if (fragments.isNotEmpty()) {
                val transaction = fragmentManager.beginTransaction()

                // 移除所有Fragment
                for (fragment in fragments) {
                    if (fragment != null && fragment.isAdded && LoginFragment::class.java.simpleName != fragment.javaClass.simpleName) {
                        Log.d(TAG, "clearAllFragments: Removing fragment ${fragment.javaClass.simpleName}")
                        transaction.remove(fragment)
                    }
                }

                transaction.commitNowAllowingStateLoss()
                Log.d(TAG, "clearAllFragments: All fragments cleared successfully")
            }

        } catch (e: Exception) {
            Log.e(TAG, "clearAllFragments: Error clearing fragments", e)
        }
    }

}

