package com.sgmw.ksongs.widget

import android.content.Context
import android.graphics.drawable.AnimationDrawable
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.R


class FrameAnimationView(context: Context, attrs: AttributeSet?, defStyleAttr: Int) :
    AppCompatImageView(context, attrs, defStyleAttr) {
    companion object {
        const val TAG = "FrameAnimationView"
    }

    private val mAnimationResId: Int

    init {
        if (attrs != null) {
            // view不可见时没必要加载动画drawable，所以自定义属性，不使用 android:src
            // xml中配置android:src为动画的第一帧。确保view可见时有图像。动画根据播放状态加载
            val typedArray = context.theme.obtainStyledAttributes(
                attrs,
                R.styleable.FrameAnimationView, defStyleAttr, 0
            )
            mAnimationResId =
                typedArray.getResourceId(R.styleable.FrameAnimationView_src_animation, -1)
            typedArray.recycle()
        } else {
            mAnimationResId = -1
            Log.w(TAG, "init attrs is null !!")
        }
    }

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    /**
     * 设置可见状态，并根据播放状态更新动画播放/停止
     */
    public fun setVisibilityWithPlayState(visibility: Int, isPlaying: Boolean) {
        setVisibility(visibility)
        // 不可见，停止动画
        if (visibility != VISIBLE) {
            stopAnim(true)
            return
        }

        // 可见则根据播放状态播放/停止动画
        if (isPlaying) {
            startAnim()
        } else {
            stopAnim()
        }
    }

    public fun startAnim() {
        val anim = if (drawable is AnimationDrawable) {
            drawable as AnimationDrawable
        } else if (mAnimationResId != -1) {
            setImageResource(mAnimationResId)
            drawable as AnimationDrawable
        } else {
            null
        }

        anim?.let {
            if (!it.isRunning) {
                it.start()
            }
        }
    }

    public fun stopAnim(release: Boolean = false) {
        val anim = if (drawable is AnimationDrawable) {
            drawable as AnimationDrawable
        } else {
            null
        }

        anim?.let {
            if (it.isRunning) {
                it.selectDrawable(0)
                it.stop()
            }
        }
    }
}