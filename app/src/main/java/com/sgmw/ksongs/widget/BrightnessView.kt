package com.sgmw.ksongs.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.annotation.NonNull
import androidx.annotation.Nullable
import androidx.constraintlayout.widget.ConstraintLayout
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.databinding.LayoutBrightnessBinding

/**
 * @author: 董俊帅
 * @time: 2025/3/31
 * @desc: 自定义亮度调节控件
 */

class BrightnessView : ConstraintLayout {
    private val TAG = "BrightnessView"
    private lateinit var mBinding: LayoutBrightnessBinding
    private lateinit var mHideRunnable: HideRunnable
    private var mDuration = 5000

    private var isFirstVisible = true

    constructor(@NonNull context: Context) : super(context) {
        init(context)
    }

    constructor(@NonNull context: Context, @Nullable attrs: AttributeSet?) : super(context, attrs) {
        init(context)
    }

    constructor(@NonNull context: Context, @Nullable attrs: AttributeSet?, defStyleAttr: Int) :
            super(context, attrs, defStyleAttr) {
        init(context)
    }

    private fun init(context: Context) {
        mBinding = LayoutBrightnessBinding.inflate(LayoutInflater.from(context), this, true)
        mHideRunnable = HideRunnable()
        visibility = GONE
    }

    fun show() {
        visibility = VISIBLE
        removeCallbacks(mHideRunnable)
        postDelayed(mHideRunnable, mDuration.toLong())
    }

    fun setProgress(progress: Int) {
        Log.d(TAG, "setProgress: $progress")
        if (!isFirstVisible) {
            show()
        }
        isFirstVisible = false
        mBinding.pbBrightness.progress = progress
    }

    fun setDuration(duration: Int) {
        this.mDuration = duration
    }

    fun setImageResource(resource: Int) {
        // Implementation goes here
    }

    private inner class HideRunnable : Runnable {
        override fun run() {
            <EMAIL> = GONE
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        removeCallbacks(mHideRunnable)
    }
}