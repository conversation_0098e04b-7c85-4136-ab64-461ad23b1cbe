package com.sgmw.ksongs.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.Transformation;
import android.view.animation.TranslateAnimation;
import android.widget.RadioButton;
import android.widget.RadioGroup;

import com.autoai.baseline.support.skincore.SkinManager;
import com.autoai.baseline.support.skincore.SkinViewSupport;
import com.sgmw.common.utils.Log;
import com.sgmw.ksongs.R;

public class NewsRadioGroup extends RadioGroup implements
        RadioGroup.OnCheckedChangeListener, SkinViewSupport {

    private static final String TAG = "NewsRadioGroup";

    private final Transformation mTransformation = new Transformation();
    private Animation mAnimation;
    private OnCheckedChangeListener mOnCheckedChangeListener;
    private int mLastCheckedId = -1;
    private Drawable mDummy;
    private Drawable mDrawableNormal, mDrawableChecked;
    private boolean mAminDoing = false;

    public NewsRadioGroup(Context context) {
        super(context);
        init();
    }

    public NewsRadioGroup(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        super.setOnCheckedChangeListener(this);
        mLastCheckedId = super.getCheckedRadioButtonId();
        mDummy = getResources().getDrawable(R.drawable.bg_radiobutton);
        mDrawableNormal = getResources().getDrawable(
                android.R.color.transparent);
    }

    public void onCheckedChanged(RadioGroup group, int checkedId) {
        Log.d(TAG,"onCheckedChanged -->" + mLastCheckedId);
        if (mLastCheckedId != -1) {
            doAmin(checkedId);
        } else {
            mLastCheckedId = checkedId;
        }
        if (mOnCheckedChangeListener != null) {
            mOnCheckedChangeListener.onCheckedChanged(group, checkedId);
        }
    }

    private void doAmin(int checkedId) {
        RadioButton rbCurChecked = (RadioButton) super.findViewById(checkedId), rbLastChecked = (RadioButton) super
                .findViewById(mLastCheckedId);
        if (rbCurChecked == null || rbLastChecked == null) {
            mLastCheckedId = checkedId;
            return;
        }
        int X1 = rbLastChecked.getLeft(), X2 = rbCurChecked.getLeft();
        if (X1 <= 0 && X2 <= 0) {
            mLastCheckedId = checkedId;
            return;
        }
        Log.d(TAG,"doAmin");
        if (mDrawableChecked == null) {
            mDrawableChecked = rbCurChecked.getBackground();
        }
        mDummy.setBounds(0, 0, rbCurChecked.getWidth(),
                rbCurChecked.getHeight());
        rbCurChecked.setBackgroundDrawable(mDrawableNormal);
//        rbCurChecked.setBackgroundResource(R.drawable.rb_checked);

        if (mAminDoing && mAnimation != null) {
            mAnimation.reset();
        }
        mAnimation = new TranslateAnimation(X1, X2, rbCurChecked.getTop(),
                rbCurChecked.getTop());
        mAnimation.setDuration(160);
        mAnimation.initialize(0, 0, 0, 0);
        mAminDoing = true;
        mAnimation.startNow();
        invalidate();
    }

    @Override
    public void setOnCheckedChangeListener(OnCheckedChangeListener listener) {
        mOnCheckedChangeListener = listener;
    }

    protected void onDraw(Canvas canvas) {
        if (mAnimation == null || !mAminDoing) {
            return;
        }
        if (!mAnimation.hasEnded()) {
            Log.d(TAG,"onDraw" + mAnimation.hasEnded());
            int sc = canvas.save();
            mAnimation.getTransformation(
                    AnimationUtils.currentAnimationTimeMillis(),
                    mTransformation);
            canvas.concat(mTransformation.getMatrix());
            mDummy.draw(canvas);
            canvas.restoreToCount(sc);
//            ((RadioButton)findViewById(getCheckedRadioButtonId())).setBackgroundResource(R.color.transparent);
            invalidate();
//            setReallyCheck();
        } else {
            mAminDoing = false;
            setReallyCheck();
//            ((RadioButton)findViewById(getCheckedRadioButtonId())).setBackgroundResource(R.drawable.sl_tab);
        }
    }

    private void setReallyCheck() {
        if (mLastCheckedId != -1) {
            super.findViewById(mLastCheckedId).setBackgroundDrawable(
                    mDrawableNormal);
        }

        mLastCheckedId = super.getCheckedRadioButtonId();
        if (mLastCheckedId != -1) {
            super.findViewById(mLastCheckedId).setBackgroundDrawable(
                    mDrawableChecked);
        }
    }

    @Override
    public void applySkin() {
        Log.d(TAG,"applySkin --> " + SkinManager.getInstance().isDay());
        if (SkinManager.getInstance().isDay()) {
            mDummy = getResources().getDrawable(R.drawable.bg_radiobutton);
        } else {
            mDummy = getResources().getDrawable(R.drawable.bg_radiobutton_n);
        }


    }
}
