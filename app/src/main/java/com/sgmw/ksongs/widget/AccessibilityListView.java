package com.sgmw.ksongs.widget;

import android.content.Context;
import android.os.Bundle;
import android.util.AttributeSet;
import android.util.Log;
import android.view.accessibility.AccessibilityNodeInfo;
import android.widget.ListView;

import androidx.annotation.Nullable;
import androidx.core.view.accessibility.AccessibilityNodeInfoCompat;

/**
 * Created by  <PERSON><PERSON><PERSON><PERSON><PERSON>   on 2024/8/30.
 * @email <EMAIL>
 */
public class AccessibilityListView  extends ListView {
    private final String TAG="AccessibilityListView";
    public AccessibilityListView(Context context) {
        super(context);
    }

    public AccessibilityListView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public AccessibilityListView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public AccessibilityListView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }
    // 获取列表总数，以及获取显示范围内的最小position 和最大position
    @Override
    public void onInitializeAccessibilityNodeInfo(AccessibilityNodeInfo info) {
        super.onInitializeAccessibilityNodeInfo(info);
        int currentMin =0;
        int currentMax =0;
        int itemCount =0;
        if (getAdapter()== null) {
            return;
        }
        try {
            AccessibilityNodeInfo.CollectionInfo   collectionInfo = info.getCollectionInfo();
            if (collectionInfo != null) {
                if (collectionInfo.getRowCount() <= 0 && collectionInfo.getColumnCount() <= 0) {
                    if(getAdapter()!=null){
                        itemCount = getAdapter().getCount();
                    }
                } else {
                    itemCount = Math.max(collectionInfo.getColumnCount(), collectionInfo.getRowCount());
                }
                info.setCollectionInfo(AccessibilityNodeInfo.CollectionInfo.obtain(itemCount, itemCount, collectionInfo.isHierarchical(), collectionInfo.getSelectionMode()));
            }
            int childCount = getChildCount();
            if (childCount > 0) {
                currentMin = getFirstVisiblePosition() ;
                currentMax = getLastVisiblePosition() ;
            }
            info.setRangeInfo(AccessibilityNodeInfo.RangeInfo.obtain(AccessibilityNodeInfoCompat.RangeInfoCompat.RANGE_TYPE_INT, currentMin, currentMax, currentMin));
            Log.d(TAG,"onInitializeAccessibilityNodeInfo setRangeInfo currentMin:"+currentMin+",currentMax:"+currentMax+",current:"+currentMin+",rowCount:"+itemCount);
        }catch (Exception e){
            e.printStackTrace();
        }

    }
    // 滑动到指定位置
    @Override
    public boolean performAccessibilityAction(int action, @Nullable Bundle arguments) {
        if (action != AccessibilityNodeInfoCompat.AccessibilityActionCompat.ACTION_SCROLL_TO_POSITION.getId()) {
            return super.performAccessibilityAction(action, arguments);
        }
        try {
            if (getAdapter() != null && arguments != null) {
                int rowInt = arguments.getInt(AccessibilityNodeInfoCompat.ACTION_ARGUMENT_ROW_INT, -1);
                int count = getAdapter().getCount();
                int position = Math.min(rowInt, count - 1);
                Log.d(TAG,"performAccessibilityAction  ACTION_SCROLL_TO_POSITION rowInt:"+rowInt+",count:"+count+",position:"+position);
                if (rowInt >= 0) {
                    setSelection(position);
                    return true;
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }

        return false;
    }
}
