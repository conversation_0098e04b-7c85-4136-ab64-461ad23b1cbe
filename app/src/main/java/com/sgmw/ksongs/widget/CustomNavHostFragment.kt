package com.sgmw.ksongs.widget

import androidx.navigation.NavController
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.plusAssign

public open class CustomNavHostFragment : NavHostFragment() {
    override fun onCreateNavController(navController: NavController) {
        navController.navigatorProvider += FixFragmentNavigator(
            requireContext(),
            fragmentManager = childFragmentManager,
            id
        )
        super.onCreateNavController(navController)
    }

//    作者：layz4android
//    链接：https://juejin.cn/post/7147265281973288973
//    来源：稀土掘金
//    著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。
}