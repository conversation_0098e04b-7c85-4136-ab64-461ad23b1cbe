package com.sgmw.ksongs.widget

/**
 * @author: 董俊帅
 * @time: 2025/3/25
 * @desc: 调音弹框带数字AppCompatSeekBar
 */
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.util.TypedValue
import android.view.MotionEvent
import androidx.appcompat.widget.AppCompatSeekBar
import com.autoai.baseline.support.skincore.SkinManager
import com.sgmw.ksongs.R


class BubbleSeekBar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = androidx.appcompat.R.attr.seekBarStyle
) : AppCompatSeekBar(context, attrs, defStyleAttr) {

    // 文本绘制工具
    private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = if (SkinManager.getInstance().isDay) context.getColor(R.color.font_main_color) else context.getColor(R.color.font_main_color_n)   // 文字颜色
        textSize = spToPx(context.resources.getDimension(R.dimen.dp_26)) // 26sp
        textAlign = Paint.Align.CENTER
    }

    // 文本垂直偏移量（单位：px）
    private val textVerticalOffset = -dpToPx(context.resources.getDimension(R.dimen.dp_34).toInt())

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas) // 先绘制原生控件
        drawProgressText(canvas)
    }

    private fun drawProgressText(canvas: Canvas) {
        thumb?.let {
            // 获取Thumb实际绘制区域
            val thumbBounds = it.copyBounds()

            // 计算文本位置
            val centerX = thumbBounds.exactCenterX()
            val baselineY = thumbBounds.top + textVerticalOffset - textPaint.ascent()

            // 绘制文本
            canvas.drawText("$progress", centerX, baselineY, textPaint)
        }
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        invalidate() // 触摸时强制重绘
        return super.onTouchEvent(event)
    }

    private fun spToPx(sp: Float): Float {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_SP,
            sp,
            resources.displayMetrics
        )
    }

    private fun dpToPx(dp: Int): Float {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            dp.toFloat(),
            resources.displayMetrics
        )
    }
}