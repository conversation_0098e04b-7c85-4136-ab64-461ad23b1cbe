package com.sgmw.ksongs.widget;


import android.content.Context;
import android.os.Bundle;
import android.util.AttributeSet;
import android.view.View;

import androidx.core.view.accessibility.AccessibilityNodeInfoCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.Objects;

public class AccessibilityLinearLayoutManager extends LinearLayoutManager {
    RecyclerView mRecyclerView;

    public AccessibilityLinearLayoutManager(Context context) {
        super(context);
        mRecyclerView = null;
    }

    public AccessibilityLinearLayoutManager(Context context,  @RecyclerView.Orientation int orientation,
                                            boolean reverseLayout) {
        super(context, orientation, reverseLayout);
        mRecyclerView = null;
    }

    public AccessibilityLinearLayoutManager(Context context,  AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        mRecyclerView = null;
    }

    @Override
    public void onAttachedToWindow(RecyclerView recyclerView) {
        super.onAttachedToWindow(recyclerView);
        mRecyclerView = recyclerView;
    }

    @Override
    public void onDetachedFromWindow(RecyclerView recyclerView, RecyclerView.Recycler recycler) {
        super.onDetachedFromWindow(recyclerView, recycler);
        mRecyclerView = null;
    }

    @Override
    public void onInitializeAccessibilityNodeInfo(RecyclerView.Recycler recycler, RecyclerView.State state, AccessibilityNodeInfoCompat accessibilityNodeInfoCompat) {
        super.onInitializeAccessibilityNodeInfo(recycler, state, accessibilityNodeInfoCompat);
        int currentMin;
        int currentMax;
        int itemCount;
        RecyclerView recyclerView = mRecyclerView;
        if (recyclerView == null || recyclerView.getAdapter() == null) {
            return;
        }
        AccessibilityNodeInfoCompat.CollectionInfoCompat collectionInfo = accessibilityNodeInfoCompat.getCollectionInfo();
        if (collectionInfo != null) {
            if (collectionInfo.getRowCount() <= 0 && collectionInfo.getColumnCount() <= 0) {
                itemCount = mRecyclerView.getAdapter().getItemCount();
            } else {
                itemCount = Math.max(collectionInfo.getColumnCount(), collectionInfo.getRowCount());
            }
            accessibilityNodeInfoCompat.setCollectionInfo(AccessibilityNodeInfoCompat.CollectionInfoCompat.obtain(itemCount, itemCount, collectionInfo.isHierarchical(), collectionInfo.getSelectionMode()));
        }
        int childCount = getChildCount();
        if (childCount > 0) {
            View childAt = getChildAt(0);
            currentMin = childAt != null ? getPosition(childAt) : 0;
            View childAt2 = getChildAt(childCount - 1);
            currentMax = childAt2 != null ? getPosition(childAt2) : 0;
        } else {
            currentMax = 0;
            currentMin = 0;
        }
        accessibilityNodeInfoCompat.setRangeInfo(AccessibilityNodeInfoCompat.RangeInfoCompat.obtain(AccessibilityNodeInfoCompat.RangeInfoCompat.RANGE_TYPE_INT, currentMin, currentMax, currentMin));
    }

    @Override
    public boolean performAccessibilityAction(RecyclerView.Recycler recycler, RecyclerView.State state, int i, Bundle bundle) {
        if (i != AccessibilityNodeInfoCompat.AccessibilityActionCompat.ACTION_SCROLL_TO_POSITION.getId()) {
            return super.performAccessibilityAction(recycler, state, i, bundle);
        }
        RecyclerView recyclerView = mRecyclerView;
        if (recyclerView != null && recyclerView.getAdapter() != null && bundle != null) {
            int rowInt = bundle.getInt(AccessibilityNodeInfoCompat.ACTION_ARGUMENT_ROW_INT, -1);
            int min = Math.min(rowInt, Objects.requireNonNull(mRecyclerView.getAdapter()).getItemCount() - 1);
            if (rowInt >= 0) {
                mRecyclerView.smoothScrollToPosition(min);
                return true;
            }
        }
        return false;
    }
}