package com.sgmw.ksongs.widget

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import androidx.interpolator.view.animation.FastOutSlowInInterpolator
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.RecyclerView

/**
 * @author: 董俊帅
 * @time: 2025/5/7
 * @desc: 删除Item动画
 */

class FadeOutDeleteAnimator : DefaultItemAnimator() {

    private val interpolator = FastOutSlowInInterpolator()

    init {
        removeDuration = REMOVE_DURATION
        moveDuration = MOVE_DURATION
    }

    override fun animateRemove(holder: RecyclerView.ViewHolder): Boolean {
        holder.itemView.animate()
            .alpha(0f)
            .setDuration(removeDuration)
            .setInterpolator(interpolator)
            .setListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    dispatchRemoveFinished(holder)
                }
            })
            .start()
        return true
    }

    override fun animateMove(
        holder: RecyclerView.ViewHolder,
        fromX: Int, fromY: Int,
        toX: Int, toY: Int
    ): Boolean {
        val view = holder.itemView
        val deltaX = toX - fromX
        val deltaY = toY - fromY

        if (deltaX == 0 && deltaY == 0) {
            dispatchMoveFinished(holder)
            return false
        }

        view.translationX = -deltaX.toFloat()
        view.translationY = -deltaY.toFloat()
        view.animate()
            .translationX(0f)
            .translationY(0f)
            .setDuration(moveDuration)
            .setInterpolator(interpolator)
            .setListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    dispatchMoveFinished(holder)
                }
            })
            .start()

        return true
    }

    override fun onRemoveFinished(item: RecyclerView.ViewHolder) {
        item.itemView.alpha = 1f
        super.onRemoveFinished(item)
    }

    companion object {
        const val TAG = "FadeOutDeleteAnimator"
        const val REMOVE_DURATION = 40L
        const val MOVE_DURATION = 120L
        const val WAITE_DURATION = 160L
    }
}
