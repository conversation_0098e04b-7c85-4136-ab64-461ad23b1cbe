package com.sgmw.ksongs.widget

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.sgmw.common.ktx.dp2px


/**
 * isPixel表示是否是像素，
 * 如果为true,leftSpace、topSpace、rightSpace表示像素
 * 如果为false,leftSpace、topSpace、rightSpace表示dp
 */
class SpaceItemDecoration(private var leftSpace: Int = 0, private var topSpace: Int = 0,
                          private var rightSpace: Int = 0, private var bottomSpace: Int = 0,
                          private val isPixel: Boolean = true, private var gridNum: Int = 0,
                          private var gridLayoutLastRowNeedSpace: Boolean = true): RecyclerView.ItemDecoration() {
    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        super.getItemOffsets(outRect, view, parent, state)
        val childAdapterPosition = parent.getChildAdapterPosition(view)

        if(!isPixel){
            leftSpace = view.context.dp2px(leftSpace.toFloat())
            topSpace = view.context.dp2px(topSpace.toFloat())
            rightSpace =view.context. dp2px(rightSpace.toFloat())
            bottomSpace = view.context.dp2px(bottomSpace.toFloat())
        }
        var rightPadding = rightSpace
        var leftPadding = leftSpace
        var tempBottomSpace = bottomSpace
        if (gridNum > 0){
            val i = childAdapterPosition % gridNum
            leftPadding = i * rightSpace / gridNum; // column * (列间距 * (1f / 列数))
            rightPadding = rightSpace - (i + 1) * rightSpace / gridNum; // 列间距 - (column + 1) * (列间距 * (1f /列数))
             // gridLayoutManager最后一行是否需要下边距
            if (isInLastRow(childAdapterPosition,parent.adapter?.itemCount?:0,gridNum) && !gridLayoutLastRowNeedSpace){
                tempBottomSpace = 0
           }else{
                tempBottomSpace = bottomSpace
           }
        }
        parent.layoutManager?.let {
             val size = parent.adapter?.itemCount?:0
           if (it is LinearLayoutManager && it.orientation == RecyclerView.HORIZONTAL && childAdapterPosition == size-1 ){
               rightPadding = 0
           }
        }
        outRect.left = leftPadding
        outRect.top = topSpace
        outRect.right = rightPadding
        outRect.bottom = tempBottomSpace
    }

    fun updateRightSpace(space: Int) {
        rightSpace = space
    }
    private fun isInLastRow(position: Int,dataSize:Int,spanCount:Int ): Boolean {
        val itemCount = dataSize
        val lastRowStart = itemCount - (itemCount % spanCount)
        return position >= lastRowStart
    }

}