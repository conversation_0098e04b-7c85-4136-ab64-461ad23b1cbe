package com.sgmw.ksongs.widget;

import android.content.Context;
import android.util.AttributeSet;

import com.autoai.baseline.support.skincore.SkinManager;
import com.autoai.baseline.support.skincore.SkinViewSupport;
import com.scwang.smart.refresh.footer.ClassicsFooter;
import com.sgmw.common.utils.Log;
import com.sgmw.ksongs.R;

/**
 * <AUTHOR>
 * 自定义底部加载更多 应对昼夜模式切换
 */
public class CustomClassicsFooter extends ClassicsFooter implements SkinViewSupport {
    private static final String TAG = "CustomClassicsFooter";
    public CustomClassicsFooter(Context context) {
        super(context);
    }

    public CustomClassicsFooter(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    public void applySkin() {
        Log.d(TAG,"applySkin --> " + SkinManager.getInstance().isDay());
        if (SkinManager.getInstance().isDay()) {
            setAccentColor(getResources().getColor(R.color.settings_text_color));
        } else {
            setAccentColor(getResources().getColor(R.color.settings_text_color_n));
        }
    }
}
