package com.sgmw.ksongs.widget

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.RectF
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView

/**
 * @author: 董俊帅
 * @time: 2025/7/9
 * @desc: 圆形的ImageView
 */
class CircleImageView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {

    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private var borderWidth = 0f
    private var borderColor = Color.WHITE

    init {
        // 设置Paint属性，实现圆形效果
        paint.isAntiAlias = true
        borderPaint.style = Paint.Style.STROKE
        borderPaint.color = borderColor
        borderPaint.strokeWidth = borderWidth
    }

    /**
     * 设置边框宽度
     */
    fun setBorderWidth(width: Float) {
        borderWidth = width
        borderPaint.strokeWidth = width
        invalidate()
    }

    /**
     * 设置边框颜色
     */
    fun setBorderColor(color: Int) {
        borderColor = color
        borderPaint.color = color
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        val drawable = drawable ?: return
        
        // 获取图片的bitmap
        val bitmap = getBitmapFromDrawable(drawable) ?: return
        
        // 获取View尺寸
        val width = width
        val height = height
        val diameter = minOf(width, height)
        val radius = diameter / 2f
        
        // 创建一个新的bitmap用于绘制圆形
        val output = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val tempCanvas = Canvas(output)
        
        // 绘制圆形
        val rect = RectF(0f, 0f, width.toFloat(), height.toFloat())
        tempCanvas.drawCircle(width / 2f, height / 2f, radius, paint)
        
        // 设置混合模式
        val xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)
        paint.xfermode = xfermode
        
        // 将原图绘制到圆形区域
        tempCanvas.drawBitmap(bitmap, null, rect, paint)
        
        // 重置混合模式
        paint.xfermode = null
        
        // 绘制边框
        if (borderWidth > 0) {
            canvas.drawCircle(width / 2f, height / 2f, radius - borderWidth / 2, borderPaint)
        }
        
        // 将处理后的bitmap绘制到画布上
        canvas.drawBitmap(output, 0f, 0f, null)
        
        // 回收不再使用的Bitmap，避免内存泄漏
        if (bitmap != (drawable as? BitmapDrawable)?.bitmap) {
            bitmap.recycle()
        }
        output.recycle()
    }

    /**
     * 从Drawable获取Bitmap
     */
    private fun getBitmapFromDrawable(drawable: Drawable): Bitmap? {
        return when (drawable) {
            is BitmapDrawable -> drawable.bitmap
            else -> {
                // 创建bitmap
                try {
                    val bitmap = Bitmap.createBitmap(
                        maxOf(drawable.intrinsicWidth, 1),
                        maxOf(drawable.intrinsicHeight, 1),
                        Bitmap.Config.ARGB_8888
                    )
                    
                    // 将drawable绘制到bitmap
                    val canvas = Canvas(bitmap)
                    drawable.setBounds(0, 0, canvas.width, canvas.height)
                    drawable.draw(canvas)
                    
                    bitmap
                } catch (e: Exception) {
                    // 处理可能的OutOfMemoryError
                    null
                }
            }
        }
    }
}