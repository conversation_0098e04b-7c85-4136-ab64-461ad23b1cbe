package com.sgmw.ksongs.api

import com.sgmw.ksongs.model.bean.VipInfoBean
import com.tme.ktv.demo.bean.VipProductBean
import com.tme.ktv.network.GatewayAPI
import com.tme.ktv.network.anno.Gateway
import com.tme.ktv.network.anno.Param
import com.tme.ktv.network.anno.Path
import com.tme.ktv.network.core.TmeCall

@Gateway(GatewayAPI.OPEN_API_BUSINESS)
interface PaymentApi {
    @Path("/karaoke/payment/v2/get_vip_info")
    fun getVipInfo(): TmeCall<VipInfoBean>

    /**
     * 获取会员产品列表
     * 必须在登录后使用
     */
    @Path("/karaoke/payment/v2/get_vip_product")
    fun getVipProduct(
        @Param("sdk_version") sdkVersion: String?,
        @Param("field_mask") fieldMask: Int, //已登录：1，未登录：2
    ): TmeCall<VipProductBean>

}