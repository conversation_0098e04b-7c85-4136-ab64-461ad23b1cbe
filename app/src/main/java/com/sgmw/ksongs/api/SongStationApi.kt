package com.sgmw.ksongs.api

import com.sgmw.ksongs.model.bean.CategoryBean
import com.sgmw.ksongs.model.bean.HotTopicBean
import com.sgmw.ksongs.model.bean.RankingsBean
import com.sgmw.ksongs.model.bean.SingerBean
import com.sgmw.ksongs.model.bean.UserLikeBean
import com.tme.ktv.network.GatewayAPI
import com.tme.ktv.network.anno.Gateway
import com.tme.ktv.network.anno.Param
import com.tme.ktv.network.anno.Path
import com.tme.ktv.network.core.TmeCall

/**
 * @author: 董俊帅
 * @time: 2025/1/18
 * @desc:
 */

@Gateway(GatewayAPI.OPEN_API_BUSINESS)
interface SongStationApi {

    @Path("/karaoke/oper/v2/recommend")
    fun getHotSinger(
        @Param("num") num: Int,
        @Param("type") type: Int
    ): TmeCall<SingerBean>

    /**
     * 综合歌单列表查询 （排行榜接口过期）
     * @param index: 下一条数据的index
     * @param nums: 分页大小+
     * @param playlistId: 歌单id 具体参考 https://docs.qq.com/sheet/DSkRDbW5iTVBEbkdT?tab=BB08J2
     * @param playlistType: 歌单类型 具体参考 https://docs.qq.com/sheet/DSkRDbW5iTVBEbkdT?tab=BB08J2
     */
    @Path("/karaoke/oper/v2/multi_playlist")
    fun getMultiPlaylist(
        @Param("index") nextIndex: Int,
        @Param("nums") pageSize: Int,
        @Param("playlist_id") playlistId: String,
        @Param("playlist_type") playlistType: String,
    ): TmeCall<RankingsBean>

    @Path("/karaoke/user/v2/user_feature_song")
    fun getUserFeatureSongList(
        @Param("sheet_type") userFeatureSongType: Int,
        @Param("start_page") startIndex: Int,
        @Param("page_num") pageSize: Int
    ): TmeCall<UserLikeBean>

    /**
     * @param pageSize: 如果为热门主题，则表示各个热门主题下歌曲数量，直接用1即可
     * @param type: 1.热门伴奏 2.热门歌手 3.热门专题 4.自建唱单
     */
    @Path("/karaoke/oper/v2/recommend")
    fun getRecommendList(
        @Param("type") type: Int,
        @Param("num") pageSize: Int
    ): TmeCall<HotTopicBean>

    /**
     * 获取专题下的歌曲列表
     * 原接口 https://api.kg.qq.com/test/karaoke/oper/v2/get_theme_songs 过期，改为下面的接口
     */
    @Path("/karaoke/oper/v2/multi_playlist")
    fun getMultiThemelist(
        @Param("index") nextIndex: Int,
        @Param("nums") pageSize: Int,
        @Param("playlist_id") playlistId: String,
        @Param("playlist_type") playlistType: String,
    ): TmeCall<RankingsBean>

    /**
     * 获取分类列表
     */
    @Path("/karaoke/oper/v2/get_class_list")
    fun getCategoryList(
        @Param("scene") sceneId: Int = 0,
    ): TmeCall<CategoryBean>

}