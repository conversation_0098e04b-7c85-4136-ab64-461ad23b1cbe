package com.sgmw.ksongs.api

import com.sgmw.ksongs.model.bean.RankingsBean
import com.sgmw.ksongs.model.bean.SearchHotWordsBean
import com.tme.ktv.network.GatewayAPI
import com.tme.ktv.network.anno.Gateway
import com.tme.ktv.network.anno.Param
import com.tme.ktv.network.anno.Path
import com.tme.ktv.network.core.TmeCall

@Gateway(GatewayAPI.OPEN_API_BUSINESS)
interface OperApi {

    @Path("/karaoke/oper/v2/get_hot_words")
    fun getHotWords(): TmeCall<SearchHotWordsBean>

    @Path("/karaoke/oper/v2/multi_playlist")
    fun getMultiPlaylist(
        @Param("index") nextIndex: Int,
        @Param("nums") pageSize: Int,
        @Param("playlist_id") playlistId: String,
        @Param("playlist_type") playlistType: String,
    ): TmeCall<RankingsBean>
}