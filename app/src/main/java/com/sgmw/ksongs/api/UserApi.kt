package com.sgmw.ksongs.api

import com.sgmw.ksongs.model.bean.LimitConfigBean
import com.sgmw.ksongs.model.bean.UserInfoBean
import com.tme.ktv.network.GatewayAPI
import com.tme.ktv.network.anno.Gateway
import com.tme.ktv.network.anno.Param
import com.tme.ktv.network.anno.Path
import com.tme.ktv.network.core.TmeCall

@Gateway(GatewayAPI.OPEN_API_BUSINESS)
interface UserApi {
    @Path("/karaoke/user/v2/get_user_info")
    fun getUserInfo(
        @Param("icon_size") icon_size:Int,
        @Param("need_account_type") need_account_type:Int
    ): TmeCall<UserInfoBean>
    @Path("/karaoke/base/v2/get_free_play_conf")
    fun getLimitConfig(
        @Param("open_id") open_id: String,
        @Param("device_id") device_id: String
    ): TmeCall<LimitConfigBean>

}