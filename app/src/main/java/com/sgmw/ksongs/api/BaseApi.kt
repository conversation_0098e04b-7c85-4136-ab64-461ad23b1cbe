package com.sgmw.ksongs.api

import com.sgmw.ksongs.model.bean.SearchResultBean
import com.sgmw.ksongs.model.bean.SongsBySingerBean
import com.tme.ktv.network.GatewayAPI
import com.tme.ktv.network.anno.Gateway
import com.tme.ktv.network.anno.Param
import com.tme.ktv.network.anno.Path
import com.tme.ktv.network.core.TmeCall

@Gateway(GatewayAPI.OPEN_API_BUSINESS)
interface BaseApi {

    @Path("/karaoke/base/v2/search")
    fun getSearchResult(
        @Param("action") action:Int,
        @Param("content_flag") content_flag:Int,
        @Param("filter_singer_area") filter_singer_area:Int,
        @Param("filter_singer_type") filter_singer_type:Int,
        @Param("start_page") start_page:Int,
        @Param("page_num") page_num:Int,
        @Param("word") word:String
    ) : TmeCall<SearchResultBean>

    @Path("/karaoke/base/v2/get_songs_by_singer")
    fun getSongsBySinger(
        @Param("index") index:Int,
        @Param("page_num") page_num:Int,
        @Param("query") query:String,
        @Param("singer_id") singer_id:String,
        @Param("start_page") start_page:Int
    ) : TmeCall<SongsBySingerBean>

}