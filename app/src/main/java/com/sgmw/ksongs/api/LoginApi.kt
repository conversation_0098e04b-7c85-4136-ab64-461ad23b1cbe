package com.sgmw.ksongs.api

import com.sgmw.ksongs.model.bean.AccessTokenBean
import com.sgmw.ksongs.model.bean.AccessTokenRequest
import com.sgmw.ksongs.model.bean.LightQrCodeBean
import com.sgmw.ksongs.model.bean.LightQrCodeRequest
import com.sgmw.ksongs.model.bean.LightQrStatBean
import com.sgmw.ksongs.model.bean.LightQrStatRequest
import com.sgmw.ksongs.model.bean.RefreshTokenBean
import com.sgmw.ksongs.model.bean.RefreshTokenRequest
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.Headers
import retrofit2.http.POST

interface LoginApi {

    @POST("oauth/v2/light_qr_code")
    @Headers("Content-Type: application/json")
    fun getLightQrCode(
        @Body request: LightQrCodeRequest
    ): Call<LightQrCodeBean>

    @POST("oauth/v2/light_qr_stat")
    @Headers("Content-Type: application/json")
    fun getLightQrStat(
        @Body request: LightQrStatRequest
    ): Call<LightQrStatBean>

    @POST("oauth/v2/access_token")
    @Headers("Content-Type: application/json")
    fun getAccessToken(
        @Body request: AccessTokenRequest
    ): Call<AccessTokenBean>

    @POST("oauth/v2/refresh_token")
    @Headers("Content-Type: application/json")
    fun refreshToken(
        @Header("X-Open-App-ID") appId: String,
        @Header("X-Open-Ts") ts: Long,  // 使用 Long 类型来表示时间戳
        @Body request: RefreshTokenRequest
    ): Call<RefreshTokenBean>

}