package com.sgmw.ksongs.track;


import com.google.gson.Gson;

import java.lang.reflect.Type;

/**
 * <AUTHOR>
 */
public class GsonManager {

    public static final String TAG = GsonManager.class.getSimpleName();
    private static Gson gson;

    private static class GsonManagerHandler {
        private static GsonManager gsonManager = new GsonManager();
    }

    private GsonManager() {
        gson = new Gson();
    }

    public static GsonManager getInstance() {
        return GsonManagerHandler.gsonManager;
    }

    public <T> T fromJson(String json, Type type) {
        return gson.fromJson(json, type);
    }

    public <T> T fromJson(String json, Class<T> clz) {
        return gson.fromJson(json, clz);
    }

    public String toJson(Object src) {
        if (src == null) {
            return "";
        }
        return gson.toJson(src);
    }
}
