package com.sgmw.ksongs.track;

import androidx.annotation.NonNull;

/**
 * @author: 秦荣昌
 * @time: 2025/6/4
 * @desc: 埋点操作类型
 */
public enum ChannelType {
    SCREEN_CLICK("屏幕操作"),
    VOICE_WAKE("语音唤醒"),
    CONTROL_CLICK("方控操作");

    private String content;

    private ChannelType(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    @NonNull
    @Override
    public String toString() {
        return "ChannelType{" +
                "content=" + content +
                '}';
    }
}