package com.sgmw.ksongs.track

import android.content.Context
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.database.ContentObserver
import android.net.Uri
import android.os.Handler
import android.os.SystemProperties
import android.provider.Settings
import android.text.TextUtils
import android.util.Log
import com.sensorsdata.analytics.android.sdk.SAConfigOptions
import com.sensorsdata.analytics.android.sdk.SensorsAnalyticsAutoTrackEventType
import com.sensorsdata.analytics.android.sdk.SensorsDataAPI
import com.sgmw.common.BaseApplication
import com.sgmw.common.utils.MMKVUtils
import com.sgmw.ksongs.BuildConfig
import com.sgmw.ksongs.R
import com.sgmw.ksongs.constant.MMKVConstant
import com.sgmw.ksongs.manager.PermissionHelper
import com.sgmw.ksongs.model.bean.UserInfoVOBean
import com.sgmw.ksongs.model.repository.AccessTokenManager
import com.sgmw.ksongs.ui.songplay.KaraokeConsole
import com.sgmw.ksongs.ui.songplay.KaraokePlayerManager
import com.tme.ktv.api.KtvSdk
import com.tme.ktv.audio.model.SongInfoModel
import ksong.support.player.KtvPlayerConfig
import org.json.JSONObject


/**
 * 神策埋点管理类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/23
 */
object SensorsDataManager {

    private const val TAG: String = "SensorsDataManager"

    // debug 模式的数据接收地址 （测试，测试项目）
    private const val SA_SERVER_URL_DEBUG: String =
        "http://apigw-test.sgmwcloud.com" + ".cn/api/log/appBuryingPoint?project=zhilian_st_202302&token=e0a40d90-01d8-7fa0-08df-0e72e316f751"

    // release 模式的数据接收地址（发版，正式项目）
    private const val SA_SERVER_URL_RELEASE: String =
        "https://buryingpoint.sgmwcloud.com" + ".cn:8001/api/log/appBuryingPoint?project=zhilian_pro_202302&token=155e89db-4e0f-fa73-8e39-761087103f61"

    /**
     * 个人中心通知媒体APP 个人中心登录成功了
     */
    private const val SETTINGS_KEY_LOGIN: String = "user_center/login"
    private var CAR_VIN: String = ""

    private var CAR_PDSN = ""
    private var SOFT_ERSION = ""

    //默认设置L3
    private const val CAR_TYPE = "LV3"
    private var CAR_SERIES = ""
    private var MODULE: String = BigDataConstants.MODULE_NAME

    //同意车机协议字段
    private const val LINGOS_AGREEMENT = "lingos_agree_agreement"

    /**
     * 初始化 SDK 、设置自动采集、设置公共属性
     */
    fun initSensorsDataSDK(context: Context) {
        try {
            Log.i(TAG, "initSensorsDataSDK isDebug: " + BuildConfig.DEBUG)
            // 设置 SAConfigOptions，传入数据接收地址 SA_SERVER_URL
            val testing = Settings.Global.getInt(
                context.contentResolver, "persist.vendor.sgmw.environment" + ".testing", 0
            )
            Log.d(TAG, "environment.testing: $testing")
            val saConfigOptions = SAConfigOptions(if (testing == 1) SA_SERVER_URL_DEBUG else SA_SERVER_URL_RELEASE)

            // 通过 SAConfigOptions 设置神策 SDK 自动采集 options
            saConfigOptions.setAutoTrackEventType(
                SensorsAnalyticsAutoTrackEventType.APP_START or  // 自动采集 App 启动事件
                        SensorsAnalyticsAutoTrackEventType.APP_END or  // 自动采集 App 退出事件
                        SensorsAnalyticsAutoTrackEventType.APP_VIEW_SCREEN or  // 自动采集 App 浏览页面事件
                        SensorsAnalyticsAutoTrackEventType.APP_CLICK
            ) // 自动采集控件点击事件
                .enableLog(BuildConfig.DEBUG) // 开启神策调试日志，默认关闭(调试时，可开启日志)。
                .enableTrackAppCrash() // 开启 crash 采集

            // 需要在主线程初始化神策 SDK
            SensorsDataAPI.startWithConfigOptions(context, saConfigOptions)

            // 初始化 SDK 后，可以获取应用名称设置为公共属性
            val properties = JSONObject()
            properties.put("appName", getAppName(context))
            setSensorsDataAPILoginState()
            SensorsDataAPI.sharedInstance().registerSuperProperties(properties)
            val vin = SystemProperties.get("persist.vendor.sgmw.car.info.code", "")
            if (!TextUtils.isEmpty(vin)) {
                CAR_VIN = vin
            }
            softVersion
            registerUserInfoListener(context)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private var userObserver: ContentObserver = object : ContentObserver(Handler()) {
        override fun onChange(selfChange: Boolean, uri: Uri?) {
            super.onChange(selfChange, uri)
            val mUri = Settings.Global.getUriFor(SETTINGS_KEY_LOGIN)

            if (mUri != null && mUri == uri) {
                setSensorsDataAPILoginState()
            } else {
                Log.i(TAG, "userObserver onChange  no uri  ")
            }
        }
    }

    private fun setSensorsDataAPILoginState() {
        val userInfoStr = Settings.Global.getString(
            BaseApplication.context.contentResolver, SETTINGS_KEY_LOGIN
        )
        if (TextUtils.isEmpty(userInfoStr)) {
            Log.i(TAG, "userObserver onChange  userInfoStr is empty   ")

            val anonymousId = SensorsDataAPI.sharedInstance().anonymousId
            SensorsDataAPI.sharedInstance().logout()
            SensorsDataAPI.sharedInstance().login(anonymousId) //登录id
            return
        }
        val userInfoVOBean = GsonManager.getInstance().fromJson(
            userInfoStr, UserInfoVOBean::class.java
        )
        val userIdStr = userInfoVOBean!!.userIdStr

        if (userInfoVOBean == null || TextUtils.isEmpty(userIdStr)) {
            Log.i(TAG, "userObserver onChange  userInfoVOBean is empty   ")
            return
        }
        Log.i(TAG, "userObserver onChange  change userId ")

        val loginId = SensorsDataAPI.sharedInstance().loginId
        if (userIdStr == loginId) {
            Log.i(TAG, "userObserver onChange  loginId no change ")
            return
        }

        SensorsDataAPI.sharedInstance().logout()
        SensorsDataAPI.sharedInstance().login(userIdStr) //登录id
    }

    private fun registerUserInfoListener(context: Context) {
        Log.i(TAG, "registerUserInfoListener  userObserver")
        context.contentResolver.registerContentObserver(
            Settings.Global.getUriFor(SETTINGS_KEY_LOGIN), false, userObserver
        )
    }

    private val softVersion: Unit
        /* 获取VIN码，PDSN ecu序列号，软件版本号 */
        get() {
            val pdsn = SystemProperties.get("persist.vendor.sgmw.ecu.serial.number")

            val softVersion = SystemProperties.get("ro.build.version.customer")

            var firstFour = ""

            if (softVersion.isNotEmpty()) {
                firstFour = softVersion.substring(0, 5)
            } else {
                // 处理 softVersion 为空的情况
                Log.i(TAG, "softVersion 为空")
            }

            if (!TextUtils.isEmpty(pdsn)) {
                CAR_PDSN = pdsn
            }
            if (!TextUtils.isEmpty(softVersion)) {
                SOFT_ERSION = softVersion
            }
            CAR_SERIES = firstFour

            Log.d(
                TAG, "pdsn == " + pdsn + " softVersion =  " + softVersion + "firstFour == " + firstFour + "CAR_SERIES = $CAR_SERIES"
            )
        }

    /**
     * @param context App 的 Context
     * 获取应用程序名称
     */
    private fun getAppName(context: Context?): CharSequence {
        if (context == null) {
            return ""
        }
        try {
            val packageManager = context.packageManager ?: return ""
            val appInfo = packageManager.getApplicationInfo(
                context.packageName, PackageManager.GET_META_DATA
            )
            //            return appInfo.loadLabel(packageManager);
            //应用名获取错误，先暂且写死
            return context.getString(R.string.app_name)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return ""
    }

    /**
     * @param context App 的 Context
     * @return debug return true,release return false
     * 用于判断是 debug 包，还是 relase 包
     */
    fun isDebugMode(context: Context): Boolean {
        try {
            val info = context.applicationInfo
            return (info.flags and ApplicationInfo.FLAG_DEBUGGABLE) != 0
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        }
    }

    /**
     * 点击事件埋点
     */
    fun trackClickEvent(
        eventCode: String?,
        eventName: String,
        channel: ChannelType = ChannelType.SCREEN_CLICK,
        newpPropertiesInfo: Map<String?, String>? = null
    ) {
        trackEvent(
            BigDataConstants.CLASS_CODE_ELEMENT_CLICK,
            BigDataConstants.CLASS_NAME,
            eventCode,
            eventName,
            BigDataConstants.EVENT_PAGE,
            channel,
            newpPropertiesInfo
        )
    }

    /**
     * 点击添加进已点列表埋点
     */
    fun trackAddDemandClickEvent(
        cardName: String,
        musicName: String,
        singer: String
    ) {
        if (TextUtils.isEmpty(cardName)) {
            return
        }
        trackClickEvent(
            BigDataConstants.EVENT_CODE_WESING_SONG_LIST_ADD,
            BigDataConstants.EVENT_NAME_WESING_SONG_LIST_ADD,
            ChannelType.SCREEN_CLICK,
            newpPropertiesInfo = mapOf(
                BigDataConstants.CARD_NAME to cardName,
                BigDataConstants.MUSIC_NAME to musicName,
                BigDataConstants.SINGER to singer
            )
        )
    }

    /**
     * 事件跟踪
     *
     * @param eventCode
     * @param eventName
     * @param eventPage
     * @param channel
     * @param newpPropertiesInfo 自定义值
     */
    fun <T> trackEvent(
        classCode: String?,
        className: String?,
        eventCode: String?,
        eventName: String,
        eventPage: String?,
        channel: ChannelType?,
        newpPropertiesInfo: Map<String?, T>?
    ) {
        Log.i(TAG, "trackEvent name: $eventName")
        //需求上报埋点前提判断是否同意车机协议
        val isAgree = Settings.Global.getInt(BaseApplication.context.contentResolver, LINGOS_AGREEMENT, 0) == 1
        if (!isAgree) {
            Log.i(TAG, "trackEvent fail, LING_OS not agree")
            return
        }
        try {
            val properties = JSONObject()
            properties.put("class_code", classCode)
            properties.put("class_name", className)
            properties.put("event_code", eventCode)
            properties.put("event_name", eventName)
            properties.put("event_page", eventPage)
            properties.put("tice_version", SOFT_ERSION)
            properties.put("pdsn", CAR_PDSN)
            properties.put("car_type", CAR_TYPE)
            properties.put("car_series", CAR_SERIES)
            if (null != channel) {
                properties.put("channel", channel.content)
            }
            if (null != newpPropertiesInfo && newpPropertiesInfo.size > 0) {
                val event = JSONObject()
                val module = JSONObject()
                for ((key, value) in newpPropertiesInfo) {
                    event.put(key, value)
                }
                module.put(MODULE, event)
                properties.put("new_properties", module.toString())
                if (eventCode == BigDataConstants.EVENT_CODE_WESING_SEARCH) {
                    Log.i(TAG, "trackEvent eventCode wesing_search")
                    return
                }
                Log.i(TAG, "trackEvent properties: $properties")
            }
            if (TextUtils.isEmpty(CAR_VIN)) {
                val vin = SystemProperties.get("persist.vendor.sgmw.car.info.code", "")
                if (!TextUtils.isEmpty(vin)) {
                    CAR_VIN = vin
                }
            }
            properties.put("vin", CAR_VIN)
            SensorsDataAPI.sharedInstance().track(classCode, properties)
        } catch (e: Exception) {
            Log.e(TAG, "trackEvent name: " + eventName + " exception: " + e.message)
        }
    }

    /**
     * 点歌台埋点业务逻辑封装
     * @param cardName 卡片名称
     */
    fun trackSongStationEvent(cardName: String) {
        trackClickEvent(
            BigDataConstants.EVENT_CODE_WESING_SONG_REQUEST_CARD_CLICK,
            BigDataConstants.EVENT_NAME_WESING_SONG_REQUEST_CARD_CLICK,
            ChannelType.SCREEN_CLICK,
            mapOf(
                BigDataConstants.CARD_NAME to cardName
            )
        )
    }

    /**
     * 我的页面 卡片 埋点业务逻辑封装
     * @param cardName 卡片名称
     */
    fun trackMineCardEvent(cardName:String){
        trackClickEvent(
            BigDataConstants.EVENT_CODE_WESING_SONG_MINE_CARD_CLICK,
            BigDataConstants.EVENT_NAME_WESING_SONG_MINE_CARD_CLICK,
            ChannelType.SCREEN_CLICK,
            mapOf(
                BigDataConstants.CARD_NAME to cardName
            )
        )
    }

    /**
     * 搜索埋点业务逻辑封装
     */
    fun trackSearchEvent(cardName:String,keyWord:String,channel:ChannelType = ChannelType.SCREEN_CLICK){
        trackClickEvent(
            BigDataConstants.EVENT_CODE_WESING_SEARCH,
            BigDataConstants.EVENT_NAME_WESING_SEARCH,
            channel,
            mapOf(
                BigDataConstants.CARD_NAME to cardName,
                BigDataConstants.LATEST_SEARCH_KEYWORD to keyWord
            )
        )
    }

    /**
     * 点歌 埋点业务封装
     * 埋点时机：点击歌曲进入播放页或弹出购买VIP时触发
     * @param cardName 歌曲来源（在哪点击的）
     * @param musicName 歌曲名
     * @param singer 歌手名字
     */
    fun trackPlaySongEvent(cardName:String,musicName:String,singer:String){
        if (TextUtils.isEmpty(cardName)) {
            return
        }
        trackClickEvent(
            BigDataConstants.EVENT_CODE_WESING_SONG_CHOOSE,
            BigDataConstants.EVENT_NAME_WESING_SONG_CHOOSE,
            ChannelType.SCREEN_CLICK,
            mapOf(
                BigDataConstants.CARD_NAME to cardName,
                BigDataConstants.MUSIC_NAME to musicName,
                BigDataConstants.SINGER to singer
            )
        )
    }

    /**
     * 演唱歌曲信息埋点业务逻辑封装
     * @param setValue 0/1/2（麦克风权限未授权/连接手持麦克风/连接顶麦）
     * @param musicName 歌名
     * @param singer 歌手
     * @param bindAccount VIP登录/非VIP登录
     * @param musicSource MV清晰度（标清/高清/超清/无）
     * @param eventDuration 演唱时长
     * @param appSdkVersion sdk版本
     */
    fun trackSongInfoEvent() {
        val micState = if (PermissionHelper.isHaveMicrophonePermission()) {
            "2"
        } else {
            "0"
        }
        val songName = KaraokeConsole.currSongInfo?.song_name ?: ""
        val singer = KaraokeConsole.currSongInfo?.singer_name ?: ""
        val videoQuality = KtvPlayerConfig.getInstance().videoQuality
        val musicSource = when (videoQuality) {
            SongInfoModel.MV_480P -> {
                BaseApplication.context.getString(R.string.standard_definition)
            }

            SongInfoModel.MV_720P -> {
                BaseApplication.context.getString(R.string.high_definition)
            }

            SongInfoModel.MV_1080P -> {
                BaseApplication.context.getString(R.string.super_definition)
            }

            else -> {
                BaseApplication.context.getString(R.string.no_definition)
            }
        }
        val bindAccount = if (MMKVUtils.getBoolean(MMKVConstant.IS_VIP, false)) {
            BaseApplication.context.getString(R.string.event_vip_login)
        } else {
            BaseApplication.context.getString(R.string.event_not_vip_login)
        }
        val duration = KaraokePlayerManager.getPlayer().duration.toString()
        val appSdkVersion = KtvSdk.getVersionName()
        Log.d(
            TAG, "trackSongInfoEvent micState: $micState songName: $songName singer: " +
                    "$singer bindAccount: $bindAccount musicSource: $musicSource duration: " +
                    "$duration appSdkVersion: $appSdkVersion"
        )
        trackClickEvent(
            BigDataConstants.EVENT_CODE_WESING_KARAOKE_SONG_INFO,
            BigDataConstants.EVENT_NAME_WESING_KARAOKE_SONG_INFO,
            newpPropertiesInfo = mapOf(
                BigDataConstants.SET_VALUE to micState,
                BigDataConstants.MUSIC_NAME to songName,
                BigDataConstants.SINGER to singer,
                BigDataConstants.BIND_ACCOUNT to bindAccount,
                BigDataConstants.MUSIC_SOURCE to musicSource,
                BigDataConstants.EVENT_DURATION to duration,
                BigDataConstants.APP_SDK_VERSION to appSdkVersion,
            )
        )
    }

    /**
     * 浏览MV页面时长埋点
     */
    fun trackBrowseMvPage(duration: Long) {
        trackEvent(
            BigDataConstants.CLASS_CODE_PAGE_BROWSE,
            BigDataConstants.CLASS_NAME,
            BigDataConstants.EVENT_CODE_BROWSE_WESING_MV_PAGE,
            BigDataConstants.EVENT_NAME_BROWSE_WESING_MV_PAGE,
            BigDataConstants.EVENT_PAGE,
            channel = null,
            newpPropertiesInfo = mapOf(
                BigDataConstants.EVENT_DURATION to duration.toString()
            )
        )
    }

    /**
     * 浏览MV页面时长埋点
     */
    fun trackBrowseAppPage(duration: Long) {
        val bindAccount = if (AccessTokenManager.isLogin()) {
            BaseApplication.context.getString(R.string.event_vip_un_login)
        } else {
            if (MMKVUtils.getBoolean(MMKVConstant.IS_VIP, false)) {
                BaseApplication.context.getString(R.string.event_vip_login)
            } else {
                BaseApplication.context.getString(R.string.event_not_vip_login)
            }
        }

        trackEvent(
            BigDataConstants.CLASS_CODE_PAGE_BROWSE,
            BigDataConstants.CLASS_NAME,
            BigDataConstants.EVENT_CODE_BROWSE_WESING_APP_PAGE,
            BigDataConstants.EVENT_NAME_BROWSE_WESING_APP_PAGE,
            BigDataConstants.EVENT_PAGE,
            channel = null,
            newpPropertiesInfo = mapOf(
                BigDataConstants.BIND_ACCOUNT to bindAccount,
                BigDataConstants.EVENT_DURATION to duration.toString()
            )
        )
    }
}
