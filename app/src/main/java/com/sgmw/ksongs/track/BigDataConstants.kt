package com.sgmw.ksongs.track

/**
 * 公共类中数据埋点key value
 */
object BigDataConstants {

    /******************************************埋点公共常量*****************************************************/

    /**
     * 点击类型 class code
     */
    const val CLASS_CODE_ELEMENT_CLICK = "wesing_element_click"
    /**
     * 浏览累类型 class code
     */
    const val CLASS_CODE_PAGE_BROWSE = "wesing_page_browse"

    /**
     * 元素名称
     */
    const val CLASS_NAME = "全民K歌元素"

    const val EVENT_PAGE = "全民K歌"

    /**
     * 模块英文名字, 扩展特殊字段时使用 用来做 json的key
     */
    const val MODULE_NAME  = "wesing"

    const val SPLIT = "|"

    const val AGE_RANK = "年代榜"
    const val HOT_RANK = "排行榜"

    const val CARD_NAME_SEARCH = "模糊搜索"
    const val CARD_NAME_HISTORY_SEARCH = "历史搜索"
    const val CARD_NAME_SEARCH_SUGGESTION = "搜索推荐"
    const val CARD_NAME_SEARCH_RESULT  = "搜索结果"
    const val CARD_NAME_ALREADY_DEMAND  = "已点列表"
    const val CARD_NAME_MINE = "我的"
    const val CARD_NAME_SONGSTATION = "点歌台"

    /***************************************event code AND event_name **************************************************************/


    const val EVENT_CODE_WESING_SONG_REQUEST_CARD_CLICK = "wesing_song_request_card_click"
    const val EVENT_NAME_WESING_SONG_REQUEST_CARD_CLICK = "点击全民K歌点歌台栏内卡片"

    const val EVENT_CODE_WESING_SONG_MINE_CARD_CLICK  = "wesing_song_mine_card_click"
    const val EVENT_NAME_WESING_SONG_MINE_CARD_CLICK  = "点击全民K歌我的栏内卡片"

    const val EVENT_CODE_WESING_SEARCH = "wesing_search"
    const val EVENT_NAME_WESING_SEARCH = "搜索全民K歌"

    const val EVENT_CODE_WESING_SONG_CHOOSE = "wesing_song_choose"
    const val EVENT_NAME_WESING_SONG_CHOOSE = "点歌"

    const val EVENT_CODE_WESING_SONG_LIST_ADD = "wesing_song_list_add "
    const val EVENT_NAME_WESING_SONG_LIST_ADD = "加入已点 "

    const val EVENT_CODE_WESING_LYRIC_MV_SWITCH = "wesing_lyric_mv_switch"
    const val EVENT_NAME_WESING_LYRIC_MV_SWITCH = "切换歌词MV"

    const val EVENT_CODE_WESING_SONG_NEXT = "wesing_song_next"
    const val EVENT_NAME_WESING_SONG_NEXT = "切歌"

    const val EVENT_CODE_WESING_ORIGINAL_ACCOMPANY_SONG_SWITCH = "wesing_original_accompany_song_switch"
    const val EVENT_NAME_WESING_ORIGINAL_ACCOMPANY_SONG_SWITCH = "切换原唱"

    const val EVENT_CODE_WESING_SCORE_SET = "wesing_score_set"
    const val EVENT_NAME_WESING_SCORE_SET = "评分开关设置"

    const val EVENT_CODE_WESING_PITCH_TUNER_SET = "wesing_pitch_tuner_set"
    const val EVENT_NAME_WESING_PITCH_TUNER_SET = "音准器开关设置"

    const val EVENT_CODE_WESING_MIXING_CONSOLE_SET = "wesing_mixing_console_set"
    const val EVENT_NAME_WESING_MIXING_CONSOLE_SET = "调音台设置"

    const val EVENT_CODE_WESING_SONG_REPLAY = "wesing_song_replay"
    const val EVENT_NAME_WESING_SONG_REPLAY = "重唱"

    const val EVENT_CODE_WESING_SONG_PAUSE ="wesing_song_pause"
    const val EVENT_NAME_WESING_SONG_PAUSE ="暂停K歌"

    const val EVENT_CODE_WESING_SONG_CONTINUE ="wesing_song_continue"
    const val EVENT_NAME_WESING_SONG_CONTINUE ="继续K歌"

    const val EVENT_CODE_WESING_KARAOKE_VOLUME_ADJUST ="wesing_karaoke_volume_adjust"
    const val EVENT_NAME_WESING_KARAOKE_VOLUME_ADJUST ="调节全民K歌音量"

    const val EVENT_CODE_WESING_KARAOKE_SONG_INFO ="wesing_karaoke_song_info"
    const val EVENT_NAME_WESING_KARAOKE_SONG_INFO ="演唱歌曲信息"

    const val EVENT_CODE_WESING_MV_QUALITY_SWITCH ="wesing_mv_quality_switch"
    const val EVENT_NAME_WESING_MV_QUALITY_SWITCH ="切换MV画质"

    const val EVENT_CODE_WESING_VIP_BUY_SUCCESS ="wesing_vip_buy_success"
    const val EVENT_NAME_WESING_VIP_BUY_SUCCESS ="购买VIP成功"

    const val EVENT_CODE_WESING_CACHE_CLEAR ="wesing_cache_clear"
    const val EVENT_NAME_WESING_CACHE_CLEAR ="清除全民K歌缓存"

    const val EVENT_CODE_BROWSE_WESING_MV_PAGE ="browse_wesing_mv_page"
    const val EVENT_NAME_BROWSE_WESING_MV_PAGE ="浏览全民K歌播放页"

    const val EVENT_CODE_BROWSE_WESING_APP_PAGE ="browse_wesing_app_page"
    const val EVENT_NAME_BROWSE_WESING_APP_PAGE ="浏览全民K歌"



   /****************************************** 埋点特殊字段 **********************************************/

    /**
     * 时长
     */
    const val EVENT_DURATION = "event_duration"

    /**
     * 含义比较丰富 有表示来源
     */
    const val CARD_NAME = "card_name"

    /**
     * 搜索关键字
     */
    const val LATEST_SEARCH_KEYWORD = "latest_search_keyword"
    /**
     * 歌名
     */
    const val MUSIC_NAME = "music_name"

    /**
     * 歌手
     */
    const val SINGER = "singer"
    const val SET_VALUE = "set_value"
    const val SET_VALUE_2 = "set_value_2"

    /**
     * VIP登录/非VIP登录
     */
    const val BIND_ACCOUNT = "bind_account"

    /**
     * MV清晰度（标清/高清/超清/无）
     */
    const val MUSIC_SOURCE = "music_source"

    /**
     * sdk版本
     */
    const val APP_SDK_VERSION = "app_sdk_version"

}