package com.sgmw.ksongs.ui.hottopic

import android.os.Bundle
import androidx.appcompat.widget.TooltipCompat
import androidx.navigation.fragment.findNavController
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.mvvm.v.BaseFrameFragment
import com.sgmw.ksongs.databinding.FragmentHotTopicHomeBinding
import com.sgmw.ksongs.model.bean.HotTopicBean
import com.sgmw.ksongs.track.BigDataConstants
import com.sgmw.ksongs.track.SensorsDataManager
import com.sgmw.ksongs.utils.RANK_TYPE_AGE_LIST
import com.sgmw.ksongs.utils.setupTabClickEvents
import java.util.ArrayList

/**
 * 热门专题
 */
class HotTopicHomeFragment : BaseFrameFragment<FragmentHotTopicHomeBinding ,HotTopicHomeViewModel>() {


    companion object {

        private const val THEME_LIST = "theme_list"
        private const val FIRST_SHOW_POSITION = "first_position"
        const val PARENT_TYPE = "热门专题"

        fun createBundle(themes: ArrayList<HotTopicBean.Theme>, firstPosition: Int = 0): Bundle {
            return Bundle().apply {
                putParcelableArrayList(THEME_LIST, themes)
                putInt(FIRST_SHOW_POSITION, firstPosition)
            }
        }

    }

    override fun FragmentHotTopicHomeBinding.initView() {
        val themeList = arguments?.getParcelableArrayList<HotTopicBean.Theme>(THEME_LIST) ?: emptyList()
        val adapter = HotTopicHomePageAdapter(this@HotTopicHomeFragment, themeList)
        viewPager.adapter = adapter
        TabLayoutMediator(tabLayout, viewPager, true, true) { tab, position ->
            tab.text = adapter.getTitle(position)
        }.attach()
        for (i in 0 until tabLayout.tabCount) {
            val tab = tabLayout.getTabAt(i)
            tab?.let {
                TooltipCompat.setTooltipText(it.view, null)
            }
        }
        tabLayout.setupTabClickEvents(viewPager)
        viewPager.setCurrentItem(arguments?.getInt(FIRST_SHOW_POSITION) ?: 0, false)

        ivBack.setOnSingleClickListener {
            findNavController().popBackStack()
        }
        tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener{
            override fun onTabSelected(tab: TabLayout.Tab?) {
                tab?.let {
                    SensorsDataManager.trackSongStationEvent(PARENT_TYPE + BigDataConstants.SPLIT + it.text.toString())
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
            }

        })
    }

    override fun initRequestData() {

    }


}