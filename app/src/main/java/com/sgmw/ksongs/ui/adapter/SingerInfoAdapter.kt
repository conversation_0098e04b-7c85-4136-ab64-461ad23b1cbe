package com.sgmw.ksongs.ui.adapter

import android.widget.ImageView
import android.widget.TextView
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.sgmw.common.utils.GlideUtil
import com.sgmw.ksongs.R
import com.sgmw.ksongs.model.bean.SingerBean
import com.sgmw.ksongs.model.bean.SongInfoBean

class SingerInfoAdapter : BaseAdapter<SingerBean.Singer>(R.layout.item_sub_song_station_singer) {
    override fun convert(holder: BaseViewHolder, item: SingerBean.Singer) {
        holder.getView<TextView>(R.id.tv_singer_name).text = item.singer_name
        val singerIcon =  holder.getView<ImageView>(R.id.ivSingerIcon)
//        GlideUtil.loadRoundCornerImage(holder.itemView.context, item.singer_cover, singerIcon, 90)
        GlideUtil.loadRoundCornerImage(holder.itemView.context, item.singer_cover,
            singerIcon, 90, R.mipmap.icon_music_default_bg, R.mipmap.icon_music_default_bg)
    }
}