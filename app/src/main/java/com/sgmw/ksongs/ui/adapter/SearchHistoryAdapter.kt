package com.sgmw.ksongs.ui.adapter
import android.view.View.GONE
import android.widget.ImageView
import android.widget.TextView
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.sgmw.ksongs.R

class SearchHistoryAdapter :BaseAdapter<String>(R.layout.item_search_history) {

    private var isDeleteVisible:Int = GONE

    override fun convert(holder: BaseViewHolder, item: String) {
        holder.getView<TextView>(R.id.tv_search_history).text = item
        holder.getView<ImageView>(R.id.iv_search_history_delete).visibility = isDeleteVisible
    }

    fun setDeleteVisible(visible:Int){
        isDeleteVisible = visible
        notifyDataSetChanged()
    }

    fun isDeleteVisible(): Int {
        return isDeleteVisible
    }
}