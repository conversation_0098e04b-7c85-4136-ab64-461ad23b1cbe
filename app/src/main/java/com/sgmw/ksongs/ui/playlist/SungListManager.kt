package com.sgmw.ksongs.ui.playlist

import androidx.lifecycle.MutableLiveData
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.model.bean.SongInfoBean

/**
 * @author: 董俊帅
 * @time: 2025/2/24
 * @desc: 已播列表管理
 * 每次重启APP后,已播列表都会清空
 * 优化版本：增强数据一致性检查和日志记录
 */
object SungListManager {

    private const val TAG = "SungListManager"

    private val _sungList = mutableListOf<SongInfoBean>()

    private var sungList = MutableLiveData<MutableList<SongInfoBean>>()

    fun getSungListLiveData(): MutableLiveData<MutableList<SongInfoBean>> {
        return sungList
    }

    /**
     * 添加到已播列表
     * 最新唱完的歌曲，添加到列表最上面
     * 使用synchronized确保线程安全，防止并发操作导致数据不一致
     * 优化版本：增强去重机制和日志记录，修复快速切歌时的重复添加问题
     */
    @Synchronized
    fun addToSungList(songInfoBean: SongInfoBean) {
        // 数据验证
        if (songInfoBean.song_name.isBlank() || songInfoBean.singer_name.isBlank()) {
            Log.e(TAG, "尝试添加无效歌曲到已唱列表: song_id=${songInfoBean.song_id}, song_name='${songInfoBean.song_name}', singer_name='${songInfoBean.singer_name}'")
            return
        }

        // 检查是否已存在相同song_id的歌曲
        val existingIndex = _sungList.indexOfFirst { it.song_id == songInfoBean.song_id }
        if (existingIndex != -1) {
            // 修复：如果歌曲已存在，先移除再添加到顶部，确保顺序正确
            Log.d(TAG, "addToSungList: 歌曲 ${songInfoBean.song_name} 已在已唱列表中，移动到顶部")
            _sungList.removeAt(existingIndex)
        }

        _sungList.add(0, songInfoBean)
        sungList.postValue(_sungList.toMutableList()) // 创建副本避免外部修改

        Log.d(TAG, "addToSungList: 成功添加歌曲 ${songInfoBean.song_name} 到已唱列表，当前列表大小: ${_sungList.size}")
    }

    /**
     * 从已唱列表移除歌曲
     * 优化版本：增强日志记录
     */
    @Synchronized
    fun removeFromSungList(songInfoBean: SongInfoBean) {
        val index = _sungList.indexOfFirst { it.song_id == songInfoBean.song_id }
        if (index != -1) {
            _sungList.removeAt(index)
            sungList.postValue(_sungList.toMutableList()) // 创建副本避免外部修改
            Log.d(TAG, "removeFromSungList: 成功从已唱列表移除歌曲 ${songInfoBean.song_name}，当前列表大小: ${_sungList.size}")
        } else {
            Log.d(TAG, "removeFromSungList: 歌曲 ${songInfoBean.song_name} 不在已唱列表中，无需移除")
        }
    }

    /**
     * 根据歌曲ID从已唱列表移除歌曲
     * 优化版本：增强日志记录
     */
    @Synchronized
    fun removeFromSungListById(mid: String) {
        val index = _sungList.indexOfFirst { it.song_id == mid }
        if (index != -1) {
            val removedSong = _sungList.removeAt(index)
            sungList.postValue(_sungList.toMutableList()) // 创建副本避免外部修改
            Log.d(TAG, "removeFromSungListById: 成功从已唱列表移除歌曲 ${removedSong.song_name}，当前列表大小: ${_sungList.size}")
        } else {
            Log.d(TAG, "removeFromSungListById: 歌曲ID $mid 不在已唱列表中，无需移除")
        }
    }

}