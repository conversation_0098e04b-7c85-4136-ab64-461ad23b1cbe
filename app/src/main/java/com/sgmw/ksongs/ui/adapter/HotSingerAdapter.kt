package com.sgmw.ksongs.ui.adapter

import android.widget.ImageView
import android.widget.TextView
import com.bumptech.glide.Glide
import com.bumptech.glide.Priority
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.sgmw.common.utils.GlideRoundTransform
import com.sgmw.common.utils.GlideUtil
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.R
import com.sgmw.ksongs.model.bean.SingerBean

class HotSingerAdapter :BaseAdapter<SingerBean.Singer>(R.layout.item_sub_song_station_singer) {
    private val radius = 80
    override fun convert(holder: BaseViewHolder, item: SingerBean.Singer) {
        holder.getView<TextView>(R.id.tv_singer_name).text = item.singer_name
       val singerIcon =  holder.getView<ImageView>(R.id.ivSingerIcon)
        Log.d("singer_cover",item.singer_cover  )
        GlideUtil.loadRoundCornerImage2(holder.itemView.context, item.singer_cover,
            singerIcon, radius, R.mipmap.icon_music_default_bg, R.mipmap.icon_music_default_bg)
    }
}