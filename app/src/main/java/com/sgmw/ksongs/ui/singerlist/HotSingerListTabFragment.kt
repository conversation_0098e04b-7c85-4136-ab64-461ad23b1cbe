package com.sgmw.ksongs.ui.singerlist

import androidx.appcompat.widget.TooltipCompat
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayout.OnTabSelectedListener
import com.google.android.material.tabs.TabLayoutMediator
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.mvvm.v.BaseFrameFragment
import com.sgmw.ksongs.R
import com.sgmw.ksongs.databinding.FragmentHotSingerListTabBinding
import com.sgmw.ksongs.track.BigDataConstants.SPLIT
import com.sgmw.ksongs.track.SensorsDataManager
import com.sgmw.ksongs.utils.setupTabClickEvents
import com.sgmw.ksongs.viewmodel.singerlist.HotSingerListViewModel

class HotSingerListTabFragment : BaseFrameFragment<FragmentHotSingerListTabBinding, HotSingerListViewModel>() {

    private val mTitles = arrayOf("全部", "港台","内地","日韩","欧美","其他")

    override fun FragmentHotSingerListTabBinding.initView() {
        mBinding?.let { binding ->

            binding.ivBack.setOnSingleClickListener {
                findNavController().popBackStack()
            }

            binding.viewPager.adapter = object : FragmentStateAdapter(this@HotSingerListTabFragment) {
                override fun getItemCount(): Int {
                    return mTitles.size
                }

                override fun createFragment(position: Int): Fragment {
                    var filterSingerArea = 0
                    when(position){
                        1,2,3,4,5 -> filterSingerArea = position - 1
                        0 -> filterSingerArea = 100
                    }
                    return HotSingerListFragment(filterSingerArea,mTitles[position])
                }

            }
            binding.let {
                TabLayoutMediator(it.tabLayout, it.viewPager) { tab, position ->
                    tab.text = mTitles[position]
                }.attach()
            }
            for (i in 0 until binding.tabLayout.tabCount) {
                val tab = binding.tabLayout.getTabAt(i)
                tab?.let {
                    TooltipCompat.setTooltipText(it.view, null)
                }
            }
            tabLayout.setupTabClickEvents(viewPager)

            binding.tabLayout.addOnTabSelectedListener(object : OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                   tab?.let {
                       SensorsDataManager.trackSongStationEvent(getString(R.string.home_title_hot_singer) + SPLIT + it.text.toString())
                   }
                }

                override fun onTabUnselected(tab: TabLayout.Tab?) {

                }

                override fun onTabReselected(tab: TabLayout.Tab?) {

                }

            })
        }
    }

    override fun initRequestData() {

    }
}