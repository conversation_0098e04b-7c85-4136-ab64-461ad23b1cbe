package com.sgmw.ksongs.ui.adapter

import android.graphics.drawable.AnimationDrawable
import android.view.ViewGroup
import androidx.annotation.LayoutRes
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.util.getItemView
import com.chad.library.adapter.base.viewholder.BaseViewHolder

abstract class BaseAdapter<T> @JvmOverloads constructor(
    @LayoutRes private val layoutResId: Int,
    data: MutableList<T>? = null
) : BaseQuickAdapter<T, BaseViewHolder>(layoutResId, data){
    // BaseQuickAdapter三方库中会走反射流程，实际代码中就是BaseViewHolder，直接创建，减少性能损耗
    override fun onCreateDefViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder {
        return BaseViewHolder(parent.getItemView(layoutResId))
    }

    // 使用可空变量来避免lazy初始化时的context访问问题
    private var playAnimationDrawable: AnimationDrawable? = null

    protected val playAnimation: AnimationDrawable?
        get() {
            if (playAnimationDrawable == null) {
                try {
                    // 只有在RecyclerView attached的情况下才初始化动画
                    playAnimationDrawable = ContextCompat.getDrawable(context, com.sgmw.common.R.drawable.kw_animation_music_play_loop) as? AnimationDrawable
                } catch (e: Exception) {
                    // 如果获取context失败，返回null
                    return null
                }
            }
            return playAnimationDrawable
        }

    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        super.onDetachedFromRecyclerView(recyclerView)
        // 安全地停止动画，避免崩溃
        playAnimationDrawable?.stop()
        playAnimationDrawable = null
    }


}