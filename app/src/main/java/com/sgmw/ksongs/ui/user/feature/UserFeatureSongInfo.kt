package com.sgmw.ksongs.ui.user.feature

import androidx.annotation.IntDef
import com.sgmw.ksongs.utils.DEFAULT_PAGE_LIST_SIZE


/**
 * 猜你喜欢
 */
const val USE_FEATURE_SONG_TYPE_LIKE = 3

@IntDef(
    USE_FEATURE_SONG_TYPE_LIKE,
)
@Retention(AnnotationRetention.SOURCE)
annotation class UserFeatureSongType{}

/**
 * 用户个推歌单默认起始index
 */
const val USER_FEATURE_DEFAULT_INDEX = 1

/**
 * 用户个推歌单默认分页大小
 */
const val USER_FEATURE_SONG_PAGE_SIZE = DEFAULT_PAGE_LIST_SIZE

/**
 * 首页搜索分页大小
 */
const val USER_FEATURE_HOME_LIKE_PAGE_SIZE = 6