package com.sgmw.ksongs.ui.hottopic

import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.sgmw.ksongs.model.bean.HotTopicBean
import com.sgmw.ksongs.track.BigDataConstants.SPLIT


class HotTopicHomePageAdapter(fragment: Fragment, private val themeList: List<HotTopicBean.Theme>) : FragmentStateAdapter(fragment) {


    override fun getItemCount(): Int {
        return themeList.size
    }

    override fun createFragment(position: Int): Fragment {
        return HotTopicListFragment.newInstance(themeList[position]?.theme_id,HotTopicHomeFragment.PARENT_TYPE + SPLIT + getTitle(position))
    }

    fun getTitle(position: Int): String {
        return themeList.getOrNull(position)?.theme_name ?: ""
    }

}