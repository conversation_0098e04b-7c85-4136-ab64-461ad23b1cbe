package com.sgmw.ksongs.ui.user.feature

import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.sgmw.common.ktx.dp2px
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.mvvm.v.BaseFrameFragment
import com.sgmw.ksongs.R
import com.sgmw.ksongs.databinding.FragmentUserFeatureSongBinding
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.onFailure
import com.sgmw.ksongs.model.bean.onSuccess
import com.sgmw.ksongs.ui.adapter.RankListAdapter
import com.sgmw.ksongs.ui.songplay.KaraokeConsole
import com.sgmw.ksongs.ui.songplay.KaraokePlayerManager
import com.sgmw.ksongs.utils.showToast
import com.sgmw.ksongs.widget.AccessibilityLinearLayoutManager
import com.sgmw.ksongs.widget.SpaceItemDecoration

/**
 * 用户个推列表
 * 默认为：猜你喜欢
 */
class UserFeatureSongFragment : BaseFrameFragment<FragmentUserFeatureSongBinding, UserFeatureSongViewModel>() {


    private val adapter = RankListAdapter()

    override fun FragmentUserFeatureSongBinding.initView() {
        rvUserFeatureSong.layoutManager = AccessibilityLinearLayoutManager(context)
        rvUserFeatureSong.adapter = adapter

        srl.setOnRefreshListener {
            mViewModel?.getSongList(Operation.Refresh)
        }
        srl.setOnLoadMoreListener {
            mViewModel?.getSongList(Operation.LoadMore)
        }
        ivBack.setOnSingleClickListener {
            findNavController().popBackStack()
        }
        adapter.setCardName(getString(R.string.title_user_like))
        adapter.setOnItemClickListener { _, view, position ->
            KaraokePlayerManager.playSong(this@UserFeatureSongFragment, adapter.getItem(position),getString(R.string.title_user_like))
        }

    }
    private fun setErrorEntryListener() {
        mBinding?.stateLayout?.setErrorRetryClickListener {
            mBinding?.stateLayout?.showLoading()
            mViewModel?.getSongList(Operation.NewData)
        }
    }
    override fun initObserve() {
        super.initObserve()
        mViewModel?.userFeatureSongResult?.observe(this) {
            it.onSuccess { value, operation ->
                when (operation) {
                    Operation.NewData -> {
                        adapter.setList(value?.map_song_info?.songList)
                        if (value?.map_has_more?.hasMore == true) {
                            mBinding?.srl?.setNoMoreData(false)
                        } else {
                            mBinding?.srl?.setNoMoreData(true)
                        }
                    }
                    Operation.Refresh -> {
                        adapter.setList(value?.map_song_info?.songList)
                        mBinding?.srl?.finishRefresh()
                        if (value?.map_has_more?.hasMore == true) {
                            mBinding?.srl?.setNoMoreData(false)
                        } else {
                            mBinding?.srl?.setNoMoreData(true)
                        }
                    }
                    Operation.LoadMore -> {
                        adapter.addData(value?.map_song_info?.songList ?: emptyList())
                        if (value?.map_has_more?.hasMore == true) {
                            mBinding?.srl?.finishLoadMore()
                        } else {
                            mBinding?.srl?.finishLoadMoreWithNoMoreData()
                        }
                    }
                    Operation.UpdateStatus -> {
                        adapter.setList(value?.map_song_info?.songList)
                    }
                    else -> {}
                }
                if (adapter.data.isNullOrEmpty()) {
                    mBinding?.stateLayout?.showEmpty()
                } else {
                    mBinding?.stateLayout?.showContent()
                }
            }.onFailure { _, operation ->
                when(operation) {
                    Operation.NewData -> {
                        mBinding?.stateLayout?.showError()
                        setErrorEntryListener()
                    }
                    Operation.Refresh -> {
                        mBinding?.srl?.finishRefresh()
                        showToast(R.string.refresh_failed)
                    }
                    Operation.LoadMore -> {
                        mBinding?.srl?.finishLoadMore()
                        showToast(R.string.load_more_failed)
                    }
                    Operation.UpdateStatus -> {

                    }
                    else -> {}
                }
            }
        }

        KaraokeConsole.playState.observe(this) {
            adapter.notifyDataSetChanged()
        }

        mViewModel?.collectSongChangeLiveData?.observe(this) {
            if (adapter.data.isNotEmpty()) {
                mViewModel?.updateCollectStatus(adapter.data)
            }
        }
        mViewModel?.demandSongInfo?.observe(this) {
            if (adapter.data.isNotEmpty()) {
                mViewModel?.updateDemandStatus(adapter.data)
            }
        }

    }

    override fun initRequestData() {
        mViewModel?.getSongList(Operation.NewData)
    }

    override fun onDestroyView() {
        // 清理RecyclerView和Adapter，防止内存泄露
        mBinding?.rvUserFeatureSong?.adapter = null
        super.onDestroyView()
    }

}