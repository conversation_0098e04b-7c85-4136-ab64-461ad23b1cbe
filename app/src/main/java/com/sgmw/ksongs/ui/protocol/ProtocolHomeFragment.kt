package com.sgmw.ksongs.ui.protocol

import android.os.Bundle
import androidx.appcompat.widget.TooltipCompat
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.google.android.material.tabs.TabLayoutMediator
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.mvvm.v.BaseFrameFragment
import com.sgmw.ksongs.R
import com.sgmw.ksongs.databinding.FragmentProtocolHomeBinding
import com.sgmw.ksongs.utils.ViewPagerUtil
import com.sgmw.ksongs.viewmodel.protocol.ProtocolViewModel

class ProtocolHomeFragment : BaseFrameFragment<FragmentProtocolHomeBinding, ProtocolViewModel>() {

    companion object {
        const val TAB_INDEX = "tabIndex"
        const val SERVICE_TERM = 0
        const val PRIVACY_POLICY = 1
        fun createBundle(tab: Int): Bundle {
            return Bundle().apply {
                putInt(TAB_INDEX, tab)
            }
        }
    }

    override fun FragmentProtocolHomeBinding.initView() {
        mBinding?.let { binding ->
            val tabTitle =
                listOf(getString(R.string.service_term), getString(R.string.privacy_policy))

            val tabFragments = mutableListOf<ProtocolWebFragment>()

            tabFragments.add(ProtocolWebFragment.newInstance(getString(R.string.service_term_url)))
            tabFragments.add(ProtocolWebFragment.newInstance(getString(R.string.privacy_policy_url)))

            var tabIndex = SERVICE_TERM

            arguments?.let {
                tabIndex = it.getInt(TAB_INDEX)
            }

            binding.ivBack.setOnSingleClickListener {
                if (binding.tabLayout.selectedTabPosition == 1) {
                    if (!tabFragments[1].goBack()) {
                        findNavController().popBackStack()
                    }
                } else {
                    findNavController().popBackStack()
                }
            }

            binding.viewPager.adapter = object : FragmentStateAdapter(this@ProtocolHomeFragment) {
                override fun getItemCount(): Int {
                    return tabTitle.size
                }

                override fun createFragment(position: Int): Fragment {
                    return tabFragments[position]
                }
            }

            TabLayoutMediator(tabLayout, viewPager,true,true) { tab, position ->
                tab.text = tabTitle[position]
            }.attach()
            binding.viewPager.setCurrentItem(tabIndex,false)
            for (i in 0 until tabLayout.tabCount) {
                val tab = tabLayout.getTabAt(i)
                tab?.let {
                    TooltipCompat.setTooltipText(it.view, null)
                }
            }

            viewPager.offscreenPageLimit = tabFragments.size
            ViewPagerUtil.setScrollAnimDuration(viewPager)
        }
    }

    override fun initRequestData() {}
}