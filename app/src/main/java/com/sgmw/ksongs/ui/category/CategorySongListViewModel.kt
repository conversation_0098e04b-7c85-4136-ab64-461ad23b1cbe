package com.sgmw.ksongs.ui.category

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.sgmw.common.mvvm.vm.BaseViewModel
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.RankingsBean
import com.sgmw.ksongs.model.bean.Result
import com.sgmw.ksongs.model.bean.SongInfoBean
import com.sgmw.ksongs.model.bean.onFailure
import com.sgmw.ksongs.model.bean.onSuccess
import com.sgmw.ksongs.model.repository.CollectRepository
import com.sgmw.ksongs.model.repository.SongStationRepository
import com.sgmw.ksongs.ui.hottopic.DEFAULT_START_INDEX
import com.sgmw.ksongs.ui.playlist.PlayListManager
import com.sgmw.ksongs.utils.updateCollectStatus
import com.sgmw.ksongs.utils.updateDemandStatus
import kotlinx.coroutines.launch

/**
 * 分类歌曲列表ViewModel - 独立实现，不依赖HotTopicListViewModel
 */
class CategorySongListViewModel : BaseViewModel() {

    // 收藏数据库数据变化监听
    val collectSongChangeLiveData: LiveData<Int> = CollectRepository().getCount()

    // 点歌列表数据变化监听
    val demandSongInfo = PlayListManager.getAllDemandSongInfoLiveData()

    private var themeId: Int = 0
    private var startIndex = DEFAULT_START_INDEX
    private var isLoading: Boolean = false

    private val songStationRepository = SongStationRepository()
    private val _categorySongListResult = MutableLiveData<Result<RankingsBean?>>()
    val categorySongListResult: LiveData<Result<RankingsBean?>> = _categorySongListResult

    /**
     * 设置主题ID
     */
    fun setThemeId(themeId: Int) {
        this.themeId = themeId
    }

    /**
     * 获取分类歌曲列表
     */
    fun getCategorySongList(operation: Operation) {
        if (isLoading) return
        isLoading = true

        // 新数据或刷新时重置起始索引
        if (operation == Operation.NewData || operation == Operation.Refresh) {
            startIndex = DEFAULT_START_INDEX
        }

        songStationRepository.getThemeSongList(operation, themeId.toString(), startIndex) { result ->
            result.onSuccess { value, operation ->
                startIndex++
                updateStatusThenSendData(result)
            }.onFailure { resultCode, operation ->
                _categorySongListResult.postValue(result)
            }
            isLoading = false
        }
    }

    /**
     * 更新歌曲状态后发送数据
     */
    private fun updateStatusThenSendData(result: Result<RankingsBean?>) {
        viewModelScope.launch {
            // 更新收藏状态和点歌状态
            result.value?.songs?.updateCollectStatus()
            result.value?.songs?.updateDemandStatus()
            _categorySongListResult.postValue(result)
        }
    }

    /**
     * 更新收藏状态
     */
    fun updateCollectStatus(songList: MutableList<SongInfoBean>) {
        viewModelScope.launch {
            songList.updateCollectStatus()
            postValueAfterUpdateStatus(songList)
        }
    }

    /**
     * 更新点歌状态
     */
    fun updateDemandStatus(songList: MutableList<SongInfoBean>) {
        viewModelScope.launch {
            songList.updateDemandStatus()
            postValueAfterUpdateStatus(songList)
        }
    }

    /**
     * 状态更新后发送数据
     */
    private fun postValueAfterUpdateStatus(songList: MutableList<SongInfoBean>) {
        val lastResult = categorySongListResult.value?.value
        lastResult?.songs = songList
        _categorySongListResult.postValue(Result.Success(lastResult, Operation.UpdateStatus))
    }
}