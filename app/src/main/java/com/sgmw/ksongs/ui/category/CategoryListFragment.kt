package com.sgmw.ksongs.ui.category

import android.os.Bundle
import androidx.navigation.fragment.findNavController
import com.sgmw.common.ktx.dp2px
import com.sgmw.common.mvvm.v.BaseFrameFragment
import com.sgmw.ksongs.R
import com.sgmw.ksongs.databinding.FragmentCategoryListBinding
import com.sgmw.ksongs.model.bean.CategoryThemeBean
import com.sgmw.ksongs.track.BigDataConstants.SPLIT
import com.sgmw.ksongs.track.SensorsDataManager
import com.sgmw.ksongs.utils.NavigationUtils
import com.sgmw.ksongs.widget.AccessibilityGridLayoutManager
import com.sgmw.ksongs.widget.SpaceItemDecoration

/**
 * 分类点歌 一级列表页面
 */
class CategoryListFragment : BaseFrameFragment<FragmentCategoryListBinding, CategoryHomeViewModel>() {


    companion object {
        private const val CATEGORY_THEME_LIST = "category_theme_list"
        private const val CATEGORY_CLASS_NAME = "category_class_name"
        private const val GRID_LAYOUT_COLUMNS = 6

        fun createCategoryListFragment(categoryItemBeanList: ArrayList<CategoryThemeBean>,className:String): CategoryListFragment {
            return CategoryListFragment().apply {
                arguments = Bundle().apply {
                    putParcelableArrayList(CATEGORY_THEME_LIST, categoryItemBeanList)
                    putString(CATEGORY_CLASS_NAME,className)
                }
            }
        }

    }
    override fun needSkinApply() = true
    override fun FragmentCategoryListBinding.initView() {
        rvCategoryList.layoutManager = AccessibilityGridLayoutManager(context, GRID_LAYOUT_COLUMNS)
        val space = context?.dp2px(70F) ?: 0
        val bottomSpace = context?.dp2px(18F) ?: 0
        rvCategoryList.addItemDecoration(
            SpaceItemDecoration(
                leftSpace = space, rightSpace = space,
                bottomSpace = bottomSpace, gridNum = GRID_LAYOUT_COLUMNS
            )
        )
        val adapter = CategoryListAdapter()
        rvCategoryList.adapter = adapter

        val categoryThemeList = arguments?.getParcelableArrayList<CategoryThemeBean>(CATEGORY_THEME_LIST)
        val categoryClassName = arguments?.getString(CATEGORY_CLASS_NAME)
        adapter.setList(categoryThemeList)

        adapter.setOnItemClickListener { _, _, position ->
            adapter.getItemOrNull(position)?.let {
               val cardName =  CategoryHomeFragment.CARD_NAME + SPLIT + categoryClassName + SPLIT + it.theme_name
                SensorsDataManager.trackSongStationEvent(cardName)
                NavigationUtils.navigateSafely(
                    findNavController(),
                    R.id.action_to_category_song_list,
                    CategorySongListFragment.createBundle(it,cardName)
                )
            }
        }
    }

    override fun initRequestData() {

    }


}