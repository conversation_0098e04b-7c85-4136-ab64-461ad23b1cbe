package com.sgmw.ksongs.ui.dialog

import android.app.Dialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.sgmw.common.R
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.manager.StatusBarManager
import com.sgmw.ksongs.R as AppR

/**
 * 支持高斯模糊背景的 DialogFragment
 * Created by qinrc on 2025/1/15.
 */
open class BaseBlurDialogFragment(private val mLayoutResID: Int) : DialogFragment() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, getDialogTheme())
    }

    override fun onStart() {
        super.onStart()
        val dialogView = dialog?.window?.decorView ?: return
        val content = dialogView.findViewById<View>(AppR.id.dialog_content)
        val bg = dialogView.findViewById<View>(AppR.id.bg_shadow)

        // 蒙版渐入动画
        bg?.let {
            val bgFadeIn = AnimationUtils.loadAnimation(context, R.anim.actionsheet_dialog_bg_in)
            it.startAnimation(bgFadeIn)
        }

        // 内容 scale 渐入动画
        content?.let {
            val contentIn = AnimationUtils.loadAnimation(context, R.anim.actionsheet_dialog_in)
            it.startAnimation(contentIn)
        }
    }

    override fun dismiss() {
        val dialogView = dialog?.window?.decorView
        val content = dialogView?.findViewById<View>(AppR.id.dialog_content)
        val bg = dialogView?.findViewById<View>(AppR.id.bg_shadow)

        var contentAnimEnd = false
        var bgAnimEnd = false

        fun tryDismiss() {
            if (contentAnimEnd && bgAnimEnd) {
                Log.d(TAG, "所有动画结束，开始关闭弹框")
                superDismiss()
            }
        }

        // 如果任何一个view为null，直接关闭
        if (content == null || bg == null) {
            Log.d(TAG, "content 或 bg 为 null，直接关闭")
            superDismiss()
            return
        }

        // 内容 scale 渐出动画
        content.let {
            val contentOut = AnimationUtils.loadAnimation(context, R.anim.actionsheet_dialog_out)
            contentOut.setAnimationListener(object : Animation.AnimationListener {
                override fun onAnimationStart(animation: android.view.animation.Animation?) {
                    Log.d(TAG, "onAnimationStart contentOut")
                }
                override fun onAnimationEnd(animation: android.view.animation.Animation?) {
                    Log.d(TAG, "onAnimationEnd contentOut")
                    contentAnimEnd = true
                    tryDismiss()
                }

                override fun onAnimationRepeat(animation: android.view.animation.Animation?) {}
            })
            it.startAnimation(contentOut)
        }

        // 蒙版渐出动画
        bg.let {
            val bgFadeOut = AnimationUtils.loadAnimation(context, R.anim.actionsheet_dialog_bg_out)
            bgFadeOut.setAnimationListener(object : Animation.AnimationListener {
                override fun onAnimationStart(animation: android.view.animation.Animation?) {
                    Log.d(TAG, "onAnimationStart bgFadeOut")
                }
                override fun onAnimationEnd(animation: android.view.animation.Animation?) {
                    Log.d(TAG, "onAnimationEnd bgFadeOut")
                    bgAnimEnd = true
                    tryDismiss()
                }

                override fun onAnimationRepeat(animation: android.view.animation.Animation?) {}
            })
            it.startAnimation(bgFadeOut)
        }
    }

    private fun superDismiss() {
        Log.d(TAG, "superDismiss: 开始调用父类的 dismiss 方法")
        try {
            super.dismiss()
            Log.d(TAG, "superDismiss: 父类的 dismiss 方法调用成功")
        } catch (e: Exception) {
            Log.e(TAG, "superDismiss: 父类的 dismiss 方法调用失败", e)
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        // 需要在Dialog创建前查询是否处于全屏状态，否则会出现Dialog创建后展示状态栏的问题
        val currentStatusBarIsHide = StatusBarManager.currentStatusBarIsHide
        Log.d("BaseBlurDialogFragment", "onCreateDialog: currentStatusBarIsHide = $currentStatusBarIsHide")
        val dialog = Dialog(requireContext(), getDialogTheme())
        dialog.setContentView(mLayoutResID)
        dialog.setCanceledOnTouchOutside(true)

        // 设置窗口布局参数
        dialog.window?.let { window ->

            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            window.clearFlags(
                WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS or
                        WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION
            )
            window.addFlags(
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
            )
            window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_LAYOUT_STABLE)
            window.isNavigationBarContrastEnforced = false
            window.statusBarColor = Color.TRANSPARENT
            val decorView = window.decorView
            val vis = decorView.systemUiVisibility
            val option = View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or
                    View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            decorView.systemUiVisibility = vis or option

            // 设置窗口背景透明
            window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

            // 获取窗口布局参数
            val layoutParams = window.attributes
            // 设置窗口宽度为MATCH_PARENT
            layoutParams.width = resources.getDimensionPixelSize(AppR.dimen.dp_1920)
            // 设置窗口高度
            if (currentStatusBarIsHide) {
                layoutParams.height = resources.getDimensionPixelSize(AppR.dimen.dp_1080)
            } else {
                layoutParams.height = resources.getDimensionPixelSize(AppR.dimen.dp_990)
            }
            // 设置窗口位置为居中
            layoutParams.gravity = Gravity.TOP or Gravity.CENTER_HORIZONTAL

            // 应用布局参数
            window.attributes = layoutParams
        }

        return dialog
    }

    open fun getDialogTheme(): Int {
        return R.style.common_dialog
    }

    /**
     * 安全地显示对话框
     * @param manager FragmentManager实例
     * @param tag 对话框标签
     * @return 是否成功显示对话框
     */
    private fun safeShow(manager: FragmentManager, tag: String): Boolean {
        try {
            // 检查Activity状态
            if (activity?.isFinishing == true || activity?.isDestroyed == true) {
                Log.e(TAG, "Activity is finishing or destroyed")
                return false
            }

            // 检查FragmentManager状态
            if (manager.isDestroyed || manager.isStateSaved) {
                Log.e(TAG, "FragmentManager is destroyed or state saved")
                return false
            }

            // 检查是否已经显示
            if (manager.findFragmentByTag(tag) == null) {
                Log.d(TAG, "Safe to show dialog with tag: $tag")
                super.show(manager, tag)
                return true
            }
            return false
        } catch (e: IllegalStateException) {
            Log.e(TAG, "Failed to show dialog: ${e.message}")
            return false
        }
    }

    /**
     * 重写show方法，使用safeShow进行安全检查
     */
    override fun show(manager: FragmentManager, tag: String?) {
        Log.d(TAG, "show: tag = $tag")
        if (tag != null) {
            safeShow(manager, tag)
        } else {
            super.show(manager, tag)
        }
    }

    companion object {
        private const val TAG = "BaseBlurDialogFragment"
    }
}