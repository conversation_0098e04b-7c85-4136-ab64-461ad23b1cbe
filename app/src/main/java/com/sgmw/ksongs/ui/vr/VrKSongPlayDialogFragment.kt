package com.sgmw.ksongs.ui.vr

import android.content.DialogInterface
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.ksongs.R
import com.sgmw.ksongs.databinding.DialogVrKsongPlayBinding
import com.sgmw.ksongs.ui.dialog.BaseBlurDialogFragment

class VrKSongPlayDialogFragment : BaseBlurDialogFragment(R.layout.dialog_vr_ksong_play) {

    private val mBinding: DialogVrKsongPlayBinding by lazy {
        DialogVrKsongPlayBinding.inflate(layoutInflater)
    }

    private var mMessage: String? = null

    private var confirmCallback: (() -> Unit)? = null
    private var cancelCallback: (() -> Unit)? = null

    private val handler = Handler(Looper.getMainLooper())
    private var runnable: Runnable? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        isCancelable = true
        dialog?.setCanceledOnTouchOutside(true)
        mBinding.apply {
            tvMessage.text = mMessage
            mBinding.dialogContent.setOnSingleClickListener {}
            root.setOnSingleClickListener {
                Log.d(TAG, "点击背景")
                cancel()
            }
            tvCancel.setOnSingleClickListener {
                Log.d(TAG, "点击取消")
                cancel()
            }
            tvConfirm.setOnSingleClickListener {//进入k歌页面
                Log.d(TAG, "点击确定")
                confirm()
            }

            startCountDown()

        }

        return mBinding.root
    }

    fun setMessage(message: String) {
        mMessage = message
    }

    override fun onCancel(dialog: DialogInterface) {
        super.onCancel(dialog)
        cancel()
    }

    private fun startCountDown() {
        var countdownSeconds = 5
        mBinding.tvConfirm.text = "确认($countdownSeconds)"
        runnable = object : Runnable {
            override fun run() {
                countdownSeconds-- // 减少秒数
                if (countdownSeconds > 0) {
                    mBinding.tvConfirm.text = "确认($countdownSeconds)"
                    handler.postDelayed(this, 1000)
                } else {//确认播放
                    Log.d(TAG, "倒计时结束")
                    confirm()
                }
            }
        }
        handler.postDelayed(runnable as Runnable, 1000)
    }

    private fun stopCountDown() {
        runnable?.let {
            handler.removeCallbacks(it)
        }
    }

    fun setOnConfirmListener(listener: () -> Unit) {
        confirmCallback = listener
    }

    fun setOnCancelListener(listener: () -> Unit) {
        cancelCallback = listener
    }

    fun confirm() {
        stopCountDown()
        confirmCallback?.invoke()
        dismiss()
    }

    fun cancel() {
        stopCountDown()
        cancelCallback?.invoke()
        dismiss()
    }

    override fun onDestroy() {
        super.onDestroy()
        // 清理Handler回调和监听器引用，防止内存泄露
        stopCountDown()
        confirmCallback = null
        cancelCallback = null
    }

    companion object {
        private const val TAG = "VrKSongPlayDialogFragment"
    }

}