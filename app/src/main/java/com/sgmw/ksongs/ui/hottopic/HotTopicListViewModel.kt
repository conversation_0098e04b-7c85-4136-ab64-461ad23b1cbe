package com.sgmw.ksongs.ui.hottopic

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.sgmw.common.mvvm.vm.BaseViewModel
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.RankingsBean
import com.sgmw.ksongs.model.bean.Result
import com.sgmw.ksongs.model.bean.SongInfoBean
import com.sgmw.ksongs.model.bean.onFailure
import com.sgmw.ksongs.model.bean.onSuccess
import com.sgmw.ksongs.model.repository.CollectRepository
import com.sgmw.ksongs.model.repository.SongStationRepository
import com.sgmw.ksongs.ui.hottopic.HotTopicListFragment.Companion.DEFAULT_THEME_ID
import com.sgmw.ksongs.ui.playlist.PlayListManager
import com.sgmw.ksongs.utils.updateCollectStatus
import com.sgmw.ksongs.utils.updateDemandStatus
import kotlinx.coroutines.launch

/**
 * 专题歌曲列表ViewModel
 */
class HotTopicListViewModel: BaseViewModel() {

    // 收藏数据库数据
    val collectSongChangeLiveData: LiveData<Int> = CollectRepository().getCount()
    // 添加到歌单数据
    val demandSongInfo = PlayListManager.getAllDemandSongInfoLiveData()

    private var themeId: Int = DEFAULT_THEME_ID

    private val songStationRepository = SongStationRepository()
    private val _hotTopicSongListResult = MutableLiveData<Result<RankingsBean?>>()
    val hotTopicSongListResult: LiveData<Result<RankingsBean?>> = _hotTopicSongListResult

    private var startIndex = DEFAULT_START_INDEX
    private var isLoading: Boolean = false

    fun setThemeId(themeId: Int) {
        this.themeId = themeId
    }

    fun getHotTopicSongList(operation: Operation) {
        if (isLoading) return
        isLoading = true
        if (operation == Operation.NewData || operation == Operation.Refresh) {
            startIndex = DEFAULT_START_INDEX
        }
        songStationRepository.getThemeSongList(operation, themeId.toString(), startIndex) {
            it.onSuccess { value, operation ->
                startIndex++
                updateStatusThenSendData(it)
            }.onFailure { resultCode, operation ->
                _hotTopicSongListResult.postValue(it)
            }
            isLoading = false
        }
    }

    private fun updateStatusThenSendData(rankListResult: Result<RankingsBean?>) {
        viewModelScope.launch {
            rankListResult.value?.songs?.updateCollectStatus()
            rankListResult.value?.songs?.updateDemandStatus()
            _hotTopicSongListResult.postValue(rankListResult)
        }
    }

    fun updateCollectStatus(songList: MutableList<SongInfoBean>) {
        viewModelScope.launch {
            songList.updateCollectStatus()
            postValueAfterUpdateStatus(songList)
        }
    }

    fun updateDemandStatus(songList: MutableList<SongInfoBean>) {
        viewModelScope.launch {
            songList.updateDemandStatus()
            postValueAfterUpdateStatus(songList)
        }
    }

    private fun postValueAfterUpdateStatus(songList: MutableList<SongInfoBean>) {
        val lastHotTopicListBean = hotTopicSongListResult.value?.value
        lastHotTopicListBean?.songs = songList
        _hotTopicSongListResult.postValue(Result.Success(lastHotTopicListBean, Operation.UpdateStatus))
    }

}