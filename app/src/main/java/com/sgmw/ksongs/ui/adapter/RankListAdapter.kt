package com.sgmw.ksongs.ui.adapter

import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.utils.GlideUtil
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.R
import com.sgmw.ksongs.db.entity.DemandSongInfo
import com.sgmw.ksongs.model.bean.SongInfoBean
import com.sgmw.ksongs.ui.songplay.KaraokeConsole
import com.sgmw.ksongs.utils.addTopSongInfo
import com.sgmw.ksongs.utils.collectOrCancelSongInfo
import com.sgmw.ksongs.utils.toggleDemandSongInfo
import com.sgmw.ksongs.utils.visibleOrGone
import com.tme.ktv.video.api.VideoState

/**
 * 歌曲各种排行列表adapter
 */
class RankListAdapter : BaseAdapter<SongInfoBean>(R.layout.item_rank_list) {

    private val iconRadius = 12
    private var mCardName = ""

    // 从数据层面防抖
    private val processingItems = mutableSetOf<String>()

    fun setCardName(cardName: String) {
        mCardName = cardName
    }

    override fun convert(holder: BaseViewHolder, item: SongInfoBean) {
        val tvNum = holder.getView<TextView>(R.id.tvNum)
        val tvSongName = holder.getView<TextView>(R.id.tvSongName)
        val tvSongAuthor = holder.getView<TextView>(R.id.tvSongAuthor)
        val ivPlayStatus = holder.getView<ImageView>(R.id.ivPlayStatus)
        val ivUp = holder.getView<ImageView>(R.id.ivUp)
        val ivCollect = holder.getView<ImageView>(R.id.ivCollect)
        val ivAdd = holder.getView<ImageView>(R.id.ivAdd)
        val ivVip = holder.getView<ImageView>(R.id.ivVip)
        val ivMv = holder.getView<ImageView>(R.id.ivMv)
        val ivScore = holder.getView<ImageView>(R.id.ivScore)
        val ivCover = holder.getView<ImageView>(R.id.ivCover)

        // 重置所有状态
        holder.itemView.isSelected = false
        ivPlayStatus.visibility = View.INVISIBLE
        ivPlayStatus.setImageDrawable(null)
        tvNum.visibility = View.VISIBLE
        ivAdd.isEnabled = true
        ivAdd.isSelected = false
        ivCollect.isSelected = false

        // 设置基本信息
        tvNum.text = (holder.layoutPosition + 1 - headerLayoutCount).toString()
        tvSongName.text = item.song_name
        tvSongAuthor.text = item.singer_name
        GlideUtil.loadRoundCornerImage(
            holder.itemView.context, item.album_img,
            ivCover, iconRadius, R.mipmap.icon_music_default_bg, R.mipmap.icon_music_default_bg
        )

        // 根据数据状态设置UI
        if (KaraokeConsole.currSongInfo?.song_id == item.song_id) {
            if (KaraokeConsole.playState.value == VideoState.STATE_PLAYING) {
                tvNum.visibility = View.GONE
                ivPlayStatus.visibility = View.VISIBLE
                ivPlayStatus.setImageDrawable(playAnimation)
                playAnimation?.start()
            } else {
                tvNum.visibility = View.VISIBLE
                ivPlayStatus.visibility = View.INVISIBLE
                playAnimation?.stop()
            }
            holder.itemView.isSelected = item.isPlaying
            ivAdd.isEnabled = false
        }

        // 设置图标可见性
        ivVip.visibleOrGone(item.need_vip)
        ivMv.visibleOrGone(item.has_mv)
        ivScore.visibleOrGone(item.has_midi)

        // 设置收藏和添加状态
        ivCollect.isSelected = item.isCollect
        ivAdd.isSelected = item.isInDemandList

        //注册可见可说热词
        if (item.isCollect) {
            ivCollect.contentDescription = context.getString(R.string.collect_cancel_content_description)
        } else {
            ivCollect.contentDescription = context.getString(R.string.collect_content_description)
        }
        if (item.isInDemandList) {
            ivAdd.contentDescription = context.getString(R.string.add_cancel_content_description)
        } else {
            ivAdd.contentDescription = context.getString(R.string.add_content_description)
        }
        holder.itemView.contentDescription = "播放${tvSongName.text};唱${tvSongName.text}"

        Log.d(
            TAG,
            "convert name: ${item.song_name} isPlaying: ${item.isPlaying} isInDemandList: ${item.isInDemandList}"
        )

        // 设置点击事件
        ivUp.setOnSingleClickListener {
            addTopSongInfo(DemandSongInfo(songInfo = item))
        }
        ivAdd.setOnClickListener {
            // 防止重复点击
            if (processingItems.contains(item.song_id)) return@setOnClickListener
            processingItems.add(item.song_id)
            toggleDemandSongInfo(DemandSongInfo(songInfo = item), cardName = mCardName) { isInDemandList ->
                // 更新UI状态
                item.isInDemandList = isInDemandList
                holder.itemView.post {
                    ivAdd.isSelected = isInDemandList
                    processingItems.remove(item.song_id)
                }
            }
        }
        ivCollect.setOnSingleClickListener {
            collectOrCancelSongInfo(item)
        }
    }

    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        super.onDetachedFromRecyclerView(recyclerView)
        // 清理防抖集合，防止内存泄露
        processingItems.clear()
    }

    companion object {
        private const val TAG = "RankListAdapter"
    }

}
