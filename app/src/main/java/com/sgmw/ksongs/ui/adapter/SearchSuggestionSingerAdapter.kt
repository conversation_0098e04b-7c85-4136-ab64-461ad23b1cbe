package com.sgmw.ksongs.ui.adapter

import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.sgmw.common.utils.GlideUtil
import com.sgmw.ksongs.R
import com.sgmw.ksongs.model.bean.SingerBean

/**
 * @author: 李雪琴
 * @time: 2025/3/11
 * @desc: 搜索建议歌手adapter
 * 输入搜索词，还未点击搜索按钮的时候，会展示搜索建议的歌手列表
 */

class SearchSuggestionSingerAdapter : BaseAdapter<SingerBean.Singer>(R.layout.item_search_singer) {
    override fun convert(holder: BaseViewHolder, item: SingerBean.Singer) {
        val singerIcon = holder.getView<ImageView>(R.id.iv_singer_icon)
        GlideUtil.loadRoundCornerImage(
            context,
            item.singer_cover,
            singerIcon,
            context.resources.getDimension(R.dimen.dp_48).toInt(),
            R.mipmap.icon_music_default_bg
        )
        holder.getView<TextView>(R.id.tv_singer_name).text = item.singer_name
        if (item.songs_num != Int.MIN_VALUE) {
            holder.getView<TextView>(R.id.tv_songs_num).visibility = View.VISIBLE
            holder.getView<TextView>(R.id.tv_songs_num).text = "${item.songs_num} 首伴奏"
        } else {
            holder.getView<TextView>(R.id.tv_songs_num).visibility = View.GONE
        }
    }
}