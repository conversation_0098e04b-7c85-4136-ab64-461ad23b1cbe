package com.sgmw.ksongs.ui.category

import androidx.appcompat.widget.TooltipCompat
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.mvvm.v.BaseFrameFragment
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.MainActivity
import com.sgmw.ksongs.constant.Constant.MAX_TAB_COUNT
import com.sgmw.ksongs.databinding.FragmentCategoryHomeBinding
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.onFailure
import com.sgmw.ksongs.model.bean.onSuccess
import com.sgmw.ksongs.track.BigDataConstants.SPLIT
import com.sgmw.ksongs.track.SensorsDataManager
import com.sgmw.ksongs.utils.setupTabClickEvents
import com.sgmw.ksongs.viewmodel.MainViewModel

/**
 * 分类点歌
 */
class CategoryHomeFragment : BaseFrameFragment<FragmentCategoryHomeBinding, CategoryHomeViewModel>() {

    companion object{
        const val CARD_NAME = "分类点歌"
    }
    override fun FragmentCategoryHomeBinding.initView() {
        val categoryHomePageAdapter = CategoryHomePageAdapter(childFragmentManager, viewLifecycleOwner.lifecycle)
        viewPager.adapter = categoryHomePageAdapter
        TabLayoutMediator(tabLayout, viewPager, true, true) { tab, position ->
            tab.text = categoryHomePageAdapter.getTitle(position)
        }.attach()
        mBinding?.tabLayout?.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener{
            override fun onTabSelected(tab: TabLayout.Tab?) {
                tab?.let {
                    SensorsDataManager.trackSongStationEvent(CARD_NAME + SPLIT + it.text.toString())
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
            }

        })
        for (i in 0 until tabLayout.tabCount) {
            val tab = tabLayout.getTabAt(i)
            tab?.let {
                TooltipCompat.setTooltipText(it.view, null)
            }
        }
        ivBack.setOnSingleClickListener {
            findNavController().popBackStack()
        }

    }

    private fun setErrorEntryListener() {
       mBinding?.stateLayout?.setErrorRetryClickListener {
           mBinding?.stateLayout?.showLoading()
            ViewModelProvider(requireActivity())[MainViewModel::class.java].getCategoryList(
                Operation.NewData
            )
        }
    }

    override fun initObserve() {
        super.initObserve()
        val categoryResultLiveData = ViewModelProvider(requireActivity())[MainViewModel::class.java].categoryResultLiveData
        categoryResultLiveData.observe(this) {
            it.onSuccess { value, operation ->
                mBinding?.apply {
                    (viewPager.adapter as? CategoryHomePageAdapter)?.setData(value?.class_list)
                    value?.class_list?.let {
                        if (it.size >= MAX_TAB_COUNT){
                            tabLayout.setupTabClickEvents(viewPager)
                        }
                    }
                    stateLayout.showContent()
                }
            }.onFailure { _, operation ->
                mBinding?.stateLayout?.showError()
                setErrorEntryListener()
            }
        }

    }

    override fun initRequestData() {

    }


}