package com.sgmw.ksongs.ui.adapter

import android.widget.TextView
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.sgmw.ksongs.R
import com.tme.ktv.demo.bean.VipProductBean

class VipPaymentAdapter: BaseAdapter<VipProductBean.GoodsInfo>(R.layout.item_vip_payment) {

    private var mPosition :Int = 0

    override fun convert(holder: BaseViewHolder, item: VipProductBean.GoodsInfo) {

        val price = holder.getView<TextView>(R.id.tv_price)
        val peroid = holder.getView<TextView>(R.id.tv_period)
        price.text = "¥${item.price}"
        peroid.text = item.goods_desc
        if(getItemPosition(item) == mPosition){
            holder.itemView.setBackgroundResource(R.drawable.bg_item_vip_payment_selected)
            price.setTextColor(holder.itemView.resources.getColor(R.color.white))
            peroid.setTextColor(holder.itemView.resources.getColor(R.color.white))
        } else {
            holder.itemView.setBackgroundResource(R.drawable.bg_item_vip_payment)
            price.setTextColor(holder.itemView.resources.getColor(R.color.item_vip_payment_text_color))
            peroid.setTextColor(holder.itemView.resources.getColor(R.color.item_vip_payment_text_color))
        }

    }

    fun setSelectPosition(position:Int){
        mPosition = position
        notifyDataSetChanged()
    }

    fun getSelectPosition():Int{
        return mPosition
    }
}