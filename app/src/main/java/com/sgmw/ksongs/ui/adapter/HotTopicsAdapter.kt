package com.sgmw.ksongs.ui.adapter

import android.widget.ImageView
import android.widget.TextView
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.sgmw.common.ktx.loadRoundImage
import com.sgmw.common.utils.GlideUtil
import com.sgmw.ksongs.R
import com.sgmw.ksongs.model.bean.HotTopicBean

class HotTopicsAdapter : BaseAdapter<HotTopicBean.Theme>(R.layout.item_sub_song_station_topic) {
    override fun convert(holder: BaseViewHolder, item: HotTopicBean.Theme) {
         holder.getView<TextView>(R.id.tv_title).text = item.theme_name
         val  ivIcon = holder.getView<ImageView>(R.id.iv_icon)
        ivIcon.loadRoundImage(
            item.theme_pic,
            roundCorner = ivIcon.context.resources.getDimension(R.dimen.dp_12)
        )
    }

}