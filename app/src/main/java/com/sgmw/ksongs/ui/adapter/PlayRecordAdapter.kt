package com.sgmw.ksongs.ui.adapter

import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.utils.GlideUtil
import com.sgmw.ksongs.R
import com.sgmw.ksongs.db.entity.DemandSongInfo
import com.sgmw.ksongs.db.entity.PlayRecordSongInfo
import com.sgmw.ksongs.track.BigDataConstants.SPLIT
import com.sgmw.ksongs.ui.songplay.KaraokeConsole
import com.sgmw.ksongs.utils.addTopSongInfo
import com.sgmw.ksongs.utils.collectOrCancelSongInfo
import com.sgmw.ksongs.utils.toggleDemandSongInfo
import com.sgmw.ksongs.utils.visibleOrGone
import com.tme.ktv.video.api.VideoState

class PlayRecordAdapter : BaseAdapter<PlayRecordSongInfo>(R.layout.item_play_record) {

    private var iconRadius = 12
    private var isEditMode = false

    // 从数据层面防抖
    private val processingItems = mutableSetOf<String>()

    var onSelectChangeListener: (() -> Unit)? = null
    private var mCardName = ""
    fun setCardName(cardName:String){
        mCardName = cardName
    }

    override fun convert(holder: BaseViewHolder, item: PlayRecordSongInfo, payloads: List<Any>) {
        if (payloads.isNotEmpty() && payloads.contains(PAYLOAD_SELECT_STATE)) {
            // 只更新选中状态，避免图片重新加载
            updateSelectState(holder, item)
            return
        }
        // 完整更新
        convert(holder, item)
    }

    override fun convert(holder: BaseViewHolder, item: PlayRecordSongInfo) {
        val tvNum = holder.getView<TextView>(R.id.tvNum)
        val tvSongName = holder.getView<TextView>(R.id.tvSongName)
        val tvSongAuthor = holder.getView<TextView>(R.id.tvSongAuthor)
        val ivPlayStatus = holder.getView<ImageView>(R.id.ivPlayStatus)
        val ivUp = holder.getView<ImageView>(R.id.ivUp)
        val ivAdd = holder.getView<ImageView>(R.id.ivAdd)
        val ivCollect = holder.getView<ImageView>(R.id.ivCollect)
        val ivSelect = holder.getView<ImageView>(R.id.ivSelect)
        val ivVip = holder.getView<ImageView>(R.id.ivVip)
        val ivMv = holder.getView<ImageView>(R.id.ivMv)
        val ivScore = holder.getView<ImageView>(R.id.ivScore)
        val ivCover = holder.getView<ImageView>(R.id.ivCover)

        // 重置所有状态
        holder.itemView.isSelected = false
        tvSongName.isSelected = false
        tvSongAuthor.isSelected = false
        ivPlayStatus.visibility = View.INVISIBLE
        ivPlayStatus.setImageDrawable(null)
        tvNum.visibility = View.VISIBLE
        ivAdd.isEnabled = true
        ivAdd.isSelected = false
        ivCollect.isSelected = false
        ivSelect.isSelected = false

        tvNum.text = (holder.layoutPosition + 1).toString()
        tvSongName.text = item.songInfo.song_name
        tvSongAuthor.text = item.songInfo.singer_name
        GlideUtil.loadRoundCornerImage(holder.itemView.context, item.songInfo.album_img,
            ivCover, iconRadius, R.mipmap.icon_music_default_bg, R.mipmap.icon_music_default_bg)

        if (KaraokeConsole.currSongInfo?.song_id == item.songInfo.song_id) {
            if (KaraokeConsole.playState.value == VideoState.STATE_PLAYING) {
                tvNum.visibility = View.GONE
                ivPlayStatus.visibility = View.VISIBLE
                ivPlayStatus.setImageDrawable(playAnimation)
                playAnimation?.start()
            } else {
                tvNum.visibility = View.VISIBLE
                ivPlayStatus.visibility = View.INVISIBLE
                playAnimation?.stop()
            }
            holder.itemView.isSelected = item.songInfo.isPlaying
            tvSongName.isSelected = true
            tvSongAuthor.isSelected = true
            ivAdd.isEnabled = false
        }

        ivVip.visibleOrGone(item.songInfo.need_vip)
        ivMv.visibleOrGone(item.songInfo.has_mv)
        ivScore.visibleOrGone(item.songInfo.has_midi)

        ivCollect.isSelected = item.songInfo.isCollect
        ivSelect.isSelected = item.isSelect
        ivAdd.isSelected = item.songInfo.isInDemandList

        if (isEditMode) {
            ivUp.visibility = View.GONE
            ivAdd.visibility = View.GONE
            ivCollect.visibility = View.GONE
            ivSelect.visibility = View.VISIBLE

            ivCollect.contentDescription = context.getString(R.string.null_content_description)
            ivAdd.contentDescription = context.getString(R.string.null_content_description)
            ivUp.contentDescription = context.getString(R.string.null_content_description)
        } else {
            ivUp.visibility = View.VISIBLE
            ivAdd.visibility = View.VISIBLE
            ivCollect.visibility = View.VISIBLE
            ivSelect.visibility = View.GONE

            if (item.songInfo.isCollect) {
                ivCollect.contentDescription = context.getString(R.string.collect_cancel_content_description)
            } else {
                ivCollect.contentDescription = context.getString(R.string.collect_content_description)
            }
            if (item.songInfo.isInDemandList) {
                ivAdd.contentDescription = context.getString(R.string.add_cancel_content_description)
            } else {
                ivAdd.contentDescription = context.getString(R.string.add_content_description)
            }
            ivUp.contentDescription = context.getString(R.string.top_content_description)
            holder.itemView.contentDescription = "播放${tvSongName.text};唱${tvSongName.text}"
        }
        ivUp.setOnSingleClickListener {
            addTopSongInfo(DemandSongInfo(songInfo = item.songInfo))
        }
        ivAdd.setOnClickListener {
            // 防止重复点击
            if (processingItems.contains(item.songInfo.song_id)) return@setOnClickListener
            processingItems.add(item.songInfo.song_id)
            toggleDemandSongInfo(
                DemandSongInfo(songInfo = item.songInfo),
                cardName = mCardName + SPLIT +context.getString(R.string.home_card_ksong_title)
            ) { isInDemandList ->
                // 更新UI状态
                item.songInfo.isInDemandList = isInDemandList
                holder.itemView.post {
                    ivAdd.isSelected = isInDemandList
                    processingItems.remove(item.songInfo.song_id)
                }
            }
        }
        ivCollect.setOnClickListener {
            collectOrCancelSongInfo(item.songInfo)
        }
        ivSelect.setOnClickListener {
            selectOrNot(holder.layoutPosition)
        }
    }

    /**
     * 只更新选中状态相关的UI，避免图片重新加载
     */
    private fun updateSelectState(holder: BaseViewHolder, item: PlayRecordSongInfo) {
        val ivSelect = holder.getView<ImageView>(R.id.ivSelect)
        ivSelect.isSelected = item.isSelect
    }

    fun selectOrNot(position: Int) {
        if (!isEditMode) return
        getItemOrNull(position)?.let {
            it.isSelect = !it.isSelect
        }
        // 使用payload进行局部更新，避免图片重新加载
        notifyItemChanged(position, PAYLOAD_SELECT_STATE)
        onSelectChangeListener?.invoke()
    }

    companion object {
        private const val PAYLOAD_SELECT_STATE = "select_state"
    }


    fun updateEditModeUI(isEdit: Boolean) {
        if (this.isEditMode != isEdit) {
            this.isEditMode = isEdit
            // 进入退出编辑模式时，默认全部选中，不记录上次编辑态下选中条目
            selectAllOrNot(false)
        }
    }

    /**
     * 全选/全不选
     */
    fun selectAllOrNot(isSelectAll: Boolean) {
        data.forEach {
            it.isSelect = isSelectAll
        }
        notifyDataSetChanged()
        onSelectChangeListener?.invoke()
    }


    /**
     * 获取全部选中的
     */
    fun getAllSelect(): List<PlayRecordSongInfo> {
        return data.filter { it.isSelect }
    }

    /**
     * 是否全选
     */
    fun isSelectAll(): Boolean {
        return data.isNotEmpty() && data.size == getAllSelect().size
    }

    /**
     * 是否有选中，是否全选
     * Pair: first value: 是否有选中数据; second value: 是否选中所有
     */
    fun hasSelected(): Pair<Boolean, Boolean> {
        val selectList = getAllSelect()
        val hasSelect = selectList.isNotEmpty()
        val hasAllSelect = selectList.isNotEmpty() && data.size == selectList.size
        return Pair(hasSelect, hasAllSelect)
    }

    /**
     * 获取当前选择状态
     * @return 0: 全不选, 1: 部分选中, 2: 全选
     */
    fun getSelectState(): Int {
        if (data.isEmpty()) return 0
        val selectedCount = getAllSelect().size
        return when {
            selectedCount == 0 -> 0 // 全不选
            selectedCount == data.size -> 2 // 全选
            else -> 1 // 部分选中
        }
    }

    override fun setList(list: Collection<PlayRecordSongInfo>?) {
        if (isEditMode) {
            mutableSetOf<String>().apply {
                getAllSelect().mapTo(this){ it.songInfo.song_id }
            }.takeIf { it.isNotEmpty() }?.let {set ->
                list?.forEach { it.isSelect = set.contains(it.songInfo.song_id) }
            }
        }
        super.setList(list)
        if (isEditMode) {
            onSelectChangeListener?.invoke()
        }
    }


    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        super.onDetachedFromRecyclerView(recyclerView)
        // 清理监听器引用，防止内存泄露
        onSelectChangeListener = null
        // 清理防抖集合
        processingItems.clear()
    }


}