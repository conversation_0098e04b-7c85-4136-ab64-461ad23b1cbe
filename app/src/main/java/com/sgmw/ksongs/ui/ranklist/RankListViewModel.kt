package com.sgmw.ksongs.ui.ranklist

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.sgmw.common.mvvm.vm.BaseViewModel
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.model.bean.RankingsBean
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.Result
import com.sgmw.ksongs.model.bean.SongInfoBean
import com.sgmw.ksongs.model.bean.onFailure
import com.sgmw.ksongs.model.bean.onSuccess
import com.sgmw.ksongs.model.repository.CollectRepository
import com.sgmw.ksongs.model.repository.SongStationRepository
import com.sgmw.ksongs.ui.playlist.PlayListManager
import com.sgmw.ksongs.utils.HotRank
import com.sgmw.ksongs.utils.RANK_DEFAULT_INDEX
import com.sgmw.ksongs.utils.RankInfo
import com.sgmw.ksongs.utils.updateCollectStatus
import com.sgmw.ksongs.utils.updateDemandStatus
import kotlinx.coroutines.launch

/**
 * 排行榜列表ViewModel
 */
class RankListViewModel : BaseViewModel() {

    // 收藏数据库数据
    val collectSongChangeLiveData: LiveData<Int> = CollectRepository().getCount()

    // 添加到歌单数据
    val demandSongInfo = PlayListManager.getAllDemandSongInfoLiveData()

    private val songStationRepository by lazy { SongStationRepository() }
    private var rankInfo: RankInfo = HotRank
    private var isLoading: Boolean = false

    /**
     * 上次接口请求成功的数据，主要在同步的时候用来构造新的HotRankingsBean发射到页面上
     */
    private var lastSuccessResult: RankingsBean? = null
    private val _rankListResult = MutableLiveData<Result<RankingsBean?>>()
    val rankListResult: LiveData<Result<RankingsBean?>> = _rankListResult

    fun setRankInfo(rankInfo: RankInfo) {
        this.rankInfo = rankInfo
    }

    fun getMultiPlaylist(operation: Operation) {
        if (isLoading) return
        isLoading = true
        val nextIndex = getNextIndex(operation)
        Log.d("getMultiPlaylist", "getMultiPlaylist nextIndex: $nextIndex")
        songStationRepository.getMultiPlaylist(rankInfo, operation, nextIndex) {
            it.onSuccess { value, operation ->
                updateStatusThenSendData(it)
                lastSuccessResult = value
            }.onFailure { resultCode, value ->
                _rankListResult.postValue(it)
                Log.e("getMultiPlaylist", "getMultiPlaylist resultCode: $resultCode, value: $value")
            }
            isLoading = false
        }
    }

    private fun getNextIndex(operation: Operation): Int {
        return when (operation) {
            Operation.Refresh, Operation.NewData -> {
                RANK_DEFAULT_INDEX
            }

            Operation.LoadMore -> {
                lastSuccessResult?.next_index ?: RANK_DEFAULT_INDEX
            }

            else -> {
                RANK_DEFAULT_INDEX
            }
        }
    }

    private fun updateStatusThenSendData(rankListResult: Result<RankingsBean?>) {
        viewModelScope.launch {
            rankListResult.value?.songs?.updateCollectStatus()
            rankListResult.value?.songs?.updateDemandStatus()
            _rankListResult.postValue(rankListResult)
        }
    }

    fun updateCollectStatus(songList: MutableList<SongInfoBean>) {
        viewModelScope.launch {
            songList.updateCollectStatus()
            postValueAfterUpdateStatus(songList)
        }
    }

    fun updateDemandStatus(songList: MutableList<SongInfoBean>) {
        viewModelScope.launch {
            songList.updateDemandStatus()
            postValueAfterUpdateStatus(songList)
        }
    }

    private fun postValueAfterUpdateStatus(songList: MutableList<SongInfoBean>) {
        val statusUpdateBean = RankingsBean(
            lastSuccessResult?.has_more ?: 0,
            lastSuccessResult?.next_index ?: RANK_DEFAULT_INDEX, lastSuccessResult?.pass_back ?: "",
            songList, lastSuccessResult?.total ?: 0
        )
        _rankListResult.postValue(Result.Success(statusUpdateBean, Operation.UpdateStatus))
    }

}