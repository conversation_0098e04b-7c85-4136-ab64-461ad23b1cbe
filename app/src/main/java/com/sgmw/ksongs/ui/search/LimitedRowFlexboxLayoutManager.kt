package com.sgmw.ksongs.ui.search

import android.content.Context
import com.google.android.flexbox.FlexLine
import com.google.android.flexbox.FlexboxLayoutManager


class LimitedRowFlexboxLayoutManager(
    context: Context?,
    private val maxLines: Int
) : FlexboxLayoutManager(context) {

    override fun getFlexLinesInternal(): List<FlexLine> {
        val flexLines = super.getFlexLinesInternal()
        val size = flexLines.size
        if (maxLines > 0 && size > maxLines) {
            flexLines.subList(maxLines, size).clear()
        }
        return flexLines
    }
}