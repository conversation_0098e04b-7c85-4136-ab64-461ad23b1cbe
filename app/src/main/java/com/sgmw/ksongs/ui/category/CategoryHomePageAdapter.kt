package com.sgmw.ksongs.ui.category

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.sgmw.ksongs.model.bean.CategoryItemBean


class CategoryHomePageAdapter(fragmentManager: FragmentManager, lifecycle: Lifecycle) : FragmentStateAdapter(fragmentManager, lifecycle) {

    private val categoryList = mutableListOf<CategoryItemBean>()

    override fun getItemCount(): Int {
        return categoryList.size
    }

    override fun createFragment(position: Int): Fragment {
        return CategoryListFragment.createCategoryListFragment(getCategoryItemBean(position)?.themes ?: arrayListOf(),getTitle(position))
    }

    private fun getCategoryItemBean(position: Int): CategoryItemBean? {
        return categoryList.getOrNull(position)
    }

    fun getTitle(position: Int): String {
        return getCategoryItemBean(position)?.class_name ?: ""
    }

    fun setData(categoryList: List<CategoryItemBean>?) {
        this.categoryList.clear()
        categoryList?.let {
            this.categoryList.addAll(it)
        }
        notifyDataSetChanged()
    }

}