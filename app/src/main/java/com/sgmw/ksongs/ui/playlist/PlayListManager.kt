package com.sgmw.ksongs.ui.playlist

import androidx.lifecycle.LiveData
import com.sgmw.common.utils.DaemonThreadDispatcher
import com.sgmw.common.utils.Log
import com.sgmw.common.utils.ioLaunch
import com.sgmw.common.utils.mainLaunch
import com.sgmw.ksongs.db.DbManager
import com.sgmw.ksongs.db.dao.DemandDao
import com.sgmw.ksongs.db.entity.DemandSongInfo
import com.sgmw.ksongs.model.bean.SongInfoBean
import com.sgmw.ksongs.track.SensorsDataManager
import com.sgmw.ksongs.ui.songplay.KaraokeConsole
import com.sgmw.ksongs.utils.addPlayRecord
import com.sgmw.ksongs.utils.DataConsistencyChecker
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.sync.Mutex

/**
 * @author: 董俊帅
 * @time: 2025/2/20
 * @desc: 播放列表管理类
 */
object PlayListManager {

    private const val TAG = "PlayListManager"

    /**
     * 获取DemandDao，每次都从DbManager获取最新实例，确保数据库重新初始化后能正常工作
     */
    private fun getDemandDao(): DemandDao {
        return DbManager.getDemandSongInfoDao()
    }

    // 使用更细粒度的锁，避免所有操作都串行化
    private val updatePlayingMutex = Mutex()
    private val updateSungMutex = Mutex()

    // 操作状态标记，用于快速检查和避免重复操作
    @Volatile
    private var isUpdatingPlaying = false
    @Volatile
    private var isUpdatingSung = false

    private val _playRecordUpdated = MutableSharedFlow<Unit>()
    val playRecordUpdated: Flow<Unit> = _playRecordUpdated.asSharedFlow()

    /**
     * 初始化播放列表管理器
     * 执行数据一致性检查和修复
     */
    fun initialize() {
        Log.d(TAG, "初始化PlayListManager，执行数据一致性检查")
        ioLaunch {
            try {
                // 延迟执行，避免在应用启动时阻塞
                kotlinx.coroutines.delay(2000)

                // 执行启动时的数据一致性检查和修复
                checkAndRepairSongConsistency()

                // 启动定期检查
                DataConsistencyChecker.schedulePeriodicCheck()
            } catch (e: Exception) {
                Log.e(TAG, "初始化数据一致性检查失败: ${e.message}", e)
            }
        }
    }

    /**
     * 添加歌曲到已点列表， 已添加的不再添加
     * 优化版本：增强数据一致性检查，防止歌曲同时存在于已点和已唱列表中
     */
    fun addDemandSongInfo(demandSongInfo: DemandSongInfo, isPlayEnd: Boolean = false, cardName: String = "") {
        ioLaunch {
            try {
                val demandDao = getDemandDao()

                // 添加数据验证
                val songInfo = demandSongInfo.songInfo
                if (songInfo.song_name.isBlank() || songInfo.singer_name.isBlank()) {
                    Log.e(TAG, "尝试添加无效歌曲数据: song_id=${songInfo.song_id}, song_name='${songInfo.song_name}', singer_name='${songInfo.singer_name}'")
                    return@ioLaunch // 不添加无效数据
                }

                // 使用数据库事务确保操作的原子性
                DbManager.runInTransaction {
                    // 1. 检查歌曲是否已在已点列表中
                    val existsInDemandList = demandDao.isSongIdExists(songInfo.song_id)

                    // 2. 检查歌曲是否在已唱列表中（内存检查）
                    val existsInSungList = SungListManager.getSungListLiveData().value
                        ?.any { it.song_id == songInfo.song_id } ?: false

                    Log.d(TAG, "addDemandSongInfo: 歌曲=${songInfo.song_name}, 已点列表存在=$existsInDemandList, 已唱列表存在=$existsInSungList")

                    if (!existsInDemandList) {
                        // 歌曲不在已点列表中，可以添加
                        if (!isPlayEnd) {
                            demandSongInfo.songInfo.isPlaying = false
                            demandSongInfo.songInfo.isPlayingState = false
                        }

                        // 如果歌曲在已唱列表中，先从已唱列表移除（确保数据一致性）
                        if (existsInSungList) {
                            Log.d(TAG, "addDemandSongInfo: 从已唱列表移除歌曲 ${songInfo.song_name}")
                            SungListManager.removeFromSungList(songInfo)
                        }

                        demandDao.insertDemandSongInfo(demandSongInfo)
                        Log.d(TAG, "addDemandSongInfo: 成功添加歌曲 ${songInfo.song_name} 到已点列表")

                        // 下方是埋点
                        SensorsDataManager.trackAddDemandClickEvent(cardName, songInfo.song_name, songInfo.singer_name)
                    } else {
                        // 歌曲已在已点列表中，删除它（切换状态）
                        Log.d(TAG, "addDemandSongInfo: 歌曲 ${songInfo.song_name} 已存在，执行删除操作")
                        demandDao.deleteDemandSongInfo(songInfo.song_id)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "addDemandSongInfo failed for ${demandSongInfo.songInfo.song_name}", e)
            }
        }
    }

    /**
     * 获取当前已点列表中的所有歌曲
     */
    suspend fun getDemandSongInfoList(): MutableList<DemandSongInfo> {
        return getDemandDao().getAllDemandSongInfo()
    }

    /**
     * 获取当前已点列表中的所有歌曲除去正在播放的歌曲
     */
    suspend fun getDemandSongInfoListWithOutPlaying(): MutableList<DemandSongInfo> {
        return getDemandDao().getAllDemandSongInfoWithOutPlaying()
    }

    /**
     * 获取正在播放的歌曲
     */
    fun getPlayingSongInfoLiveData(): LiveData<DemandSongInfo?> {
        return getDemandDao().getPlayingSongInfoLiveData()
    }

    /**
     * 获取正在播放的歌曲
     */
    suspend fun getPlayingSongInfo(): DemandSongInfo? {
        return getDemandDao().getPlayingSongInfo()
    }

    /**
     * 将歌曲添加到已点列表的顶部
     * 优化版本：增强数据一致性检查
     */
    suspend fun addTopSongInfo(songInfo: DemandSongInfo) {
        // 添加数据验证
        val songInfoBean = songInfo.songInfo
        if (songInfoBean.song_name.isNullOrBlank() || songInfoBean.singer_name.isNullOrBlank()) {
            Log.e(TAG, "尝试添加无效歌曲数据到顶部: song_id=${songInfoBean.song_id}, song_name='${songInfoBean.song_name}', singer_name='${songInfoBean.singer_name}'")
            return // 不添加无效数据
        }

        val demandDao = getDemandDao()

        // 使用数据库事务确保操作的原子性
        DbManager.runInTransaction {
            // 检查歌曲是否在已唱列表中
            val existsInSungList = SungListManager.getSungListLiveData().value
                ?.any { it.song_id == songInfoBean.song_id } ?: false

            if (existsInSungList) {
                Log.d(TAG, "addTopSongInfo: 从已唱列表移除歌曲 ${songInfoBean.song_name}")
                SungListManager.removeFromSungList(songInfoBean)
            }

            val topDemandSongInfo = demandDao.getTopDemandSongInfo()
            val currentTime = topDemandSongInfo?.insertTime ?: System.currentTimeMillis()
            val songInfoById = demandDao.getSongInfoById(songInfo.songInfo.song_id)
            if (songInfoById != null) {
                Log.d(TAG, "addTopSongInfo: 删除已存在的歌曲 ${songInfoBean.song_name}")
                demandDao.deleteDemandSongInfo(songInfoById)
            }
            songInfo.insertTime = currentTime - 1
            demandDao.insertDemandSongInfo(songInfo)

            Log.d(TAG, "addTopSongInfo: 成功添加歌曲 ${songInfoBean.song_name} 到已点列表顶部")
        }
    }

    /**
     * 删除所有的已点歌曲
     */
    suspend fun deleteAllDemandSongInfo() {
        getDemandDao().deleteAllDemandSongInfo()
    }

    /**
     * 获取下一首要播放的歌曲信息
     */
    suspend fun getNextPlaySongInfo(): DemandSongInfo? {
        return try {
            // 使用数据库专用线程池，避免阻塞主线程
            withContext(DaemonThreadDispatcher.Database) {
                val songInfoList = getDemandDao().getAllDemandSongInfoWithOutPlaying()
                if (songInfoList.isEmpty()) {
                    null
                } else {
                    val songInfo = songInfoList[0]
                    Log.d(TAG, "getNextPlaySongInfo song_name: ${songInfo.songInfo.song_name}")
                    songInfo
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "getNextPlaySongInfo error: ${e.message}", e)
            null
        }
    }

    /**
     * 删除已点歌曲
     */
    suspend fun deleteDemandSongInfoById(songId: String) {
        try {
            // 使用数据库专用线程池，避免阻塞主线程
            withContext(DaemonThreadDispatcher.Database) {
                getDemandDao().deleteDemandSongInfo(songId)
            }
        } catch (e: Exception) {
            Log.e(TAG, "deleteDemandSongInfoById error: ${e.message}", e)
        }
    }

    /**
     * 随机打乱已点列表
     */
    suspend fun shuffleDemandList() {
        val list = getDemandSongInfoListWithOutPlaying()
        list.shuffle()
        val baseTime = System.currentTimeMillis()
        for (i in list.indices) {
            val songInfoBean = list[i]
            // 为每个歌曲设置不同的时间戳，确保排序稳定
            songInfoBean.insertTime = baseTime + i
        }
        getDemandDao().update(list)
    }

    /**
     * 获取已点歌曲列表LiveData
     */
    fun getAllDemandSongInfoLiveData(): LiveData<MutableList<DemandSongInfo>> {
        return getDemandDao().getAllDemandSongInfoLiveData()
    }

    /**
     * 获取已点歌曲列表LiveData，包含正在播放的歌曲
     */
    fun getAllDemandSongInfoWithPlayingLiveData(): LiveData<MutableList<DemandSongInfo>> {
        return getDemandDao().getAllDemandSongInfoWithPlayingLiveData()
    }

    /**
     * 把当前歌曲设置成正在播放的歌曲
     * 优化版本：修复快速切歌时歌曲丢失问题，确保所有歌曲状态变更都能被正确处理
     */
    suspend fun updatePlayingSongInfo(songInfo: SongInfoBean) {
        // 数据验证
        if (songInfo.song_name.isNullOrBlank() || songInfo.singer_name.isNullOrBlank()) {
            Log.e(TAG, "尝试设置无效歌曲为正在播放: song_id=${songInfo.song_id}, song_name='${songInfo.song_name}', singer_name='${songInfo.singer_name}'")
            return
        }

        // 使用专用线程池，避免在主线程执行耗时操作
        withContext(DaemonThreadDispatcher.Database) {
            // 修复：使用lock()而不是tryLock()，确保所有歌曲状态变更都能被处理
            // 添加超时机制避免死锁
            try {
                withTimeout(10000) { // 10秒超时
                    updatePlayingMutex.lock()
                }
            } catch (e: TimeoutCancellationException) {
                Log.e(TAG, "updatePlayingSongInfo: 获取锁超时，可能存在死锁问题")
                return@withContext
            }

            try {
                isUpdatingPlaying = true
                Log.d(TAG, "updatePlayingSongInfo: 开始设置正在播放歌曲 ${songInfo.song_name}")

                val demandDao = getDemandDao()

                // 使用数据库事务确保原子性
                DbManager.runInTransaction {
                    // 1. 先处理当前正在播放的歌曲
                    val currentPlayingSong = demandDao.getPlayingSongInfo()
                    currentPlayingSong?.let { playingSong ->
                        Log.d(TAG, "updatePlayingSongInfo: 当前播放歌曲 ${playingSong.songInfo.song_name}")

                        // 只有当新歌曲与当前播放歌曲不同时才处理
                        if (songInfo.song_id != playingSong.songInfo.song_id) {
                            Log.d(TAG, "updatePlayingSongInfo: 处理旧的播放歌曲，移动到已唱列表")

                            // 修复：移除已唱列表检查，确保歌曲能被正确处理
                            // 即使歌曲在已唱列表中，也要确保状态正确更新

                            // 设置为非播放状态
                            playingSong.songInfo.isPlaying = false

                            // 从已点列表删除
                            demandDao.deleteDemandSongInfo(playingSong)

                            // 检查歌曲是否已在已唱列表中，如果在则先移除再添加（确保顺序正确）
                            val existsInSungList = SungListManager.getSungListLiveData().value
                                ?.any { it.song_id == playingSong.songInfo.song_id } ?: false

                            if (existsInSungList) {
                                Log.d(TAG, "updatePlayingSongInfo: 歌曲 ${playingSong.songInfo.song_name} 已在已唱列表中，先移除再重新添加确保顺序")
                                SungListManager.removeFromSungList(playingSong.songInfo)
                            }

                            // 添加到已唱歌曲（确保总是添加）
                            SungListManager.addToSungList(playingSong.songInfo)

                            // 添加到播放历史记录
                            addPlayRecord(playingSong.songInfo)

                            Log.d(TAG, "updatePlayingSongInfo: 成功将 ${playingSong.songInfo.song_name} 移动到已唱列表")
                        } else {
                            Log.d(TAG, "updatePlayingSongInfo: 相同歌曲，无需处理")
                            return@runInTransaction
                        }
                    }

                    // 2. 处理新的播放歌曲
                    Log.d(TAG, "updatePlayingSongInfo: 设置新歌曲为正在播放")

                    // 从已唱列表移除（如果存在）
                    val existsInSungList = SungListManager.getSungListLiveData().value
                        ?.any { it.song_id == songInfo.song_id } ?: false
                    if (existsInSungList) {
                        Log.d(TAG, "updatePlayingSongInfo: 从已唱列表移除歌曲 ${songInfo.song_name}")
                        SungListManager.removeFromSungList(songInfo)
                    }

                    // 检查新歌曲是否已在已点列表中
                    var newPlayingSong = demandDao.getSongInfoById(songInfo.song_id)
                    if (newPlayingSong != null) {
                        // 如果已存在，先删除再重新插入（确保状态正确）
                        Log.d(TAG, "updatePlayingSongInfo: 歌曲 ${songInfo.song_name} 已在已点列表中，更新状态")
                        demandDao.deleteDemandSongInfo(newPlayingSong)
                        newPlayingSong.songInfo.isPlaying = true
                    } else {
                        // 如果不存在，创建新的记录
                        Log.d(TAG, "updatePlayingSongInfo: 歌曲 ${songInfo.song_name} 不在已点列表中，创建新记录")
                        songInfo.isPlaying = true
                        newPlayingSong = DemandSongInfo(songInfo = songInfo)
                    }

                    // 插入新的正在播放歌曲
                    demandDao.insertDemandSongInfo(newPlayingSong)

                    Log.d(TAG, "updatePlayingSongInfo: 完成设置正在播放歌曲 ${songInfo.song_name}")

                    // 执行数据一致性检查（异步，不阻塞主流程）
                    ioLaunch {
                        try {
                            // 延迟执行，确保当前事务完成
                            kotlinx.coroutines.delay(100)
                            checkAndRepairSongConsistency()
                        } catch (e: Exception) {
                            Log.e(TAG, "数据一致性检查失败: ${e.message}", e)
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "updatePlayingSongInfo 执行失败: ${e.message}", e)
            } finally {
                isUpdatingPlaying = false
                updatePlayingMutex.unlock()
            }
        }
    }

    /**
     * 把当前歌曲设置成已经播放的歌曲
     * 优化版本：修复快速切歌时歌曲丢失问题，确保所有歌曲都能被正确处理
     */
    fun updateSungList(mid: String) {
        ioLaunch {
            // 使用专用线程池，确保不在主线程执行
            withContext(DaemonThreadDispatcher.Database) {
                // 修复：使用lock()而不是tryLock()，确保所有歌曲都能被处理
                // 添加超时机制避免死锁
                try {
                    withTimeout(10000) { // 10秒超时
                        updateSungMutex.lock()
                    }
                } catch (e: TimeoutCancellationException) {
                    Log.e(TAG, "updateSungList: 获取锁超时，可能存在死锁问题")
                    return@withContext
                }

                try {
                    isUpdatingSung = true
                    Log.d(TAG, "updateSungList: 开始处理歌曲 $mid")

                    val demandDao = getDemandDao()

                    // 使用数据库事务确保原子性
                    DbManager.runInTransaction {
                        // 1. 数据一致性检查：确保歌曲存在且状态正确
                        val songInfoById = demandDao.getSongInfoById(mid)
                        if (songInfoById == null) {
                            Log.w(TAG, "updateSungList: 歌曲 $mid 不存在于已点列表中，可能已被处理")
                            return@runInTransaction
                        }

                        Log.d(TAG, "updateSungList: 找到歌曲 ${songInfoById.songInfo.song_name}")

                        // 2. 验证歌曲状态，确保数据一致性
                        if (!songInfoById.songInfo.isPlaying) {
                            Log.w(TAG, "updateSungList: 歌曲 ${songInfoById.songInfo.song_name} 不是正在播放状态，可能存在状态不一致")
                        }

                        // 3. 在主线程更新UI状态（必须在删除数据库记录之前）
                        mainLaunch {
                            // 清空当前播放信息
                            KaraokeConsole.currSongInfo = null
                            KaraokeConsole.currSongInfoLiveData.value = null
                        }

                        // 4. 按顺序执行数据库操作（在事务中确保原子性）
                        Log.d(TAG, "updateSungList: 从已点列表删除歌曲")
                        demandDao.deleteDemandSongInfo(songInfoById)

                        Log.d(TAG, "updateSungList: 添加到已唱列表")
                        SungListManager.addToSungList(songInfoById.songInfo)

                        Log.d(TAG, "updateSungList: 添加播放记录")
                        addPlayRecord(songInfoById.songInfo)

                        Log.d(TAG, "updateSungList: 完成处理歌曲 ${songInfoById.songInfo.song_name}")
                    }

                    // 5. 发送通知
                    _playRecordUpdated.emit(Unit)

                    // 6. 执行数据一致性检查（异步，不阻塞主流程）
                    ioLaunch {
                        try {
                            // 延迟执行，确保当前事务完成
                            kotlinx.coroutines.delay(100)
                            checkAndRepairSongConsistency()
                        } catch (e: Exception) {
                            Log.e(TAG, "updateSungList数据一致性检查失败: ${e.message}", e)
                        }
                    }

                } catch (e: Exception) {
                    Log.e(TAG, "updateSungList 执行失败: ${e.message}", e)
                } finally {
                    isUpdatingSung = false
                    updateSungMutex.unlock()
                }
            }
        }
    }

    /**
     * 数据一致性检查和修复
     * 优化版本：使用超时机制和轻量级检查，避免启动时阻塞过久
     */
    suspend fun validateAndRepairDataConsistency() {
        // 使用专用线程池，避免阻塞主线程
        withContext(DaemonThreadDispatcher.Database) {
            try {
                Log.d(TAG, "validateAndRepairDataConsistency: 开始数据一致性检查")

                val demandDao = getDemandDao()

                // 使用超时机制，避免长时间阻塞
                withTimeout(5000) { // 5秒超时
                    DbManager.runInTransaction {
                        // 1. 快速检查是否有多个正在播放的歌曲
                        val playingSongs = demandDao.getAllDemandSongInfo().filter { it.songInfo.isPlaying }

                        if (playingSongs.size > 1) {
                            Log.w(TAG, "validateAndRepairDataConsistency: 发现 ${playingSongs.size} 个正在播放的歌曲，进行修复")

                            // 保留最新的一个，其他的设为非播放状态
                            val latestPlayingSong = playingSongs.maxByOrNull { it.insertTime }
                            playingSongs.forEach { song ->
                                if (song.demandId != latestPlayingSong?.demandId) {
                                    song.songInfo.isPlaying = false
                                    demandDao.insertDemandSongInfo(song)
                                    Log.d(TAG, "validateAndRepairDataConsistency: 修复歌曲 ${song.songInfo.song_name} 的播放状态")
                                }
                            }
                        }

                        // 2. 轻量级数据完整性检查
                        val totalSongs = demandDao.getAllDemandSongInfo().size
                        Log.d(TAG, "validateAndRepairDataConsistency: 总歌曲数=$totalSongs, 播放歌曲数=${playingSongs.size}")

                        // 3. 快速清理明显无效的数据（只检查关键字段）
                        val invalidSongs = demandDao.getAllDemandSongInfo().filter {
                            it.songInfo.song_id.isBlank() || it.songInfo.song_name.isBlank()
                        }

                        if (invalidSongs.isNotEmpty()) {
                            Log.w(TAG, "validateAndRepairDataConsistency: 发现 ${invalidSongs.size} 首无效歌曲，进行清理")
                            invalidSongs.forEach { invalidSong ->
                                demandDao.deleteDemandSongInfo(invalidSong)
                                Log.d(TAG, "validateAndRepairDataConsistency: 删除无效歌曲 song_id=${invalidSong.songInfo.song_id}")
                            }
                        }

                        Log.d(TAG, "validateAndRepairDataConsistency: 数据一致性检查完成")
                    }
                }
            } catch (e: TimeoutCancellationException) {
                Log.w(TAG, "validateAndRepairDataConsistency: 检查超时，跳过本次检查")
            } catch (e: Exception) {
                Log.e(TAG, "validateAndRepairDataConsistency 执行失败: ${e.message}", e)
            }
        }
    }

    /**
     * 检查和修复歌曲数据一致性
     * 修复快速切歌时可能出现的歌曲丢失问题
     */
    private suspend fun checkAndRepairSongConsistency() {
        try {
            withContext(DaemonThreadDispatcher.Database) {
                val demandDao = getDemandDao()

                // 获取当前已点列表中的所有歌曲
                val demandSongs = demandDao.getAllDemandSongInfo()
                val playingSongs = demandSongs.filter { it.songInfo.isPlaying }

                // 获取已唱列表中的歌曲
                val sungSongs = SungListManager.getSungListLiveData().value ?: mutableListOf()

                Log.d(TAG, "checkAndRepairSongConsistency: 已点列表=${demandSongs.size}, 正在播放=${playingSongs.size}, 已唱列表=${sungSongs.size}")

                // 检查是否有多个正在播放的歌曲
                if (playingSongs.size > 1) {
                    Log.w(TAG, "checkAndRepairSongConsistency: 发现多个正在播放的歌曲，进行修复")
                    val latestPlayingSong = playingSongs.maxByOrNull { it.insertTime }
                    playingSongs.forEach { song ->
                        if (song.demandId != latestPlayingSong?.demandId) {
                            song.songInfo.isPlaying = false
                            demandDao.insertDemandSongInfo(song)
                            Log.d(TAG, "checkAndRepairSongConsistency: 修复歌曲 ${song.songInfo.song_name} 的播放状态")
                        }
                    }
                }

                // 检查是否有歌曲既在已点列表又在已唱列表中（除了正在播放的）
                val nonPlayingDemandSongs = demandSongs.filter { !it.songInfo.isPlaying }
                val duplicateSongs = nonPlayingDemandSongs.filter { demandSong ->
                    sungSongs.any { sungSong -> sungSong.song_id == demandSong.songInfo.song_id }
                }

                if (duplicateSongs.isNotEmpty()) {
                    Log.w(TAG, "checkAndRepairSongConsistency: 发现 ${duplicateSongs.size} 首重复歌曲，从已点列表移除")
                    duplicateSongs.forEach { duplicateSong ->
                        demandDao.deleteDemandSongInfo(duplicateSong)
                        Log.d(TAG, "checkAndRepairSongConsistency: 从已点列表移除重复歌曲 ${duplicateSong.songInfo.song_name}")
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "checkAndRepairSongConsistency 执行失败: ${e.message}", e)
        }
    }

}