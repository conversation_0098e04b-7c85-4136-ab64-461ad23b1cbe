package com.sgmw.ksongs.ui.category

import android.widget.ImageView
import android.widget.TextView
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.sgmw.common.utils.GlideUtil
import com.sgmw.ksongs.R
import com.sgmw.ksongs.model.bean.CategoryThemeBean
import com.sgmw.ksongs.ui.adapter.BaseAdapter

class CategoryListAdapter : BaseAdapter<CategoryThemeBean>(R.layout.item_category) {

    private val iconRadius = 12
    override fun convert(holder: BaseViewHolder, item: CategoryThemeBean) {
        val tvThemeName = holder.getView<TextView>(R.id.tvThemeName)
        val ivThemeBg = holder.getView<ImageView>(R.id.ivThemeBg)
        GlideUtil.loadRoundCornerImage(holder.itemView.context, item.theme_pic,
            ivThemeBg, iconRadius, R.mipmap.icon_music_default_bg, R.mipmap.icon_music_default_bg)
        tvThemeName.text = item.theme_name
    }


}