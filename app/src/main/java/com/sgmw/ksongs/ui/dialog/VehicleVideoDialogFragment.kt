package com.sgmw.ksongs.ui.dialog

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.ComponentName
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.R
import com.sgmw.ksongs.databinding.DialogVehicleVideoBinding
import com.sgmw.ksongs.viewmodel.MainViewModel

/**
 * @author: 董俊帅
 * @time: 2025/3/20
 * @desc: 行车视频限制弹窗
 */

class VehicleVideoDialogFragment : BaseBlurDialogFragment(R.layout.dialog_vehicle_video) {

    private var countdownTimer: CountDownTimer? = null
    private val PACKAGE_NAME = "com.sgmw.vehiclesettings"
    private val CLASS_NAME = "com.sgmw.vehiclesettings.ui.main.MainActivity"
    private val mBinding: DialogVehicleVideoBinding by lazy {
        DialogVehicleVideoBinding.inflate(layoutInflater)
    }
    private val mainViewModel by lazy {
        ViewModelProvider(requireActivity())[MainViewModel::class.java]
    }

    /**
     * 是否需要返回播放页面之前的页面
     */
    private var isNeedBackPlayPage  = true

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return super.onCreateDialog(savedInstanceState).apply {
            // 拦截返回键事件
            setOnKeyListener { _, keyCode, event ->
                if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP) {
                    findNavController().popBackStack()
                    true
                } else {
                    false
                }
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        isCancelable = true
        dialog?.setCanceledOnTouchOutside(true)
        return mBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
    }

    private fun initView() {
        mBinding.apply {
            mBinding.dialogContent.setOnSingleClickListener {}
            root.setOnSingleClickListener {
                dismiss()
            }
            ivBack.setOnSingleClickListener {
                dismiss()
            }
            btnConfirm.setOnSingleClickListener {
                dismiss()
            }
            btnGoSetting.setOnSingleClickListener {
                isNeedBackPlayPage = false
                dismiss()
                goSetting(requireContext())
            }
        }
        startCountdown()
    }

    private fun startCountdown() {
        var secondsLeft = 5 // 假设倒计时5秒
        countdownTimer?.cancel()
        countdownTimer = object : CountDownTimer(5000, 1000) {
            @SuppressLint("SetTextI18n")
            override fun onTick(millisUntilFinished: Long) {
                Log.d(TAG, "onTick: $secondsLeft")
                mBinding.btnConfirm.text = "确认（${secondsLeft}s）"
                secondsLeft--
            }
            override fun onFinish() {
                dismiss()
            }
        }
        countdownTimer?.start()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        Log.d(TAG, "onDestroyView: ")
        countdownTimer?.cancel()
    }

    private fun goSetting(context: Context) {
        val intent = Intent(Intent.ACTION_MAIN)
        intent.component =
            ComponentName(
                PACKAGE_NAME,
                CLASS_NAME
            )
        intent.putExtra("vr", 2)
        intent.putExtra("drivingVideoPosition", 1)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(intent)
    }

    override fun onDismiss(dialog: DialogInterface) {
        Log.d(TAG, "onDismiss")
        countdownTimer?.cancel()
        super.onDismiss(dialog)
        if (isNeedBackPlayPage) {
            // 如果在播放页面，则隐藏播放页面，否则不做处理
            val isSongPlayViewShow = mainViewModel.isSongPlayViewShow.value
            Log.d(TAG, "onDismiss isSongPlayViewShow: $isSongPlayViewShow")
            if (isSongPlayViewShow == true) {
                requireActivity().onBackPressedDispatcher.onBackPressed()
            }
        }
        isNeedBackPlayPage = true
    }

    companion object {
        private const val TAG = "VehicleVideoDialogFragment"
    }

}