package com.sgmw.ksongs.ui.dialog

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.R
import com.sgmw.ksongs.databinding.DialogClearCacheBinding
import com.tencent.karaoke.download.cache.DownloadCacheManager
import java.util.concurrent.Executors

class ClearCacheDialogFragment: BaseBlurDialogFragment(R.layout.dialog_clear_cache) {

    private val mBinding: DialogClearCacheBinding by lazy {
        DialogClearCacheBinding.inflate(layoutInflater)
    }

    private var onClearCacheListener: OnClearCacheListener? = null
    private val executor = Executors.newSingleThreadExecutor()
    private val mainHandler = Handler(Looper.getMainLooper())
    private var isDismissed = false
    // 保存超时任务的引用，用于清理
    private var timeoutRunnable: Runnable? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        dialog?.setCanceledOnTouchOutside(true)
        return mBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Log.d(TAG, "onViewCreated onClearCacheListener is null: ${onClearCacheListener == null}")
        
        // 设置5秒超时，防止弹框卡死
        timeoutRunnable = Runnable {
            if (!isDismissed) {
                Log.w(TAG, "缓存清理超时，强制关闭弹框")
                isDismissed = true
                onClearCacheListener?.onFailed()
                dismiss()
            }
        }
        timeoutRunnable?.let { mainHandler.postDelayed(it, 5000) }
        
        // 在后台线程执行缓存清理
        // 因为 DownloadCacheManager.getInstance().trimSpace(true) 是post了64毫秒才执行的
        // 所以这里放到后台执行，避免主线程卡顿
        executor.execute {
            try {
                Log.d(TAG, "开始清理缓存")
                DownloadCacheManager.getInstance().trimSpace(true)
                Log.d(TAG, "缓存清理成功")
                
                // 在主线程回调成功
                mainHandler.post {
                    if (!isDismissed) {
                        isDismissed = true
                        onClearCacheListener?.onSuccess()
                        dismiss()
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "缓存清理失败", e)
                
                // 在主线程回调失败
                mainHandler.post {
                    if (!isDismissed) {
                        isDismissed = true
                        onClearCacheListener?.onFailed()
                        dismiss()
                    }
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        isDismissed = true

        // 清理Handler回调，防止内存泄露
        timeoutRunnable?.let { mainHandler.removeCallbacks(it) }
        timeoutRunnable = null

        // 清理监听器引用
        onClearCacheListener = null

        executor.shutdown()
    }

    fun setOnClearCacheListener(listener: OnClearCacheListener){
        onClearCacheListener = listener
    }

    interface OnClearCacheListener {
          fun onSuccess()
          fun onFailed()
    }

    companion object {
        private const val TAG = "ClearCacheDialogFragment"
    }

}