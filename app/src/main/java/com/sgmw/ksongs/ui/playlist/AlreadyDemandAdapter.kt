package com.sgmw.ksongs.ui.playlist

import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.sgmw.common.utils.GlideUtil
import com.sgmw.ksongs.R
import com.sgmw.ksongs.db.entity.DemandSongInfo
import com.sgmw.ksongs.ui.adapter.BaseAdapter
import com.sgmw.ksongs.ui.songplay.KaraokeConsole
import com.sgmw.ksongs.utils.collectOrCancelSongInfo
import com.sgmw.ksongs.utils.visibleOrGone

/**
 * @author: 董俊帅
 * @time: 2025/1/19
 * @desc: 已点列表适配器
 */
class AlreadyDemandAdapter: BaseAdapter<DemandSongInfo>(R.layout.item_already_demand) {

    private var iconRadius = 12

    override fun convert(holder: BaseViewHolder, item: DemandSongInfo) {
        val songInfo = item.songInfo

        val tvNum = holder.getView<TextView>(R.id.tvNum)
        val tvSongName = holder.getView<TextView>(R.id.tvSongName)
        val tvSongAuthor = holder.getView<TextView>(R.id.tvSongAuthor)
        val ivPlayStatus = holder.getView<ImageView>(R.id.ivPlayStatus)
        val ivVip = holder.getView<ImageView>(R.id.ivVip)
        val ivMv = holder.getView<ImageView>(R.id.ivMv)
        val ivScore = holder.getView<ImageView>(R.id.ivScore)
        val ivCover = holder.getView<ImageView>(R.id.ivCover)

        tvNum.text = (holder.layoutPosition + 1).toString()
        tvSongName.text = songInfo.song_name
        tvSongAuthor.text = songInfo.singer_name
        GlideUtil.loadRoundCornerImage(holder.itemView.context, songInfo.album_img,
            ivCover, iconRadius, R.mipmap.icon_music_default_bg, R.mipmap.icon_music_default_bg)

        if (KaraokeConsole.currSongInfo?.song_id == songInfo.song_id) {
            ivPlayStatus.visibility = View.VISIBLE
            tvNum.visibility = View.GONE
            holder.itemView.isSelected = true
            tvSongName.isSelected = true
            tvSongAuthor.isSelected = true
        } else {
            ivPlayStatus.visibility = View.INVISIBLE
            tvNum.visibility = View.VISIBLE
            holder.itemView.isSelected = false
            tvSongName.isSelected = false
            tvSongAuthor.isSelected = false
        }

        ivVip.visibleOrGone(songInfo.need_vip)
        ivMv.visibleOrGone(songInfo.has_mv)
        ivScore.visibleOrGone(songInfo.has_midi)

        holder.itemView.contentDescription = "播放${tvSongName.text};${tvSongName.text}"
    }

}