package com.sgmw.ksongs.ui.ranklist

import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.sgmw.ksongs.track.BigDataConstants
import com.sgmw.ksongs.track.BigDataConstants.SPLIT
import com.sgmw.ksongs.utils.HotRank
import com.sgmw.ksongs.utils.RANK_TYPE_AGE_LIST
import com.sgmw.ksongs.utils.RankInfo
import com.sgmw.ksongs.utils.RankType
import com.sgmw.ksongs.utils.getRankInfoList


class RankHomePageAdapter(fragment: Fragment, @RankType val type: Int) : FragmentStateAdapter(fragment) {


    private val rankInfoList = getRankInfoList(type)

    override fun getItemCount(): Int {
        return rankInfoList.size
    }

    override fun createFragment(position: Int): Fragment {
        return RankListFragment.newInstance(getRankInfo(position),getRankType(type) + SPLIT + getTitle(position))
    }

    private fun getRankInfo(position: Int): RankInfo {
        return rankInfoList.getOrElse(position) { HotRank }
    }

    fun getTitle(position: Int): String {
        return getRankInfo(position).title
    }
    fun getRankType (type:Int):String{
        return  when (type) {
            RANK_TYPE_AGE_LIST -> {
                BigDataConstants.AGE_RANK
            }

            else -> {
                BigDataConstants.HOT_RANK
            }
        }
    }



}