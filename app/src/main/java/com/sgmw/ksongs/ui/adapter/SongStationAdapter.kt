package com.sgmw.ksongs.ui.adapter

import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.Fragment
import androidx.navigation.Navigation
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.util.getItemView
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.R
import com.sgmw.ksongs.model.bean.BaseMultiQuickItem
import com.sgmw.ksongs.model.bean.BaseMultiQuickItem.Companion.HOT_AGES
import com.sgmw.ksongs.model.bean.BaseMultiQuickItem.Companion.HOT_LIKE
import com.sgmw.ksongs.model.bean.BaseMultiQuickItem.Companion.HOT_RANKINGS
import com.sgmw.ksongs.model.bean.BaseMultiQuickItem.Companion.HOT_SINGER
import com.sgmw.ksongs.model.bean.BaseMultiQuickItem.Companion.HOT_TOPICS
import com.sgmw.ksongs.model.bean.BaseMultiQuickItem.Companion.STATION_CARD
import com.sgmw.ksongs.model.bean.CardItem
import com.sgmw.ksongs.model.bean.HotAgeItem
import com.sgmw.ksongs.model.bean.HotLikeItem
import com.sgmw.ksongs.model.bean.HotRankingsItem
import com.sgmw.ksongs.model.bean.HotSingerItem
import com.sgmw.ksongs.model.bean.HotTopicsItem
import com.sgmw.ksongs.model.bean.SingerBean
import com.sgmw.ksongs.model.bean.SongInfoBean
import com.sgmw.ksongs.track.BigDataConstants
import com.sgmw.ksongs.track.BigDataConstants.SPLIT
import com.sgmw.ksongs.track.SensorsDataManager
import com.sgmw.ksongs.ui.collect.CollectFragment
import com.sgmw.ksongs.ui.ranklist.RankHomeFragment
import com.sgmw.ksongs.ui.record.PlayRecordFragment
import com.sgmw.ksongs.ui.singerlist.SongListBySingerFragment
import com.sgmw.ksongs.ui.songplay.KaraokePlayerManager
import com.sgmw.ksongs.utils.NavigationUtils
import com.sgmw.ksongs.utils.RANK_POSITION_00
import com.sgmw.ksongs.utils.RANK_POSITION_60
import com.sgmw.ksongs.utils.RANK_POSITION_70
import com.sgmw.ksongs.utils.RANK_POSITION_80
import com.sgmw.ksongs.utils.RANK_POSITION_90
import com.sgmw.ksongs.utils.RANK_POSITION_WEEK
import com.sgmw.ksongs.widget.AccessibilityGridLayoutManager
import com.sgmw.ksongs.widget.AccessibilityLinearLayoutManager
import com.sgmw.ksongs.widget.SpaceItemDecoration

class SongStationAdapter(private val fragment: Fragment) :
    BaseAdapter<BaseMultiQuickItem>(R.layout.item_song_station_card) {
    companion object {
        const val TAG = "SongStationAdapter"
    }

    /**
     * 猜你喜欢列数
     */
    private val columnsNumber = 2

    // 热门歌手
    private var mHotSingerAdapter: HotSingerAdapter? = null

    // 热门榜
    private var mHotRankingsAdapter: HotRankingsAdapter? = null

    // 本周排行榜
    private var mHotWeeklyRankingsAdapter: HotRankingsAdapter? = null

    // 猜你喜欢
    private var mHotLikeAdapter: HotLikeAdapter? = null

    // 热门专题
    private var mHotTopicsAdapter: HotTopicsAdapter? = null

    var onHotTopicItemClickListener: ((position: Int) -> Unit)? = null
    override fun getDefItemViewType(position: Int): Int {
        return data[position].itemType
    }

    override fun convert(holder: BaseViewHolder, item: BaseMultiQuickItem) {
        Log.d(TAG, "convert item == ${item?.itemType}")
        if (item.itemType == HOT_SINGER) {
            (item.data as? HotSingerItem)?.let {
                convertHotSinger(holder, it)
            }
        } else if (item.itemType == HOT_RANKINGS) {
            (item.data as? HotRankingsItem)?.let {
                convertHotRankings(holder, it)
            }
            val title = holder.getView<TextView>(R.id.tv_title)
            val tvHotTitle = holder.getView<TextView>(R.id.tv_hot_title)
            val tvWeeklyTitle = holder.getView<TextView>(R.id.tv_weekly_title)
            holder.getViewOrNull<TextView>(R.id.tv_title)?.setOnSingleClickListener {
                SensorsDataManager.trackSongStationEvent(title.text.toString())
                NavigationUtils.navigateSafely(
                    Navigation.findNavController(it),
                    R.id.action_home_to_rank,
                    RankHomeFragment.createBundleWithDefault()
                )
            }
            holder.getViewOrNull<View>(R.id.iv_icon)?.setOnSingleClickListener {
                SensorsDataManager.trackSongStationEvent(title.text.toString())
                NavigationUtils.navigateSafely(
                    Navigation.findNavController(it),
                    R.id.action_home_to_rank,
                    RankHomeFragment.createBundleWithDefault()
                )

            }
            holder.getViewOrNull<View>(R.id.tv_hot_title)?.setOnSingleClickListener {
                SensorsDataManager.trackSongStationEvent(title.text.toString()+ SPLIT +tvHotTitle.text.toString())
                NavigationUtils.navigateSafely(
                    Navigation.findNavController(it),
                    R.id.action_home_to_rank,
                    RankHomeFragment.createBundleWithDefault()
                )
            }
            holder.getViewOrNull<View>(R.id.iv_hot_icon)?.setOnSingleClickListener {
                SensorsDataManager.trackSongStationEvent(title.text.toString()+ SPLIT +tvHotTitle.text.toString())
                NavigationUtils.navigateSafely(
                    Navigation.findNavController(it),
                    R.id.action_home_to_rank,
                    RankHomeFragment.createBundleWithDefault()
                )
            }
            holder.getViewOrNull<View>(R.id.tv_weekly_title)?.setOnSingleClickListener {
                SensorsDataManager.trackSongStationEvent(title.text.toString()+ SPLIT +tvWeeklyTitle.text.toString())
                NavigationUtils.navigateSafely(
                    Navigation.findNavController(it),
                    R.id.action_home_to_rank,
                    RankHomeFragment.createBundleWithDefault(RANK_POSITION_WEEK)
                )
            }
            holder.getViewOrNull<View>(R.id.iv_weekly_icon)?.setOnSingleClickListener {
                SensorsDataManager.trackSongStationEvent(title.text.toString()+ SPLIT +tvWeeklyTitle.text.toString())
                NavigationUtils.navigateSafely(
                    Navigation.findNavController(it),
                    R.id.action_home_to_rank,
                    RankHomeFragment.createBundleWithDefault(RANK_POSITION_WEEK)
                )
            }
        } else if (item.itemType == HOT_LIKE) {
            (item.data as? HotLikeItem)?.let {
                convertHotLike(holder, it)
            }
            val title = holder.getView<TextView>(R.id.tv_title)
            holder.getViewOrNull<View>(R.id.tv_title)?.setOnSingleClickListener {
                SensorsDataManager.trackSongStationEvent(title.text.toString())
                NavigationUtils.navigateSafely(Navigation.findNavController(it), R.id.action_home_to_user_feature_song)

            }
            holder.getViewOrNull<View>(R.id.iv_icon)?.setOnSingleClickListener {
                SensorsDataManager.trackSongStationEvent(title.text.toString())
                NavigationUtils.navigateSafely(Navigation.findNavController(it), R.id.action_home_to_user_feature_song)
            }
        } else if (item.itemType == HOT_AGES) {
            (item.data as? HotAgeItem)?.let {
                convertHotAge(holder, it)
            }
        } else if (item.itemType == HOT_TOPICS) {
            (item.data as? HotTopicsItem)?.let {
                convertHotTopics(holder, it)
            }
            val cardTitle = holder.getView<TextView>(R.id.tv_title).text.toString()
            holder.getViewOrNull<View>(R.id.tv_title)?.setOnSingleClickListener {
                SensorsDataManager.trackSongStationEvent(cardTitle)
                onHotTopicItemClickListener?.invoke(0)
            }
            holder.getViewOrNull<ImageView>(R.id.iv_icon)?.setOnSingleClickListener {
                SensorsDataManager.trackSongStationEvent(cardTitle)
                onHotTopicItemClickListener?.invoke(0)
            }
        } else if (item.itemType == STATION_CARD) {
            (item.data as? CardItem)?.let {
                convertCardItem(holder, it)
            }
        }

    }


    /**
     *处理卡片
     */
    private fun convertCardItem(holder: BaseViewHolder, it: CardItem) {
        val tvVip = holder.getView<TextView>(R.id.tv_vip)
        val tvCategory = holder.getView<TextView>(R.id.tv_category)
        val tvRecord = holder.getView<TextView>(R.id.tv_record)
        val tvCollect = holder.getView<TextView>(R.id.tv_collect)
        tvCategory.setOnSingleClickListener {
            SensorsDataManager.trackSongStationEvent(tvCategory.text.toString())
            NavigationUtils.navigateSafely(Navigation.findNavController(it), R.id.action_home_to_category)
        }
        tvRecord.setOnSingleClickListener {
            SensorsDataManager.trackSongStationEvent(tvRecord.text.toString())
            NavigationUtils.navigateSafely(Navigation.findNavController(it), R.id.action_home_to_play_record,PlayRecordFragment.createBundle(
            BigDataConstants.CARD_NAME_SONGSTATION))
        }
        tvCollect.setOnSingleClickListener {
            SensorsDataManager.trackSongStationEvent(tvCollect.text.toString())
            NavigationUtils.navigateSafely(Navigation.findNavController(it), R.id.action_home_to_collect,
                CollectFragment.createBundle(BigDataConstants.CARD_NAME_SONGSTATION))
        }
        tvVip.setOnSingleClickListener {
            SensorsDataManager.trackSongStationEvent(tvVip.text.toString())
            NavigationUtils.navigateSafely(Navigation.findNavController(it), R.id.action_home_to_vip_payment)
        }
    }


    /**
     * 处理专题
     */
    private fun convertHotTopics(holder: BaseViewHolder, item: HotTopicsItem) {
        val title = holder.getView<TextView>(R.id.tv_title)
        title.text = item.title
        if (mHotTopicsAdapter != null) {
            mHotTopicsAdapter?.setNewInstance(item.songList)
            return
        }
        mHotTopicsAdapter = HotTopicsAdapter()
        val rvSingerList = holder.getView<RecyclerView>(R.id.rv_topics)
        rvSingerList.adapter = mHotTopicsAdapter
        rvSingerList.layoutManager = AccessibilityLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        rvSingerList.addItemDecoration(SpaceItemDecoration(rightSpace = 32))
        mHotTopicsAdapter?.setNewInstance(item.songList)
        mHotTopicsAdapter?.setOnItemClickListener { adapter, view, position ->
            item.songList?.get(position)?.let{
                SensorsDataManager.trackSongStationEvent(item.title + SPLIT +it.theme_name)
            }
            onHotTopicItemClickListener?.invoke(position)
        }
    }

    /**
     * 处理年代
     */
    private fun convertHotAge(holder: BaseViewHolder, item: HotAgeItem) {
        holder.getView<TextView>(R.id.tv_title).text = item.title
        val ll_age_60 = holder.getView<View>(R.id.ll_age_60)
        val ll_age_70 = holder.getView<View>(R.id.ll_age_70)
        val ll_age_80 = holder.getView<View>(R.id.ll_age_80)
        val ll_age_90 = holder.getView<View>(R.id.ll_age_90)
        val ll_age_00 = holder.getView<View>(R.id.ll_age_00)
        val card_60 =  holder.getView<TextView>(R.id.tv_age_60).text.toString()
        val card_70 =  holder.getView<TextView>(R.id.tv_age_70).text.toString()
        val card_80 =  holder.getView<TextView>(R.id.tv_age_80).text.toString()
        val card_90 =  holder.getView<TextView>(R.id.tv_age_90).text.toString()
        val card_00 =  holder.getView<TextView>(R.id.tv_age_00).text.toString()
        ll_age_60.setOnSingleClickListener {
            SensorsDataManager.trackSongStationEvent(item.title + SPLIT + card_60 )
            NavigationUtils.navigateSafely(
                Navigation.findNavController(it), R.id.navigation_rank,
                RankHomeFragment.createBundleWithAge(RANK_POSITION_60)
            )
        }
        ll_age_70.setOnSingleClickListener {
            SensorsDataManager.trackSongStationEvent(item.title + SPLIT + card_70 )
            NavigationUtils.navigateSafely(
                Navigation.findNavController(it), R.id.navigation_rank,
                RankHomeFragment.createBundleWithAge(RANK_POSITION_70)
            )
        }
        ll_age_80.setOnSingleClickListener {
            SensorsDataManager.trackSongStationEvent(item.title + SPLIT + card_80 )
            NavigationUtils.navigateSafely(
                Navigation.findNavController(it), R.id.navigation_rank,
                RankHomeFragment.createBundleWithAge(RANK_POSITION_80)
            )
        }
        ll_age_90.setOnSingleClickListener {
            SensorsDataManager.trackSongStationEvent(item.title + SPLIT + card_90 )
            NavigationUtils.navigateSafely(
                Navigation.findNavController(it), R.id.navigation_rank,
                RankHomeFragment.createBundleWithAge(RANK_POSITION_90)
            )
        }
        ll_age_00.setOnSingleClickListener {
            SensorsDataManager.trackSongStationEvent(item.title + SPLIT + card_00 )
            NavigationUtils.navigateSafely(
                Navigation.findNavController(it), R.id.navigation_rank,
                RankHomeFragment.createBundleWithAge(RANK_POSITION_00)
            )
        }
    }

    /**
     * 处理猜你喜欢
     */
    private fun convertHotLike(holder: BaseViewHolder, it: HotLikeItem) {
        Log.d(TAG, "convertHotLike == ")
        val title = holder.getView<TextView>(R.id.tv_title)
        title.text = it.title
        var hotLike = mutableListOf<SongInfoBean>()
        it.songList?.let {
            Log.d(TAG, "convertHotLike == ${it.size} ")
            it.forEach { songInfobean ->
                hotLike.add(songInfobean.deepCopy())
            }
        }
        if (mHotLikeAdapter != null) {
            // 确保数据状态同步
            hotLike.forEach { song ->
                song.isInDemandList = it.songList?.find { it.song_id == song.song_id }?.isInDemandList ?: false
            }
            val diffResult = diffResult(mHotLikeAdapter, hotLike)
            // 使用计算出的 DiffResult 更新适配器
            mHotLikeAdapter?.setDiffNewData(diffResult, hotLike)
        } else {
            mHotLikeAdapter = HotLikeAdapter()
            val rvHotLike = holder.getView<RecyclerView>(R.id.rv_like)
            rvHotLike.adapter = mHotLikeAdapter
            rvHotLike.layoutManager =
                AccessibilityGridLayoutManager(context, columnsNumber)
            mHotLikeAdapter?.setNewInstance(hotLike)
            mHotLikeAdapter?.setOnItemClickListener { adapter, view, position ->
                val songItem = mHotLikeAdapter?.getItemOrNull(position)
                if (!TextUtils.isEmpty(songItem?.song_id)) {
                    KaraokePlayerManager.playSong(fragment, songItem, it.title)
                }
            }
            rvHotLike.addItemDecoration(
                SpaceItemDecoration(
                    leftSpace = 0,
                    rightSpace = 112,
                    bottomSpace = 0,
                    gridNum = columnsNumber
                )
            )

        }
    }

    /**
     * 列表数据差分，局部更新
     */
    private fun diffResult(
        adapter: BaseAdapter<SongInfoBean>?,
        hotLike: MutableList<SongInfoBean>
    ): DiffUtil.DiffResult {
        val diffCallback = object : DiffUtil.Callback() {
            override fun getOldListSize(): Int = adapter?.data?.size ?: 0
            override fun getNewListSize(): Int = hotLike.size

            override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                val old = adapter?.data?.get(oldItemPosition)
                val new = hotLike[newItemPosition]
                return old?.song_id == new?.song_id
            }

            override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                val old = adapter?.data?.get(oldItemPosition)
                val new = hotLike[newItemPosition]
                return old?.song_id == new?.song_id && 
                       old?.isInDemandList == new?.isInDemandList && 
                       old?.isPlayingState == new?.isPlayingState &&
                       old?.isPlaying == new?.isPlaying
            }
        }

        val diffResult = DiffUtil.calculateDiff(diffCallback)
        return diffResult
    }

    /**
     * 处理热门推荐
     */
    private fun convertHotRankings(holder: BaseViewHolder, it: HotRankingsItem) {
        holder.getView<TextView>(R.id.tv_title).text = it.title
        val title = holder.getView<TextView>(R.id.tv_title)
        val tvHotTitle = holder.getView<TextView>(R.id.tv_hot_title)
        val tvWeeklyTitle = holder.getView<TextView>(R.id.tv_weekly_title)
        val hotSongList = mutableListOf<SongInfoBean>()
        val hotWeeklyList = mutableListOf<SongInfoBean>()
        it.hotSongList?.let {
            it.forEach { songInfoBean ->
                hotSongList.add(songInfoBean.deepCopy())
            }
        }
        it.hotWeeklyList?.let {
            it.forEach { songInfoBean ->
                hotWeeklyList.add(songInfoBean.deepCopy())
            }
        }
        if (mHotRankingsAdapter != null) {
            Log.d(TAG, "convertHotRankings: ${mHotRankingsAdapter?.data?.size}")
            // 确保数据状态同步
            hotSongList.forEach { song ->
                song.isInDemandList = it.hotSongList?.find { it.song_id == song.song_id }?.isInDemandList ?: false
            }
            val diffResult = diffResult(mHotRankingsAdapter, hotSongList)
            // 使用计算出的 DiffResult 更新适配器
            mHotRankingsAdapter?.setDiffNewData(diffResult, hotSongList)
        } else {
            mHotRankingsAdapter = HotRankingsAdapter(context.getString(R.string.home_title_hot_ranks))
            val rvRankingsList = holder.getView<RecyclerView>(R.id.rv_Hot)
            setRankAdapter(rvRankingsList, hotSongList, mHotRankingsAdapter,title.text.toString()+ SPLIT +tvHotTitle.text.toString())
        }

        if (mHotWeeklyRankingsAdapter != null) {
            // 确保数据状态同步
            hotWeeklyList.forEach { song ->
                song.isInDemandList = it.hotWeeklyList?.find { it.song_id == song.song_id }?.isInDemandList ?: false
            }
            val diffResult = diffResult(mHotWeeklyRankingsAdapter, hotWeeklyList)
            // 使用计算出的 DiffResult 更新适配器
            mHotWeeklyRankingsAdapter?.setDiffNewData(diffResult, hotWeeklyList)
        } else {
            mHotWeeklyRankingsAdapter = HotRankingsAdapter(context.getString(R.string.home_title_weekly))
            val rvRankingsList = holder.getView<RecyclerView>(R.id.rv_Hot_weekly)
            setRankAdapter(rvRankingsList, hotWeeklyList, mHotWeeklyRankingsAdapter,title.text.toString()+ SPLIT +tvWeeklyTitle.text.toString())
        }
    }

    /**
     * 设置排行榜适配器
     */
    private fun setRankAdapter(
        rvRankingsList: RecyclerView,
        dataList: MutableList<SongInfoBean>,
        adapter: HotRankingsAdapter?,
        cardName :String = ""
    ) {
        rvRankingsList.adapter = adapter
        rvRankingsList.layoutManager =
            AccessibilityLinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
        adapter?.setNewInstance(dataList)
        adapter?.setOnItemClickListener { _, _, position ->
            val songItem = adapter?.getItemOrNull(position)
            if (!TextUtils.isEmpty(songItem?.song_id)) {
                KaraokePlayerManager.playSong(fragment, songItem, cardName)
            }
        }
    }

    /**
     * 处理热门歌曲
     */
    private fun convertHotSinger(holder: BaseViewHolder, it: HotSingerItem) {
        Log.d(TAG, "convertHotSinger: ")
        val title = holder.getView<TextView>(R.id.tv_title)
        title.text = it.title
        title.setOnSingleClickListener {
            SensorsDataManager.trackSongStationEvent(title.text.toString())
            NavigationUtils.navigateSafely(Navigation.findNavController(it), R.id.action_home_to_hot_singer_list)
        }
        val icon = holder.getView<ImageView>(R.id.iv_icon)
        icon.setOnSingleClickListener {
            SensorsDataManager.trackSongStationEvent(title.text.toString())
            NavigationUtils.navigateSafely(Navigation.findNavController(it), R.id.action_home_to_hot_singer_list)
        }
        if (mHotSingerAdapter == null) {
            mHotSingerAdapter = HotSingerAdapter()
            mHotSingerAdapter?.setOnItemClickListener { adapter, view, position ->
                val singer = it.singerList?.get(position)
                singer?.let {
                    val cardName = title.text.toString() + SPLIT + singer.singer_name
                    SensorsDataManager.trackSongStationEvent(cardName)
                    NavigationUtils.navigateSafely(
                        Navigation.findNavController(view), R.id.action_home_to_song_by_singer,
                        SongListBySingerFragment.createBundle(it.singer_id, it.singer_name,cardName)
                    )
                }
            }
            val rvSingerList = holder.getView<RecyclerView>(R.id.rv_singer_list)
            rvSingerList.adapter = mHotSingerAdapter
            rvSingerList.layoutManager =
                AccessibilityLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            rvSingerList.addItemDecoration(SpaceItemDecoration(rightSpace = 118))
        }
        it.singerList?.let {
            val data = mutableListOf<SingerBean.Singer>()
            data.addAll(it)
            mHotSingerAdapter?.setNewInstance(data)
        }

    }


    override fun onCreateDefViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder {
        return when (viewType) {
            HOT_SINGER -> {
                createViewHolderForLayout(
                    parent,
                    R.layout.item_song_station_hot_singer
                ) {
                    null
                }
            }

            HOT_RANKINGS -> {

                return createViewHolderForLayout(
                    parent,
                    R.layout.item_song_station_hot_rankings
                ) { null }
            }

            HOT_LIKE -> {

                return createViewHolderForLayout(
                    parent,
                    R.layout.item_song_station_hot_like
                ) { null }
            }

            HOT_AGES -> {

                return createViewHolderForLayout(
                    parent,
                    R.layout.item_song_station_hot_ages
                ) { null }
            }

            HOT_TOPICS -> {
                return createViewHolderForLayout(
                    parent,
                    R.layout.item_song_station_hot_topics
                ) { null }
            }

            else -> {
                createBaseViewHolder(parent, R.layout.item_song_station_card)
            }
        }
    }


    private fun createViewHolderForLayout(
        parent: ViewGroup,
        layoutResId: Int,
        layoutField: () -> ConstraintLayout?
    ): BaseViewHolder {
        var layout = layoutField()
        if (layout == null) {
            layout = parent.getItemView(layoutResId) as? ConstraintLayout
            layoutField.invoke()?.let { layout = it }
        }
        return layout?.let { createBaseViewHolder(it) } ?: createBaseViewHolder(parent, layoutResId)
    }


}

