package com.sgmw.ksongs.ui.playlist

import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.sgmw.common.mvvm.v.BaseFrameFragment
import com.sgmw.common.utils.Log
import com.sgmw.common.widget.StateLayout
import com.sgmw.ksongs.R
import com.sgmw.ksongs.databinding.FragmentAlreadySungBinding
import com.sgmw.ksongs.phonestatus.PhoneStatusManager
import com.sgmw.ksongs.track.BigDataConstants
import com.sgmw.ksongs.ui.songplay.KaraokePlayerManager
import com.sgmw.ksongs.viewmodel.playlist.AlreadySungViewModel
import com.sgmw.ksongs.widget.AccessibilityLinearLayoutManager

/**
 * @author: 董俊帅
 * @time: 2025/1/19
 * @desc: 已唱歌曲列表
 */
class AlreadySungFragment : BaseFrameFragment<FragmentAlreadySungBinding, AlreadySungViewModel>() {

    private val mAdapter by lazy { AlreadySungAdapter() }

    override fun onResume() {
        super.onResume()
        mViewModel?.getSungList()
    }
    override fun needSkinApply() = true
    override fun FragmentAlreadySungBinding.initView() {
        mBinding?.let { binding ->
            binding.rvSung.layoutManager = AccessibilityLinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            binding.rvSung.adapter = mAdapter
        }

        mAdapter.setOnItemClickListener { _, _, position ->
            Log.d(TAG, "setOnItemClickListener ----> $position")
            KaraokePlayerManager.playSong(
                requireActivity(),
                mAdapter.getItem(position),
                BigDataConstants.CARD_NAME_ALREADY_DEMAND
            )
            // 关闭对话框
            parentFragment?.let {
                if (it is SongListDialogFragment && (!PhoneStatusManager.isInCall(false))) {
                    it.dismiss()
                }
            }
        }
    }

    override fun initObserve() {
        super.initObserve()
        mViewModel?.let { viewModel ->
            viewModel.sungList.observe(viewLifecycleOwner) {
                mAdapter.setList(it)
                mViewModel?.updateUI()
            }
        }
    }

    override fun initRequestData() {
        mViewModel?.getSungList()
    }

    override fun setStateLayoutParameters(stateLayout: StateLayout) {
        stateLayout.setShowEmptyBtn(false)
            .setEmptyDataHintTxt(resources.getString(R.string.demand_list_empty))
            .setEmptyIvTopMargin(resources.getDimension(R.dimen.dp_121))
    }

    override fun getLoadViewRoot(): View? {
        return mBinding?.root
    }

    override fun onDestroyView() {
        // 清理RecyclerView和Adapter，防止内存泄露
        mBinding?.rvSung?.adapter = null
        super.onDestroyView()
    }

    companion object {
        private const val TAG = "AlreadySungFragment"
    }
}