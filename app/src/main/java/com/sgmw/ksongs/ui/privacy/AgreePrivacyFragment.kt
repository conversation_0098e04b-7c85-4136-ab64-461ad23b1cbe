package com.sgmw.ksongs.ui.privacy

import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.core.content.ContextCompat
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.mvvm.v.BaseFrameFragment
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.R
import com.sgmw.ksongs.databinding.FragmentAgreePrivacyBinding
import com.sgmw.ksongs.ui.protocol.ProtocolHomeFragment
import com.sgmw.ksongs.utils.NavigationUtils
import kotlin.system.exitProcess

/**
 * @author: 董俊帅
 * @time: 2025/1/15
 * @desc: 用户协议及隐私保护
 */

class AgreePrivacyFragment : BaseFrameFragment<FragmentAgreePrivacyBinding, AgreePrivacyViewModel>() {

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                // 只有当前fragment是顶层可见fragment时才拦截返回键
                if (isVisible && findNavController().currentDestination?.id == R.id.navigation_agree_privacy) {
                    requireActivity().finish()
                } else {
                    // 让系统处理返回键，不拦截
                    isEnabled = false
                    requireActivity().onBackPressed()
                    isEnabled = true
                }
            }
        })
    }

    override fun initRequestData() {}

    override fun FragmentAgreePrivacyBinding.initView() {
        initSpannableString()
        mBinding?.apply {
            btDisagree.setOnSingleClickListener {
                mViewModel?.setAgreePrivacyState(false)
            }
            btAgree.setOnSingleClickListener {
                mViewModel?.setAgreePrivacyState(true)
            }
        }
    }

    private fun initSpannableString() {
        val text = getString(R.string.privacy_policy_content2)

        // 创建 SpannableString
        val spannableString = SpannableString(text)

        // 设置第一个链接的样式和点击事件
        val link1Start = text.indexOf("《")
        val link1End = text.indexOf("》") + 1
        spannableString.setSpan(
            ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.font_privacy_color)),
            link1Start,
            link1End,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        spannableString.setSpan(
            object : ClickableSpan() {
                override fun onClick(widget: View) {
                    NavigationUtils.navigateSafely(
                        findNavController(),
                        R.id.action_privacy_to_protocol,
                        ProtocolHomeFragment.createBundle(ProtocolHomeFragment.SERVICE_TERM)
                    )
                }

                override fun updateDrawState(ds: TextPaint) {
                    ds.isUnderlineText = false // 去掉下划线
                    ds.bgColor = Color.TRANSPARENT // 去掉背景色
                }
            },
            link1Start,
            link1End,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        // 设置第二个链接的样式和点击事件
        val link2Start = text.lastIndexOf("《")
        val link2End = text.lastIndexOf("》") + 1
        spannableString.setSpan(
            ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.font_privacy_color)),
            link2Start,
            link2End,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        spannableString.setSpan(
            object : ClickableSpan() {
                override fun onClick(widget: View) {
                    NavigationUtils.navigateSafely(
                        findNavController(),
                        R.id.action_privacy_to_protocol,
                        ProtocolHomeFragment.createBundle(ProtocolHomeFragment.PRIVACY_POLICY)
                    )
                }

                override fun updateDrawState(ds: TextPaint) {
                    ds.isUnderlineText = false // 去掉下划线
                    ds.bgColor = Color.TRANSPARENT // 去掉背景色
                }
            },
            link2Start,
            link2End,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        // 设置 TextView 的文本和点击事件支持
        mBinding?.apply {
            tvContent2.text = spannableString
            tvContent2.movementMethod = LinkMovementMethod.getInstance()
            tvContent2.highlightColor = Color.TRANSPARENT

            tvContent2Temp.setOnClickListener {
                NavigationUtils.navigateSafely(
                    findNavController(),
                    R.id.action_privacy_to_protocol,
                    ProtocolHomeFragment.createBundle(ProtocolHomeFragment.PRIVACY_POLICY)
                )
            }
        }
    }

    override fun initObserve() {
        mViewModel?.isAgreePrivacy?.observe(viewLifecycleOwner) { agree ->
            Log.d(TAG, "isAgreePrivacy: $agree")
            if (agree) {
                val navOptions = NavOptions.Builder()
                    .setPopUpTo(R.id.navigation_agree_privacy, true)
                    .build()
                NavigationUtils.navigateSafely(
                    findNavController(),
                    R.id.action_to_login,
                    navOptions
                )
            } else {
                requireActivity().finishAffinity()
                exitProcess(0)
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        mViewModel?.isAgreePrivacy?.removeObservers(viewLifecycleOwner)
    }

    companion object {
        private const val TAG = "AgreePrivacyDialogFragment"
    }

}