package com.sgmw.ksongs.ui.category

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.sgmw.common.mvvm.vm.BaseViewModel
import com.sgmw.ksongs.model.bean.CategoryBean
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.repository.SongStationRepository
import com.sgmw.ksongs.model.bean.Result

class CategoryHomeViewModel : BaseViewModel() {

    private val stationRepository = SongStationRepository()

    private val _categoryResultLiveData = MutableLiveData<Result<CategoryBean?>>()
    val categoryResultLiveData: LiveData<Result<CategoryBean?>> = _categoryResultLiveData


    fun getCategoryList(operation: Operation) {
        stationRepository.getCategoryList(operation){
            _categoryResultLiveData.postValue(it)
        }
    }

}