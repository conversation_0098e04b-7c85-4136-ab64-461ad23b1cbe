package com.sgmw.ksongs.ui.record

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.sgmw.common.mvvm.vm.BaseViewModel
import com.sgmw.ksongs.db.entity.PlayRecordSongInfo
import com.sgmw.ksongs.model.repository.CollectRepository
import com.sgmw.ksongs.model.repository.PlayRecordRepository
import com.sgmw.ksongs.ui.playlist.PlayListManager
import com.sgmw.ksongs.utils.updateCollectStatus
import com.sgmw.ksongs.utils.updateDemandStatus
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

class PlayRecordViewModel: BaseViewModel() {

    private val playRecordRepository by lazy { PlayRecordRepository() }
    private val mutex = Mutex()

    // 添加到歌单数据
    val demandSongInfo = PlayListManager.getAllDemandSongInfoLiveData()
    // 收藏数据库监听变化
    val collectSongChangeLiveData: LiveData<Int> = CollectRepository().getCount()

    private val _playRecordSongsAfterUpdateStatus = MutableLiveData<List<PlayRecordSongInfo>>()
    val playRecordSongsAfterUpdateStatus: LiveData<List<PlayRecordSongInfo>> = _playRecordSongsAfterUpdateStatus

    /**
     * 编辑态LiveData
     */
    private val _editMode = MutableLiveData<Boolean>(false)
    val editMode: LiveData<Boolean> = _editMode

    init {
        // 初始化时就开始监听数据库变化
        viewModelScope.launch {
            playRecordRepository.findAll().collect { list ->
                // 当数据库更新时，立即更新状态
                updateAllStatus(list)
            }
        }
    }

    /**
     * 更新是否已收藏，是否已添加到播放列表
     */
    private suspend fun updateAllStatus(playRecordList: List<PlayRecordSongInfo>) {
        mutex.withLock {
            playRecordList.mapTo(mutableListOf()) { it.songInfo }.apply {
                updateDemandStatus()
                updateCollectStatus()
            }
            _playRecordSongsAfterUpdateStatus.postValue(playRecordList)
        }
    }

    /**
     * 更新是否已收藏字段
     */
    fun updateCollectStatus(playRecordList: List<PlayRecordSongInfo>) {
        viewModelScope.launch {
            mutex.withLock {
                val songList = playRecordList.map { it.songInfo }
                songList.updateCollectStatus()
                _playRecordSongsAfterUpdateStatus.postValue(playRecordList)
            }
        }
    }

    /**
     * 更新是否在播放列表字段
     */
    fun updateDemandStatus(playRecordList: List<PlayRecordSongInfo>) {
        viewModelScope.launch {
            mutex.withLock {
                playRecordList.mapTo(mutableListOf()) { it.songInfo }.updateDemandStatus()
                _playRecordSongsAfterUpdateStatus.postValue(playRecordList)
            }
        }
    }

    /**
     * 设置是否编辑态度
     */
    fun setEditMode(isEdit: Boolean) {
        _editMode.postValue(isEdit)
    }

    fun deleteSelectSongInfo(songInfoList: List<PlayRecordSongInfo>) {
        viewModelScope.launch {
            playRecordRepository.delete(songInfoList)
        }
    }

    fun getPlayRecordList() {
        viewModelScope.launch {
            // 强制刷新数据库查询
            playRecordRepository.findAll().collect { list ->
                // 当数据库更新时，立即更新状态
                updateAllStatus(list)
            }
        }
    }
}