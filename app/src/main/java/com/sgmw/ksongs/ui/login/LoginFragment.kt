package com.sgmw.ksongs.ui.login

import android.animation.ObjectAnimator
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.TextUtils
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.view.View
import android.view.animation.LinearInterpolator
import androidx.activity.OnBackPressedCallback
import androidx.core.content.ContextCompat
import androidx.navigation.fragment.findNavController
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.mvvm.v.BaseFrameFragment
import com.sgmw.common.utils.EventBusUtils
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.R
import com.sgmw.ksongs.constant.Constant
import com.sgmw.ksongs.databinding.FragmentLoginBinding
import com.sgmw.ksongs.model.bean.LightQrStatBean
import com.sgmw.ksongs.model.bean.LoginSuccessEvent
import com.sgmw.ksongs.ui.protocol.ProtocolHomeFragment
import com.sgmw.ksongs.utils.NavigationUtils
import com.sgmw.ksongs.viewmodel.MainViewModel
import retrofit2.Call
import kotlin.system.exitProcess

enum class LoginStateEnum {
    NONE,
    LOADING,
    LOAD_SUCCESS,
    LOAD_ERROR,
    LOGIN_SUCCESS,
    LOGIN_ERROR
}

/**
 * @author: 董俊帅
 * @time: 2025/1/15
 * @desc: 登录页面
 */

class LoginFragment : BaseFrameFragment<FragmentLoginBinding, LoginViewModel>() {

    private val mHandler = Handler(Looper.getMainLooper())
    private var loginState = LoginStateEnum.NONE
    // 标记轮询是否应该运行（用于页面可见性控制）
    private var shouldPolling = false

    private val mRunnable = object : Runnable {
        override fun run() {
            // 检查是否有请求正在进行中
            if (mViewModel?.isQrStatRequestInProgress() == true) {
                Log.d(TAG, "Previous QR stat request is still in progress, skipping this request")
            } else {
                // 发起新的请求
                mViewModel?.getLightQrStat()
            }
            // 再次调用自身，实现循环
            mHandler.postDelayed(this, 1000)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                // 拦截返回键
                requireActivity().finish()
            }
        })
    }

    override fun initRequestData() {}

    override fun FragmentLoginBinding.initView() {
        initSpannableString()
        showAgreeText()
        mBinding?.apply {
            btExit.setOnSingleClickListener {
                requireActivity().finishAffinity()
                exitProcess(0)
            }
            cbAgree.setOnCheckedChangeListener { _, isChecked ->
                if (isChecked) {
                    cbAgree.contentDescription = getString(R.string.content_description_disagree)
                    getQrcode()
                } else {
                    cbAgree.contentDescription = getString(R.string.content_description_agree)
                    stopPolling()
                    mViewModel?.cancelCurrentQrStatRequest()
                    showAgreeText()
                }
            }
            btAgree.setOnSingleClickListener {
                if (!cbAgree.isChecked) {
                    cbAgree.isChecked = true
                }
            }
            ivQrcodeTip.setOnSingleClickListener {
                if (loginState == LoginStateEnum.LOAD_ERROR) {
                    getQrcode()
                }
            }
            ivLoginStatus.setOnSingleClickListener {
                if (loginState == LoginStateEnum.LOGIN_ERROR) {
                    getQrcode()
                }
            }
        }
    }


    private fun getQrcode() {
        showQRLoading()
        mViewModel?.getLightQrCode()
    }

    private fun showAgreeText() {
        loginState = LoginStateEnum.NONE
        mBinding?.let { binding ->
            binding.clQrcode.visibility = View.GONE
            binding.layoutLoadStatus.visibility = View.VISIBLE
            binding.ivQrcodeTip.visibility = View.GONE
            binding.tvLoadStatus.visibility = View.VISIBLE
            binding.ivLoginStatus.visibility = View.GONE
        }
    }

    private fun hideQRLoading() {
        loginState = LoginStateEnum.LOAD_SUCCESS
        mBinding?.let {
            stopAnimation()
            it.clQrcode.visibility = View.VISIBLE
            it.layoutLoadStatus.visibility = View.GONE
        }
    }

    private fun showQRLoading() {
        loginState = LoginStateEnum.LOADING
        mBinding?.let { binding ->
            binding.clQrcode.visibility = View.GONE
            binding.layoutLoadStatus.visibility = View.VISIBLE
            binding.tvLoadStatus.visibility = View.GONE
            binding.ivLoginStatus.visibility = View.GONE
            binding.ivQrcodeTip.visibility = View.VISIBLE
            binding.ivQrcodeTip.contentDescription = getString(R.string.null_content_description)
            binding.ivQrcodeTip.setImageResource(R.mipmap.ksongs_qrcode_loading)
            startAnimation()
        }
    }

    private fun showLoginSuccess() {
        loginState = LoginStateEnum.LOGIN_SUCCESS
        mBinding?.let { binding ->
            stopAnimation()
            binding.clQrcode.visibility = View.GONE
            binding.layoutLoadStatus.visibility = View.VISIBLE
            binding.tvLoadStatus.visibility = View.GONE
            binding.ivQrcodeTip.visibility = View.GONE
            binding.ivLoginStatus.visibility = View.VISIBLE
            binding.ivLoginStatus.setImageResource(R.mipmap.ksongs_login_success)
        }
    }

    private fun showLoginField() {
        loginState = LoginStateEnum.LOGIN_ERROR
        mBinding?.let { binding ->
            binding.clQrcode.visibility = View.GONE
            binding.layoutLoadStatus.visibility = View.VISIBLE
            binding.tvLoadStatus.visibility = View.GONE
            binding.ivQrcodeTip.visibility = View.GONE
            binding.ivLoginStatus.visibility = View.VISIBLE
            stopAnimation()
            binding.ivLoginStatus.setImageResource(R.mipmap.ksongs_login_field)
        }
    }

    private fun showQRFailed() {
        loginState = LoginStateEnum.LOAD_ERROR
        mBinding?.let { binding ->
            binding.clQrcode.visibility = View.GONE
            binding.layoutLoadStatus.visibility = View.VISIBLE
            binding.tvLoadStatus.visibility = View.GONE
            binding.ivQrcodeTip.visibility = View.VISIBLE
            binding.ivLoginStatus.visibility = View.GONE
            stopAnimation()
            binding.ivQrcodeTip.contentDescription = getString(R.string.content_description_refresh)
            binding.ivQrcodeTip.setImageResource(R.mipmap.ksongs_qrcode_refresh)
        }

    }

    private fun resetQrCode() {
        mBinding?.let { binding ->
            binding.clQrcode.visibility = View.VISIBLE
            binding.tvScanTip.visibility = View.VISIBLE
            binding.cbAgree.visibility = View.VISIBLE
            binding.tvAgree.visibility = View.VISIBLE
            binding.btAgree.visibility = View.VISIBLE
            binding.btExit.visibility = View.VISIBLE

            binding.layoutLoadStatus.visibility = View.GONE

            binding.tvScanTip.text = getString(R.string.login_scan_tip)
            binding.btExit.text = getString(R.string.login_disagree)
        }
    }

    private var mRotateAnimation: ObjectAnimator? = null
    private fun startAnimation() {
        if (mRotateAnimation == null) {
            mRotateAnimation =
                ObjectAnimator.ofFloat<View>(mBinding?.ivQrcodeTip, View.ROTATION, 0f, 360f)
            mRotateAnimation?.duration = 1000
            mRotateAnimation?.repeatCount = ObjectAnimator.INFINITE
            mRotateAnimation?.interpolator = LinearInterpolator()
        }
        if (mRotateAnimation?.isStarted == false) {
            mRotateAnimation?.start()
        }
    }

    private fun stopAnimation() {
        mRotateAnimation?.cancel()
        // 重置旋转角度
        mBinding?.ivQrcodeTip?.rotation = 0f
    }

    private fun initSpannableString() {
        val text = requireContext().getString(R.string.login_privacy_tip)

        // 创建 SpannableString
        val spannableString = SpannableString(text)

        // 设置第一个链接的样式和点击事件
        val link1Start = text.indexOf("《")
        val link1End = text.indexOf("》") + 1
        spannableString.setSpan(
            ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.font_privacy_color)),
            link1Start,
            link1End,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        spannableString.setSpan(
            object : ClickableSpan() {
                override fun onClick(widget: View) {
                    NavigationUtils.navigateSafely(
                        findNavController(),
                        R.id.action_login_to_protocol,
                        ProtocolHomeFragment.createBundle(ProtocolHomeFragment.SERVICE_TERM)
                    )
                }

                override fun updateDrawState(ds: TextPaint) {
                    ds.isUnderlineText = false // 去掉下划线
                    ds.bgColor = Color.TRANSPARENT // 去掉背景色
                }
            },
            link1Start,
            link1End,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        // 设置第二个链接的样式和点击事件
        val link2Start = text.lastIndexOf("《")
        val link2End = text.lastIndexOf("》") + 1
        spannableString.setSpan(
            ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.font_privacy_color)),
            link2Start,
            link2End,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        spannableString.setSpan(
            object : ClickableSpan() {
                override fun onClick(widget: View) {
                    NavigationUtils.navigateSafely(
                        findNavController(), R.id.action_login_to_protocol,
                        ProtocolHomeFragment.createBundle(ProtocolHomeFragment.PRIVACY_POLICY)
                    )
                }

                override fun updateDrawState(ds: TextPaint) {
                    ds.isUnderlineText = false // 去掉下划线
                    ds.bgColor = Color.TRANSPARENT // 去掉背景色
                }
            },
            link2Start,
            link2End,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        // 设置 TextView 的文本和点击事件支持
        mBinding?.apply {
            tvAgree.text = spannableString
            tvAgree.movementMethod = LinkMovementMethod.getInstance()
            tvAgree.highlightColor = Color.TRANSPARENT
        }
    }

    override fun initObserve() {
        mViewModel?.apply {
            qrCodeUrl.observe(viewLifecycleOwner) {
                Log.d(TAG, "initObserve qrCodeUrl observe: $it")
                if (!TextUtils.isEmpty(it)) {
                    hideQRLoading()
                    mBinding?.ivQrcode?.setUrl(it)
                    startPolling()
                } else {
                    showQRFailed()
                }
            }
            mLightQrStatBean.observe(viewLifecycleOwner) {
                if (it?.error_code == 0) {
                    refreshView(it.stat)
                    when (it.stat) {
                        11 -> {
                            Log.d(TAG, "待扫码")
                        }

                        12 -> {
                            Log.d(TAG, "已扫码，待确认")
                        }

                        13 -> {
                            Log.d(TAG, "已扫码，已确认 (只有此时会返回授权码，且此状态只触发一次)")
                        }

                        14 -> {
                            Log.d(TAG, "扫码完成登录")
                            stopPolling()
                            mViewModel?.cancelCurrentQrStatRequest()
                            mViewModel?.getAccessToken()
                        }
                    }
                } else if (it?.error_code == 3005) {
                    // 二维码过期，停止轮询并取消当前请求
                    stopPolling()
                    mViewModel?.cancelCurrentQrStatRequest()
                    refreshView(it.error_code)
                } else {
                    Log.d(TAG, "获取二维码状态失败 error_code: ${it?.error_code} error_msg: ${it?.error_msg}")
                    if (it != null) {
                        it.error_code?.let { it1 -> refreshView(it1) }
                    }

                }

            }

            mAccessTokenBean.observe(viewLifecycleOwner) {
                if (!TextUtils.isEmpty(it.access_token)) {
                    Log.d(TAG, "登录成功")
                    EventBusUtils.postEvent(LoginSuccessEvent())
                    NavigationUtils.navigateSafely(findNavController(), R.id.action_login_to_main)
                }
            }
        }
    }

    private fun refreshView(code: Int) {
        when (code) {
            11 -> {
            }

            12 -> {
                mBinding?.apply {
                    showLoginSuccess()
                    tvScanTip.text = getString(R.string.login_scan_tip2)
                    btExit.text = getString(R.string.login_disagree2)
                    cbAgree.visibility = View.GONE
                    tvAgree.visibility = View.GONE
                    btAgree.visibility = View.GONE
                }
            }

            13 -> {
            }

            14 -> {
            }
            // 二维码过期
            3005 -> {
                showQRFailed()
            }

            else -> {
                showLoginField()
            }
        }
    }

    /**
     * 启动轮询
     */
    private fun startPolling() {
        if (!shouldPolling) {
            shouldPolling = true
            Log.d(TAG, "Starting QR code polling")
            mHandler.postDelayed(mRunnable, 1000)
        } else {
            Log.d(TAG, "Polling is already running")
        }
    }

    /**
     * 停止轮询
     */
    private fun stopPolling() {
        shouldPolling = false
        mHandler.removeCallbacks(mRunnable)
        Log.d(TAG, "Stopped QR code polling")
    }

    /**
     * 解决 F710SSW-1528，新增逻辑：
     * 手机扫码后，K歌退到后台，再打开K歌，需要初始化状态，重新获取二维码
     */
    override fun onStart() {
        super.onStart()
        Log.d(TAG, "onStart loginState: $loginState ")
        if (loginState == LoginStateEnum.LOGIN_SUCCESS) {
            loginState = LoginStateEnum.NONE
            mViewModel?.getLightQrCode()
            resetQrCode()
        }
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume - restarting polling if needed")
        // 页面可见时，如果二维码已经显示且登录状态正常，重新启动轮询
        if (shouldPolling && loginState != LoginStateEnum.LOGIN_SUCCESS) {
            Log.d(TAG, "Restarting polling after resume")
            mHandler.postDelayed(mRunnable, 1000)
        }
    }

    override fun onStop() {
        super.onStop()
        Log.d(TAG, "onStop loginState: $loginState ")
        // 页面不可见时停止轮询并取消当前请求，避免后台继续请求
        // 注意：不重置 shouldPolling 标志，以便在 onResume 时能够恢复轮询
        mHandler.removeCallbacks(mRunnable)
        mViewModel?.cancelCurrentQrStatRequest()
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy - cancelling all requests and removing callbacks")
        // 确保页面销毁时取消所有网络请求和Handler回调
        stopPolling()
        mViewModel?.cancelAllRequests()
        MainViewModel.resetTokenErrorStatus()
    }

    companion object {
        private const val TAG = "LoginDialogFragment"
    }

}