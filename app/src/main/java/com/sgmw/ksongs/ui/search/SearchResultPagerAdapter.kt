package com.sgmw.ksongs.ui.search

import android.view.View
import android.view.ViewGroup
import androidx.viewpager.widget.PagerAdapter
import java.util.Objects

class SearchResultPagerAdapter(views:List<View>, tab:List<String>) : PagerAdapter() {

    private var mViews: List<View>
    private var mTab:List<String>

    init {
        mViews = views
        mTab = tab
    }

    override fun getCount(): Int {
        return mViews.size
    }

    override fun isViewFromObject(v: View, any: Any): Boolean {
        return v == any
    }

    override fun instantiateItem(container: ViewGroup, position: Int): Any {
        val view = mViews.get(position)
        container.addView(view)
        return view
    }

    override fun destroyItem(container: ViewGroup, position: Int, any : Any) {
        if (any is View) {
            container.removeView(any)
        }
    }

    override fun getPageTitle(position: Int): CharSequence? {
        return mTab[position]
    }

}