package com.sgmw.ksongs.ui.user.feature

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.sgmw.common.mvvm.vm.BaseViewModel
import com.sgmw.ksongs.db.entity.CollectSongInfo
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.Result
import com.sgmw.ksongs.model.bean.SongInfoBean
import com.sgmw.ksongs.model.bean.UserLikeBean
import com.sgmw.ksongs.model.bean.onFailure
import com.sgmw.ksongs.model.bean.onSuccess
import com.sgmw.ksongs.model.repository.CollectRepository
import com.sgmw.ksongs.model.repository.SongStationRepository
import com.sgmw.ksongs.ui.playlist.PlayListManager
import com.sgmw.ksongs.utils.updateCollectStatus
import com.sgmw.ksongs.utils.updateDemandStatus
import kotlinx.coroutines.launch

class UserFeatureSongViewModel: BaseViewModel() {

    // 收藏数据库数据
    val collectSongChangeLiveData: LiveData<Int> = CollectRepository().getCount()
    // 添加到歌单数据
    val demandSongInfo = PlayListManager.getAllDemandSongInfoLiveData()

    private val songStationRepository by lazy { SongStationRepository() }
    private var userFeatureSongType: Int = USE_FEATURE_SONG_TYPE_LIKE

    private val _userFeatureSongResult = MutableLiveData<Result<UserLikeBean?>>()
    val userFeatureSongResult: LiveData<Result<UserLikeBean?>> = _userFeatureSongResult
    private var startIndex = USER_FEATURE_DEFAULT_INDEX
    private var isLoading: Boolean = false

    fun getSongList(operation: Operation, pageSize: Int = USER_FEATURE_SONG_PAGE_SIZE) {
        if (isLoading) return
        isLoading = true
        if (operation == Operation.NewData || operation == Operation.Refresh) {
            startIndex = USER_FEATURE_DEFAULT_INDEX
        }
        songStationRepository.getUserFeatureSongList(userFeatureSongType, operation, startIndex, pageSize) {
            it.onSuccess { value, operation ->
                startIndex++
                updateStatusThenSendData(it)
            }.onFailure { resultCode, operation ->
                _userFeatureSongResult.postValue(it)
            }
            isLoading = false
        }
    }

    private fun updateStatusThenSendData(rankListResult: Result<UserLikeBean?>) {
        viewModelScope.launch {
            rankListResult.value?.map_song_info?.songList?.updateCollectStatus()
            rankListResult.value?.map_song_info?.songList?.updateDemandStatus()
            _userFeatureSongResult.postValue(rankListResult)
        }
    }

    fun updateCollectStatus(songList: MutableList<SongInfoBean>) {
        viewModelScope.launch {
            songList.updateCollectStatus()
            postValueAfterUpdateStatus(songList)
        }
    }

    fun updateDemandStatus(songList: MutableList<SongInfoBean>) {
        viewModelScope.launch {
            songList.updateDemandStatus()
            postValueAfterUpdateStatus(songList)
        }
    }

    private fun postValueAfterUpdateStatus(songList: MutableList<SongInfoBean>) {
        val lastUserLikeBean = _userFeatureSongResult.value?.value
        val statusUpdateBean = UserLikeBean(
            UserLikeBean.MapHasMore(lastUserLikeBean?.map_has_more?.hasMore ?: true),
            UserLikeBean.MapSongInfo(songList)
        )
        _userFeatureSongResult.postValue(Result.Success(statusUpdateBean, Operation.UpdateStatus))
    }

}