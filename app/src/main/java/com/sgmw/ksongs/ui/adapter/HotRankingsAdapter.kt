package com.sgmw.ksongs.ui.adapter

import android.text.TextUtils
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.sgmw.common.ktx.loadRoundImage
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.R
import com.sgmw.ksongs.db.entity.DemandSongInfo
import com.sgmw.ksongs.model.bean.SongInfoBean
import com.sgmw.ksongs.ui.songplay.KaraokeConsole
import com.sgmw.ksongs.utils.toggleDemandSongInfo
import com.sgmw.ksongs.utils.visibleOrGone
import com.tme.ktv.video.api.VideoState

class HotRankingsAdapter(private val cardName: String) :BaseAdapter<SongInfoBean>(R.layout.home_rank_list_item) {
    // 从数据层面防抖
    private val processingItems = mutableSetOf<String>()
    override fun convert(holder: BaseViewHolder, item: SongInfoBean) {
        Log.d("HotRankingsAdapter", "convert: ${item.song_name}")
        val tvNum = holder.getView<TextView>(R.id.tvNum)
        val tvSongName = holder.getView<TextView>(R.id.tvSongName)
        val tvSongAuthor = holder.getView<TextView>(R.id.tvSongAuthor)
        val ivPlayStatus = holder.getView<ImageView>(R.id.ivPlayStatus)
        val ivAdd = holder.getView<ImageView>(R.id.ivAdd)
        val ivVip = holder.getView<ImageView>(R.id.ivVip)
        val ivMv = holder.getView<ImageView>(R.id.ivMv)
        val ivScore = holder.getView<ImageView>(R.id.ivScore)
        val ivCover = holder.getView<ImageView>(R.id.ivCover)

        // 重置所有状态
        holder.itemView.isSelected = false
        tvSongName.isSelected = false
        tvSongAuthor.isSelected = false
        ivPlayStatus.visibility = View.INVISIBLE
        ivPlayStatus.setImageDrawable(null)
        tvNum.visibility = View.VISIBLE
        ivAdd.isEnabled = true
        ivAdd.isSelected = false

        tvNum.text = (holder.layoutPosition + 1).toString()
        var songName = item.song_name
        if (TextUtils.isEmpty(item.song_name)){
            songName =  context.getString(R.string.home_data_loading)
        }
        tvSongName.text = songName

        // 确保TextView宽度约束正确应用，防止文字被省略
        tvSongName.post {
            tvSongName.requestLayout()
        }
        var singerName = item.singer_name
        if (TextUtils.isEmpty(singerName)){
            tvSongAuthor.visibility =View.GONE
        }else{
            tvSongAuthor.visibility =View.VISIBLE
            tvSongAuthor.text = item.singer_name
        }

        ivCover.loadRoundImage(
            item.album_img,
            roundCorner = ivCover.context.resources.getDimension(R.dimen.dp_12),
            R.mipmap.icon_music_default_bg
        )
        if (KaraokeConsole.currSongInfo?.song_id == item.song_id) {
            if (KaraokeConsole.playState.value == VideoState.STATE_PLAYING) {
                tvNum.visibility = View.GONE
                ivPlayStatus.visibility = View.VISIBLE
                ivPlayStatus.setImageDrawable(playAnimation)
                playAnimation?.start()
            } else {
                tvNum.visibility = View.VISIBLE
                ivPlayStatus.visibility = View.INVISIBLE
                playAnimation?.stop()
            }
            holder.itemView.isSelected = true
            tvSongName.isSelected = true
            tvSongAuthor.isSelected = true
            ivAdd.isEnabled = false
        }

        ivVip.visibleOrGone(item.need_vip)
        ivMv.visibleOrGone(item.has_mv)
        ivScore.visibleOrGone(item.has_midi)
        ivAdd.isSelected = item.isInDemandList

        //注册可见可说热词
        if (item.isInDemandList) {
            ivAdd.contentDescription = context.getString(R.string.add_cancel_content_description)
        } else {
            ivAdd.contentDescription = context.getString(R.string.add_content_description)
        }
        holder.itemView.contentDescription = "播放${tvSongName.text};唱${tvSongName.text}"

        ivAdd.setOnClickListener {
            if (!TextUtils.isEmpty(item.song_id)){
                // 防止重复点击
                if (processingItems.contains(item.song_id)) return@setOnClickListener
                processingItems.add(item.song_id)
                toggleDemandSongInfo(
                    DemandSongInfo(songInfo = item),
                    cardName = cardName
                ) { isInDemandList ->
                    // 更新UI状态 - 移除异步post，改为同步更新避免布局时序问题
                    item.isInDemandList = isInDemandList
                    ivAdd.isSelected = isInDemandList

                    // 强制请求布局重新计算，确保tvSongName宽度正确
                    tvSongName.requestLayout()

                    processingItems.remove(item.song_id)
                }
            }
        }
    }

    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        super.onDetachedFromRecyclerView(recyclerView)
        // 清理防抖集合，防止内存泄露
        processingItems.clear()
    }
}