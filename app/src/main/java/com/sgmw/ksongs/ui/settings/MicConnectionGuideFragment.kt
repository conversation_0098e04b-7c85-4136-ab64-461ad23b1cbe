package com.sgmw.ksongs.ui.settings

import androidx.navigation.fragment.findNavController
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.mvvm.v.BaseFrameFragment
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.databinding.FragmentMicConnectionGuideBinding
import com.sgmw.ksongs.viewmodel.settings.MicConnectionGuideViewModel

class MicConnectionGuideFragment :BaseFrameFragment<FragmentMicConnectionGuideBinding,MicConnectionGuideViewModel>() {

    private val TAG = MicConnectionGuideFragment::class.java.simpleName;

    override fun FragmentMicConnectionGuideBinding.initView() {
        Log.d(TAG,"initView")
        ivBack.setOnSingleClickListener {
            findNavController().popBackStack()
        }
    }

    override fun initObserve() {
        Log.d(TAG,"initObserve")
    }

    override fun initRequestData() {
        Log.d(TAG,"initRequestData")
    }
}