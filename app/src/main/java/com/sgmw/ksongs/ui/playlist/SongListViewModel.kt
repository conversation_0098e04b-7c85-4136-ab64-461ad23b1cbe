package com.sgmw.ksongs.ui.playlist

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.sgmw.common.mvvm.vm.BaseViewModel
import com.sgmw.ksongs.model.repository.AlreadyDemandRepository
import kotlinx.coroutines.launch

/**
 * @author: 董俊帅
 * @time: 2025/1/23
 * @desc:
 */
class SongListViewModel: BaseViewModel() {

    val mDemandList = PlayListManager.getAllDemandSongInfoWithPlayingLiveData()
    val mSungList = SungListManager.getSungListLiveData()

    val demandSongInfoSize = MutableLiveData<Int>()

    private val mRepository by lazy { AlreadyDemandRepository() }

    fun getDemandListSize() {
        viewModelScope.launch {
            val demandSize = mRepository.getDemandSongInfoList().size
            demandSongInfoSize.postValue(demandSize)
        }
    }


}