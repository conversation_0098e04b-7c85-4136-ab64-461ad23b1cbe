package com.sgmw.ksongs.ui.privacy

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.sgmw.common.mvvm.vm.BaseViewModel
import com.sgmw.common.utils.Log
import com.sgmw.common.utils.MMKVUtils
import com.sgmw.ksongs.constant.MMKVConstant

/**
 * @author: 董俊帅
 * @time: 2025/1/18
 * @desc:
 */

class AgreePrivacyViewModel : BaseViewModel() {

    val isAgreePrivacy = MutableLiveData<Boolean>()

    fun setAgreePrivacyState(agree: Boolean) {
        Log.d(TAG, "setAgreePrivacyState: $agree")
        MMKVUtils.put(MMKVConstant.IS_AGREE_PRIVACY, agree)
        isAgreePrivacy.postValue(agree)
    }

    companion object {
        private const val TAG = "AgreePrivacyViewModel"
    }


}