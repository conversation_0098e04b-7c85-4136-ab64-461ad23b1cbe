package com.sgmw.ksongs.ui.home


import android.util.Log
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import com.blankj.utilcode.util.TimeUtils
import com.bumptech.glide.Priority
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.sgmw.common.ktx.dp2px
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.mvvm.v.BaseFrameFragment
import com.sgmw.common.utils.EventBusUtils
import com.sgmw.common.utils.GlideRoundTransform
import com.sgmw.common.utils.GlideUtil
import com.sgmw.ksongs.R
import com.sgmw.ksongs.databinding.FragmentMineBinding
import com.sgmw.ksongs.model.eventbus.PlayControlEvent
import com.sgmw.ksongs.model.repository.AccessTokenManager
import com.sgmw.ksongs.track.BigDataConstants.CARD_NAME_MINE
import com.sgmw.ksongs.track.SensorsDataManager
import com.sgmw.ksongs.ui.collect.CollectFragment
import com.sgmw.ksongs.ui.record.PlayRecordFragment
import com.sgmw.ksongs.utils.NavigationUtils
import com.sgmw.ksongs.viewmodel.home.MineViewModel
import java.text.SimpleDateFormat
import java.util.Locale


class MineFragment : BaseFrameFragment<FragmentMineBinding, MineViewModel>() {

    private val TAG = "MineFragment"
    override fun needSkinApply() = true
    override fun FragmentMineBinding.initView() {
        EventBusUtils.postEvent(PlayControlEvent(View.VISIBLE))
        mBinding?.let {
            it.btnExitAccount.setOnSingleClickListener {
                AccessTokenManager.logout()
                NavigationUtils.navigateSafely(findNavController(), R.id.action_to_login)
            }

            it.ivBuyVip.setOnSingleClickListener {
                SensorsDataManager.trackMineCardEvent(getString(R.string.vip_buy))
                NavigationUtils.navigateSafely(findNavController(), R.id.action_home_to_vip_payment)
            }

            it.clHistory.setOnSingleClickListener {
                SensorsDataManager.trackMineCardEvent(getString(R.string.history))
                NavigationUtils.navigateSafely(findNavController(), R.id.action_home_to_play_record,PlayRecordFragment.createBundle(CARD_NAME_MINE))
            }

            it.clCollect.setOnSingleClickListener {
                SensorsDataManager.trackMineCardEvent(getString(R.string.collect))
                NavigationUtils.navigateSafely(findNavController(), R.id.action_home_to_collect,CollectFragment.createBundle(CARD_NAME_MINE))
            }

            it.clSetting.setOnSingleClickListener {
                SensorsDataManager.trackMineCardEvent(getString(R.string.settings))
                NavigationUtils.navigateSafely(findNavController(), R.id.action_home_to_settings)
            }

        }
    }

    override fun initObserve() {
        mViewModel?.let { viewModel ->
            mBinding?.let { binding ->
                viewModel.mUserInfoBean.observe(viewLifecycleOwner) {
                    if (it == null) {//网络问题
                        binding.ivAvatar.setImageResource(R.mipmap.icon166_default_avatar)
                    } else {
                        val options: RequestOptions = RequestOptions()
                            .centerCrop()
                            .placeholder(R.mipmap.icon166_default_avatar) //预加载图片
                            .error(R.mipmap.icon166_default_avatar) //加载失败图片
                            .priority(Priority.HIGH) //优先级
                            .diskCacheStrategy(DiskCacheStrategy.NONE) //缓存
                            .transform(GlideRoundTransform(90)) //圆角
                        GlideUtil.loadImageWithOptions(
                            requireContext(),
                            it.user_avatar,
                            binding.ivAvatar,
                            options
                        )
                        binding.tvNick.text = it.user_nick
                        Log.d(TAG, "account_type: ${it.account_type}")
                    }

                }

                viewModel.mVipInfoBean.observe(viewLifecycleOwner) {
                    if (it == null) {//网络有问题

                    } else {
                        if (it.status == 0) {//非会员
                            binding.tvVipPeroid.visibility = View.GONE
                            binding.ivVip.setImageResource(R.mipmap.icon64_not_vip)
                            context?.let {context ->
                                marginTop(binding.btnExitAccount,dp2px(context.resources.getDimension(R.dimen.dp_47)))
                            }
                        } else {//会员
                            binding.tvVipPeroid.visibility = View.VISIBLE
                            binding.ivVip.setImageResource(R.mipmap.icon64_vip)
                            context?.let {context ->
                                marginTop(binding.btnExitAccount,dp2px(context.resources.getDimension(R.dimen.dp_22)))
                            }
                            val periodStr = StringBuilder()
                            periodStr.append("有效期: ")
                            val sdf = SimpleDateFormat("yyyy/MM/dd", Locale.getDefault())
                            periodStr.append(
                                TimeUtils.millis2String(
                                    it.start_time * 1000L,
                                    sdf
                                )
                            )
                            periodStr.append("~")
                            periodStr.append(
                                TimeUtils.millis2String(
                                    it.expire_time * 1000L,
                                    sdf
                                )
                            )
                            binding.tvVipPeroid.text = periodStr.toString()
                        }
                    }
                }
            }
        }
    }

   private fun marginTop(view: View, top: Int) {
        val layoutParams = view.layoutParams as ViewGroup.MarginLayoutParams
        layoutParams.topMargin = top
        view.layoutParams = layoutParams
    }

    override fun onResume() {
        super.onResume()
        mViewModel?.let {
            it.getVipInfo()
        }
    }
    override fun initRequestData() {
        mViewModel?.let {
            it.getUserInfo()
            it.getVipInfo()
        }
    }

}