package com.sgmw.ksongs.ui.home

import android.annotation.SuppressLint
import android.view.View
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.sgmw.common.mvvm.v.BaseFrameFragment
import com.sgmw.common.utils.Log
import com.sgmw.common.utils.RegisterEventBus
import com.sgmw.common.widget.StateLayout
import com.sgmw.ksongs.R
import com.sgmw.ksongs.databinding.FragmentSongStationBinding
import com.sgmw.ksongs.model.bean.BaseMultiQuickItem
import com.sgmw.ksongs.model.bean.CardItem
import com.sgmw.ksongs.model.bean.HotAgeItem
import com.sgmw.ksongs.model.bean.HotLikeItem
import com.sgmw.ksongs.model.bean.HotRankingsItem
import com.sgmw.ksongs.model.bean.HotSingerItem
import com.sgmw.ksongs.model.bean.HotTopicsItem
import com.sgmw.ksongs.model.bean.LoginSuccessEvent
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.SongInfoBean
import com.sgmw.ksongs.model.repository.SongStationRepository.Companion.NUMBER_SINGER
import com.sgmw.ksongs.model.repository.SongStationRepository.Companion.NUMBER_TOPICS
import com.sgmw.ksongs.model.repository.SongStationRepository.Companion.START_INDEX
import com.sgmw.ksongs.model.repository.SongStationRepository.Companion.TYPE_HOT_SINGER
import com.sgmw.ksongs.ui.adapter.SongStationAdapter
import com.sgmw.ksongs.ui.hottopic.HotTopicHomeFragment
import com.sgmw.ksongs.ui.songplay.KaraokeConsole
import com.sgmw.ksongs.ui.songplay.VipAndLimitManager
import com.sgmw.ksongs.ui.user.feature.USER_FEATURE_DEFAULT_INDEX
import com.sgmw.ksongs.ui.user.feature.USER_FEATURE_HOME_LIKE_PAGE_SIZE
import com.sgmw.ksongs.ui.user.feature.USE_FEATURE_SONG_TYPE_LIKE
import com.sgmw.ksongs.utils.DayRank
import com.sgmw.ksongs.utils.HotRank
import com.sgmw.ksongs.utils.NavigationUtils
import com.sgmw.ksongs.utils.RANK_DEFAULT_INDEX
import com.sgmw.ksongs.utils.RANK_HOME_PAGE_SIZE
import com.sgmw.ksongs.utils.updateDemandStatus
import com.sgmw.ksongs.viewmodel.MainViewModel
import com.sgmw.ksongs.viewmodel.home.SongStationViewModel
import com.sgmw.ksongs.widget.AccessibilityLinearLayoutManager
import com.sgmw.ksongs.widget.SpaceItemDecoration
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * 点歌台页面
 */
@RegisterEventBus
class SongStationFragment: BaseFrameFragment<FragmentSongStationBinding, SongStationViewModel>() {

    private var mSongStationAdapter :SongStationAdapter ?= null
    private var layoutManager: AccessibilityLinearLayoutManager? = null

    /**
     * 首页RecyclerView list数据
     */
    private var listData = mutableListOf<BaseMultiQuickItem>()

    /**
     * 热门榜数据
     */
    private var mHotRankingsData = mutableListOf<SongInfoBean>()

    /**
     * 周排行榜数据
     */
    private var mHotWeeklyRankingsData = mutableListOf<SongInfoBean>()

    /**
     * 猜你喜欢数据
     */
    private var mLikeData = mutableListOf<SongInfoBean>()

    override fun getLoadViewRoot(): View? {
        return mBinding?.rvSongStation
    }

    override fun needSkinApply() = true

    override fun setStateLayoutParameters(stateLayout: StateLayout) {
        stateLayout.setLoadingIvTopMargin(resources.getDimension(R.dimen.dp_254))
    }
    override fun FragmentSongStationBinding.initView() {
       Log.d(TAG,"initView")
        mBinding?.let { binding ->
            mSongStationAdapter = SongStationAdapter(this@SongStationFragment)
            layoutManager = AccessibilityLinearLayoutManager(requireContext())
            layoutManager?.orientation = LinearLayoutManager.VERTICAL
            binding.rvSongStation.layoutManager = layoutManager
            binding.rvSongStation.adapter = mSongStationAdapter
            binding.rvSongStation.addItemDecoration(SpaceItemDecoration(bottomSpace = 25))
            listData = initData()
            mSongStationAdapter?.setNewInstance(listData)
        }

    }
    //模拟数据
    private fun initData(): MutableList<BaseMultiQuickItem> {
        val cardItem = CardItem("vip")
        val hotSingerItem = HotSingerItem(getString(R.string.title_hot_singer))
        hotSingerItem.singerList = mutableListOf()
        val hotRankingsItem = HotRankingsItem(getString(R.string.title_rankings))
        hotRankingsItem.hotSongList = mHotRankingsData
        if (mHotRankingsData.isEmpty()){
            for(i in 0..2){
                mHotRankingsData.add(SongInfoBean())
            }
        }
        hotRankingsItem.hotWeeklyList = mHotWeeklyRankingsData
        if (mHotWeeklyRankingsData.isEmpty()){
            for(i in 0..2){
                mHotWeeklyRankingsData.add(SongInfoBean())
            }
        }
        val hotLikeItem = HotLikeItem(getString(R.string.title_user_like))
        hotLikeItem.songList = mLikeData
        if (mLikeData.isEmpty()){
            for(i in 0..5){
                mLikeData.add(SongInfoBean())
            }
        }
        val hotAgeItem = HotAgeItem(getString(R.string.title_hot_age))
        val hotTopicsItem = HotTopicsItem(getString(R.string.title_hot_topics))
        hotTopicsItem.songList = mutableListOf()
        return mutableListOf(
            BaseMultiQuickItem(BaseMultiQuickItem.STATION_CARD, cardItem),
            BaseMultiQuickItem(BaseMultiQuickItem.HOT_SINGER, hotSingerItem),
            BaseMultiQuickItem(BaseMultiQuickItem.HOT_RANKINGS, hotRankingsItem),
            BaseMultiQuickItem(BaseMultiQuickItem.HOT_LIKE, hotLikeItem),
            BaseMultiQuickItem(BaseMultiQuickItem.HOT_AGES, hotAgeItem),
            BaseMultiQuickItem(BaseMultiQuickItem.HOT_TOPICS, hotTopicsItem)
        )
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initObserve() {
        super.initObserve()
        mViewModel?.let { viewModel ->
            // 热门歌手
            viewModel.hotSingersData.observe(viewLifecycleOwner) {
                Log.d(TAG, "hotSingersData: ${it?.singers?.size}")
                if (it == null) {
                    return@observe
                }
                (listData[1].data as HotSingerItem).singerList = it.singers
                mSongStationAdapter?.notifyDataSetChanged()
            }

            viewModel.hotRankingsData.observe(viewLifecycleOwner) {
                Log.d(TAG, "hotRankingsData: ${it?.songs?.size}")
                if (it == null) {
                    return@observe
                }
                it.songs?.let {songs->
                    mHotRankingsData.clear()
                    mHotRankingsData.addAll(songs)
                }
                (listData[2].data as HotRankingsItem).hotSongList = mHotRankingsData
                mSongStationAdapter?.notifyDataSetChanged()
            }
            viewModel.hotWeeklyRankingsData.observe(viewLifecycleOwner) {
                Log.d(TAG, "hotWeeklyRankingsData: ${it?.songs?.size}")
                if (it == null) {
                    return@observe
                }
                it.songs?.let { songs ->
                    mHotWeeklyRankingsData.clear()
                    mHotWeeklyRankingsData.addAll(songs)
                }
                (listData[2].data as HotRankingsItem).hotWeeklyList = mHotWeeklyRankingsData
                mSongStationAdapter?.notifyDataSetChanged()
            }

            viewModel.userLikeData.observe(viewLifecycleOwner) {
                Log.d(TAG, "hotWeeklyRankingsData: ${it?.map_song_info?.songList?.size}")
                if (it == null) {
                    return@observe
                }
                it.map_song_info.songList?.let { songList ->
                   mLikeData.clear()
                   mLikeData.addAll(songList)
                }
                (listData[3].data as HotLikeItem).songList = mLikeData
                mSongStationAdapter?.notifyDataSetChanged()
            }
            viewModel.hotTopicsData.observe(viewLifecycleOwner) {
                if (it == null) {
                    return@observe
                }
                (listData[5].data as HotTopicsItem).songList = it.themes?.subList(START_INDEX, NUMBER_TOPICS)
                mSongStationAdapter?.notifyDataSetChanged()
            }
            viewModel.demandSongInfo.observe(this) {
                // 同步已点列表状态
                updateDemandStatus()
            }
            viewModel.homeDateWrapperLiveData.observe(this) {
                // 使用协程在后台线程更新状态
                viewLifecycleOwner.lifecycleScope.launch {
                    // 在后台线程同步状态
                    withContext(Dispatchers.IO) {
                        // 更新数据并同步状态
                        mHotRankingsData.clear()
                        mHotRankingsData.addAll(it.hotRankData)
                        mHotRankingsData.updateDemandStatus()
                        
                        mHotWeeklyRankingsData.clear()
                        mHotWeeklyRankingsData.addAll(it.hotWeekRankData)
                        mHotWeeklyRankingsData.updateDemandStatus()
                        
                        mLikeData.clear()
                        mLikeData.addAll(it.useLikeData)
                        mLikeData.updateDemandStatus()

                        // 在主线程更新UI
                        withContext(Dispatchers.Main) {
                            (listData[2].data as HotRankingsItem).hotSongList = mHotRankingsData
                            (listData[2].data as HotRankingsItem).hotWeeklyList = mHotWeeklyRankingsData
                            (listData[3].data as HotLikeItem).songList = mLikeData
                            mSongStationAdapter?.notifyDataSetChanged()
                        }
                    }
                }
            }
        }

        mSongStationAdapter?.onHotTopicItemClickListener = {
            val themeList = mViewModel?.hotTopicsData?.value?.themes?.mapTo(ArrayList()){ theme ->
                theme
            } ?: ArrayList()
            NavigationUtils.navigateSafely(findNavController(), R.id.action_home_to_hot_topic,
                HotTopicHomeFragment.createBundle(themeList, it))
        }
        // 播放状态动画更新
        KaraokeConsole.playState.observe(viewLifecycleOwner) {
            updateDemandStatus()
        }
    }

    /**
     * 更新已点列表状态
     */
    private fun updateDemandStatus() {
        // 使用协程在后台线程更新状态，然后在主线程更新UI
        viewLifecycleOwner.lifecycleScope.launch {
            // 在后台线程同步状态
            withContext(Dispatchers.IO) {
                // 同步热门榜数据状态
                mHotRankingsData.updateDemandStatus()
                // 同步周榜数据状态
                mHotWeeklyRankingsData.updateDemandStatus()
                // 同步猜你喜欢数据状态
                mLikeData.updateDemandStatus()

                // 在主线程更新UI
                withContext(Dispatchers.Main) {
                    mSongStationAdapter?.notifyDataSetChanged()
                }
            }
        }
    }

    override fun initRequestData() {
        mViewModel?.getHotSingers(Operation.NewData, NUMBER_SINGER,TYPE_HOT_SINGER)
        mViewModel?.getMultiPlaylist(HotRank, Operation.NewData, RANK_DEFAULT_INDEX, RANK_HOME_PAGE_SIZE)
        mViewModel?.getMultiPlaylist(DayRank, Operation.NewData, RANK_DEFAULT_INDEX, RANK_HOME_PAGE_SIZE)
        mViewModel?.getUserFeatureSongList(USE_FEATURE_SONG_TYPE_LIKE, Operation.NewData, USER_FEATURE_DEFAULT_INDEX, USER_FEATURE_HOME_LIKE_PAGE_SIZE)
        mViewModel?.getHotTopics(Operation.NewData)

        ViewModelProvider(requireActivity())[MainViewModel::class.java].getCategoryList(Operation.NewData)
        // 获取vip状态
        VipAndLimitManager.getVipInfo {  }
    }

    /**
     * 登录后加载数据
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(event: LoginSuccessEvent) {
        initRequestData()
    }

    companion object {
        private const val TAG = "SongStationFragment"
    }

}