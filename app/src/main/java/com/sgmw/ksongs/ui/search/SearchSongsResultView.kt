package com.sgmw.ksongs.ui.search

import android.content.Context
import android.view.LayoutInflater
import android.widget.LinearLayout
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.ToastUtils
import com.chad.library.adapter.base.listener.OnItemChildClickListener
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.chad.library.adapter.base.listener.OnLoadMoreListener
import com.sgmw.ksongs.R
import com.sgmw.ksongs.databinding.LayoutSearchResultBinding
import com.sgmw.ksongs.model.bean.SongInfoBean
import com.sgmw.ksongs.track.BigDataConstants
import com.sgmw.ksongs.track.BigDataConstants.SPLIT
import com.sgmw.ksongs.ui.adapter.RankListAdapter
import com.sgmw.ksongs.ui.songplay.KaraokePlayerManager
import com.sgmw.ksongs.widget.AccessibilityLinearLayoutManager
import com.sgmw.ksongs.widget.SpaceItemDecoration
import java.lang.ref.WeakReference

class SearchSongsResultView @JvmOverloads constructor(context: Context,fragment:Fragment) : LinearLayout(context) {


    private val mBinding: LayoutSearchResultBinding by lazy {
        LayoutSearchResultBinding.inflate(LayoutInflater.from(context),this,true)
    }
    private val songInfoAdapter: RankListAdapter by lazy {
        RankListAdapter()
    }


    private var mOnLoadMoreListener:OnLoadMoreListener? = null
    private var mHideKeyboardListener:HideKeyboardListener? = null

    // 使用软引用包装 Fragment
    private val mFragmentRef: WeakReference<Fragment>

    init {
        mFragmentRef = WeakReference(fragment)
    }

    init {
        initViews()
    }


    fun addData(data:MutableList<SongInfoBean>?) {
        if(data != null){
            songInfoAdapter.addData(data)
            mBinding.refreshLayoutResult.finishLoadMore()
        } else {
            mBinding.refreshLayoutResult.finishLoadMoreWithNoMoreData()
        }
    }

    fun setList(data:MutableList<SongInfoBean>?){
        mBinding.recyclerviewResult.scrollToPosition(0)
        if(data == null){
            songInfoAdapter.setList(mutableListOf())
            mBinding.stateLayout.showEmpty()
        } else {
            songInfoAdapter.setList(data)
            mBinding.stateLayout.showContent()
        }
    }

    fun notifyDataSetChanged(){
        songInfoAdapter.notifyDataSetChanged()
    }

    fun finishLoadMore(success:Boolean){
        mBinding.refreshLayoutResult.finishLoadMore(success)
    }

    fun getData():MutableList<SongInfoBean>{
        return songInfoAdapter.data
    }

    private fun initViews() {

        val layoutManager = AccessibilityLinearLayoutManager(
            context, LinearLayoutManager.VERTICAL, false
        )
        mBinding.recyclerviewResult.layoutManager = layoutManager
        mBinding.recyclerviewResult.adapter = songInfoAdapter
       val searchResultCardName = BigDataConstants.CARD_NAME_SEARCH_RESULT + SPLIT + context.getString(R.string.song)
        songInfoAdapter.setCardName(searchResultCardName)
        mBinding.recyclerviewResult.setOnTouchListener { view, motionEvent ->
            mHideKeyboardListener?.hideKeyboard()
            false
        }
        songInfoAdapter.setOnItemClickListener { adapter, v, position ->
            mHideKeyboardListener?.hideKeyboard()
            // 获取软引用中的 Fragment 实例
            mFragmentRef.get()?.let {
                KaraokePlayerManager.playSong(
                    it,
                    songInfoAdapter.getItem(position),
                    searchResultCardName
                )
            }
        }

        songInfoAdapter.setNewInstance(mutableListOf())

        mBinding.refreshLayoutResult.setOnLoadMoreListener {
            mOnLoadMoreListener?.onLoadMore()
        }

        mBinding.stateLayout.showContent()

    }

    fun setOnLoadMoreListener(onLoadMoreListener: OnLoadMoreListener?){
        mOnLoadMoreListener = onLoadMoreListener
    }

    fun setHideKeyboardListener(hideKeyboardListener:HideKeyboardListener?){
        mHideKeyboardListener = hideKeyboardListener
    }

    fun  releaseFragment(){
        // 清理RecyclerView的Adapter，防止内存泄露
        mBinding.recyclerviewResult.adapter = null
        mFragmentRef.clear()
        removeAllViews()
        parent?.let { removeView(this) }
    }
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        // 视图从窗口分离时释放资源
        releaseFragment()
    }
}