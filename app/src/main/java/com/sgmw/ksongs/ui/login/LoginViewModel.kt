package com.sgmw.ksongs.ui.login

import android.util.Log
import androidx.lifecycle.MutableLiveData
import com.sgmw.common.mvvm.vm.BaseViewModel
import com.sgmw.ksongs.model.bean.AccessTokenBean
import com.sgmw.ksongs.model.bean.LightQrCodeBean
import com.sgmw.ksongs.model.bean.LightQrStatBean
import com.sgmw.ksongs.model.repository.LoginRepository
import com.tme.ktv.login.api.LoginEvent
import com.tme.ktv.login.api.LoginToken
import com.tme.ktv.login.api.ThirdPartyLoginService
import com.tme.ktv.network.core.KtvLoginParams
import com.tme.ktv.network.core.LoginType
import retrofit2.Call

/**
 * @author: 董俊帅
 * @time: 2025/1/15
 * @desc: 登录ViewModel 流程如下：
 * 1. 获取LightQrCode 获取二维码所需的字段
 * 2. 获取LightQrStat 轮询判断是否扫描
 * 3. 获取AccessToken 保存AccessToken
 */

class LoginViewModel : BaseViewModel() {

    //扫码后获取AccessToken所需的code，轮询LightQrStat获取
    private var mCode = ""
    private var mLightQrCodeBean: LightQrCodeBean? = null

    // 保存当前正在进行的网络请求，用于取消操作
    private var currentQrStatCall: Call<LightQrStatBean>? = null
    // 标记是否有二维码状态请求正在进行中
    private var isQrStatRequestInProgress = false

    val qrCodeUrl = MutableLiveData<String>()
    val mLightQrStatBean = MutableLiveData<LightQrStatBean?>()
    val mAccessTokenBean = MutableLiveData<AccessTokenBean>()

    private val mLoginRepository by lazy {
        LoginRepository()
    }

    // 升级SDK Version到3.4后，新增的字段
    private val mLoginParams = KtvLoginParams().apply {
        loginType = LoginType.QRCODE_LOGIN
    }

    fun getLightQrCode() {
        mLoginRepository.getLightQrCode() { code, lightQrCode ->
            if (code == 0) {
                lightQrCode?.let {
                    val mQrCodeUrl =
                        "http://kg.qq.com/m.html?sig=${it.qr_sig}&code=${it.qr_code}&exp=1"
                    Log.d(TAG, "getLightQrCode success mQrCodeUrl: $mQrCodeUrl")
                    mLightQrCodeBean = it
                    qrCodeUrl.postValue(mQrCodeUrl)
                }
            } else {
                Log.e(TAG, "getLightQrCode error code: $code")
                qrCodeUrl.postValue("")
            }
        }
    }

    /**
     * 检查是否有二维码状态请求正在进行中
     */
    fun isQrStatRequestInProgress(): Boolean {
        return isQrStatRequestInProgress
    }

    fun getLightQrStat(): Call<LightQrStatBean>? {
        // 如果有请求正在进行中，则不发起新请求
        if (isQrStatRequestInProgress) {
            Log.d(TAG, "QR stat request is already in progress, skipping this request")
            return null
        }

        return mLightQrCodeBean?.let {
            // 标记请求开始
            isQrStatRequestInProgress = true
            Log.d(TAG, "Starting QR stat request")

            val call = mLoginRepository.getLightQrStat(it) { code, lightQrStat ->
                // 请求完成，重置标志位
                isQrStatRequestInProgress = false
                Log.d(TAG, "QR stat request completed with code: $code")

                if (code == 0) {
                    lightQrStat?.let { qrStat ->
                        mLightQrStatBean.postValue(qrStat)
                        if (qrStat.stat == 13) {
                            mCode = qrStat.data
                        }
                    }
                } else {
                    Log.e(TAG, "getLightQrStat error code: $code")
                    mLightQrStatBean.postValue(lightQrStat)
                }
            }
            // 保存当前请求，用于后续取消
            currentQrStatCall = call
            call
        }
    }

    fun getAccessToken() {
        mLoginRepository.getAccessToken(mCode) { code, accessTokenBean ->
            if (code == 0) {
                accessTokenBean?.let {
                    ThirdPartyLoginService.notifyLoginStateChanged(
                        LoginEvent.LoginSuccess(
                            mLoginParams,
                            LoginToken(
                                accessTokenBean.refresh_token,
                                accessTokenBean.openid,
                                System.currentTimeMillis() + 2 * 60 * 60 * 1000L,
                                accessTokenBean.access_token
                            )
                        )
                    )
                    mAccessTokenBean.postValue(it)
                }

            } else {
                Log.e(TAG, "getAccessToken error code: $code")
            }

        }
    }

    /**
     * 取消当前正在进行的二维码状态查询请求
     */
    fun cancelCurrentQrStatRequest() {
        currentQrStatCall?.let { call ->
            if (!call.isCanceled && !call.isExecuted) {
                call.cancel()
                Log.d(TAG, "Cancelled current QR stat request")
            }
        }
        currentQrStatCall = null
        // 重置请求状态标志位
        isQrStatRequestInProgress = false
    }

    /**
     * 取消所有正在进行的网络请求
     */
    fun cancelAllRequests() {
        cancelCurrentQrStatRequest()
        Log.d(TAG, "Cancelled all network requests")
    }

    override fun onCleared() {
        super.onCleared()
        // ViewModel 销毁时取消所有请求
        cancelAllRequests()
        Log.d(TAG, "LoginViewModel cleared, all requests cancelled")
    }

    companion object {
        private const val TAG = "LoginViewModel"
    }


}