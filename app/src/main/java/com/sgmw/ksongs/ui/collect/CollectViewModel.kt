package com.sgmw.ksongs.ui.collect

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.sgmw.common.mvvm.vm.BaseViewModel
import com.sgmw.ksongs.db.entity.CollectSongInfo
import com.sgmw.ksongs.model.repository.CollectRepository
import com.sgmw.ksongs.utils.updateDemandStatus
import com.sgmw.ksongs.ui.playlist.PlayListManager
import kotlinx.coroutines.launch

class CollectViewModel: BaseViewModel() {

    private val collectRepository by lazy { CollectRepository() }

    private val _collectSongsAfterUpdateStatus = MutableLiveData<List<CollectSongInfo>>()
    val collectSongsAfterUpdateStatus: LiveData<List<CollectSongInfo>> = _collectSongsAfterUpdateStatus
    // 收藏数据库数据
    val collectSongs: LiveData<List<CollectSongInfo>> = collectRepository.findAll()
    // 添加到歌单数据
    val demandSongInfo = PlayListManager.getAllDemandSongInfoLiveData()


    private val _editMode = MutableLiveData<Boolean>(false)
    val editMode: LiveData<Boolean> = _editMode

    fun updateDemandStatus(collectSongList: List<CollectSongInfo>) {
        viewModelScope.launch {
            collectSongList.map { it.songInfo }.updateDemandStatus()
            _collectSongsAfterUpdateStatus.postValue(collectSongList)
        }
    }


    /**
     * 设置是否编辑态度
     */
    fun setEditMode(isEdit: Boolean) {
        _editMode.postValue(isEdit)
    }

    fun deleteSelectSongInfo(songInfoList: List<CollectSongInfo>) {
        viewModelScope.launch {
            collectRepository.delete(songInfoList)
        }
    }

}