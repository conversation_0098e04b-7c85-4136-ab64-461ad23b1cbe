package com.sgmw.ksongs.ui.songplay

import com.sgmw.common.utils.ioLaunch
import com.sgmw.common.utils.Log
import com.sgmw.common.utils.mainLaunch
import com.sgmw.common.utils.MMKVUtils
import com.sgmw.ksongs.constant.MMKVConstant
import com.sgmw.ksongs.model.bean.LimitConfigBean
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.VipInfoBean
import com.sgmw.ksongs.model.bean.onFailure
import com.sgmw.ksongs.model.bean.onSuccess
import com.sgmw.ksongs.model.repository.MineRepository

/**
 * vip状态 以及每日限制次数管理类
 * 全局单例
 */
object VipAndLimitManager {

    private const val TAG = "VipAndLimitManager"

    private var isVip = false
    private var currentLimitBean: LimitConfigBean? = null
    private val mMineRepository by lazy {
        MineRepository()
    }

    /**
     * 获取vip信息
     */
    fun getVipInfo(callBack: (VipInfoBean?) -> Unit) {
        Log.d(TAG, "getVipInfo")

        // 使用IO线程处理网络请求，避免阻塞主线程
        ioLaunch {
            try {
                mMineRepository.getVipInfo(Operation.NewData) {
                    it.onSuccess { data, operation ->
                        data?.let { vipInfoBean ->
                            Log.d(TAG, "getVipInfo onSuccess --->vipInfoBean.status= ${vipInfoBean.status}")
                            isVip = vipInfoBean.status == 1
                            // 异步保存到MMKV，避免阻塞
                            ioLaunch {
                                MMKVUtils.putBoolean(MMKVConstant.IS_VIP, isVip)
                            }
                            // 在主线程回调
                            mainLaunch {
                                callBack.invoke(vipInfoBean)
                            }
                        }
                    }.onFailure { resultCode, operation ->
                        Log.e(TAG, "getVipInfo onFailure: $resultCode")
                        // 在主线程回调缓存
                        mainLaunch {
                            callBack.invoke(null)
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "getVipInfo error: ${e.message}", e)
                mainLaunch {
                    callBack.invoke(null)
                }
            }
        }
    }

    /**
     * 返回是否是vip
     */
    fun isVip(): Boolean {
        return MMKVUtils.getBoolean(MMKVConstant.IS_VIP, false)
    }

    /**
     * 获取限免播放次数
     */
    fun getLimitCount(callBack: (Int, Int) -> Unit) {
        // 使用IO线程处理网络请求，避免阻塞主线程
        ioLaunch {
            try {
                mMineRepository.getLimitConfig(Operation.NewData) {
                    it.onSuccess { data, operation ->
                        Log.d(TAG, "getLimitCount onSuccess --->data= $data")
                        currentLimitBean = data
                        data?.let { limitBean ->
                            // 在主线程回调
                            mainLaunch {
                                callBack(limitBean.total_free_nums, limitBean.played_times)
                            }
                        }
                    }.onFailure { resultCode, operation ->
                        Log.d(TAG, "getLimitCount onFailure --> $resultCode")
                        currentLimitBean?.let { limitBean ->
                            // 在主线程回调缓存数据
                            mainLaunch {
                                callBack(limitBean.total_free_nums, limitBean.played_times)
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "getLimitCount error: ${e.message}", e)
                // 发生异常时，尝试使用缓存数据
                currentLimitBean?.let { limitBean ->
                    mainLaunch {
                        callBack(limitBean.total_free_nums, limitBean.played_times)
                    }
                }
            }
        }
    }

}