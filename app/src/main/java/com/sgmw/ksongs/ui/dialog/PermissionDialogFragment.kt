package com.sgmw.ksongs.ui.dialog

import android.Manifest
import android.graphics.Typeface
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RadioButton
import com.blankj.utilcode.util.GsonUtils
import com.google.gson.reflect.TypeToken
import com.sgmw.common.BaseApplication
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.BuildConfig
import com.sgmw.ksongs.R
import com.sgmw.ksongs.databinding.DialogPermissionBinding
import com.sgmw.permissionsdk.PermissionChangeListener
import com.sgmw.permissionsdk.PermissionManager
import com.sgmw.permissionsdk.bean.PermissionApp

/**
 * @author: 董俊帅
 * @time: 2025/6/24
 * @desc: 权限申请弹框
 */

class PermissionDialogFragment(val listener: (Boolean) -> Unit) : BaseBlurDialogFragment(R.layout.dialog_permission) {

    /**
     * 权限组名
     * -1-关闭
     * 1-本次允许
     * 2-三个月
     * 3-一年
     */
    private var flag = -1

    private val mBinding: DialogPermissionBinding by lazy {
        DialogPermissionBinding.inflate(layoutInflater)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        dialog?.setCanceledOnTouchOutside(true)
        return mBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mBinding.apply {
            mBinding.dialogContent.setOnSingleClickListener {}
            root.setOnSingleClickListener {
                listener.invoke(false)
                dismiss()
            }
            ivBack.setOnSingleClickListener {
                listener.invoke(false)
                dismiss()
            }
            tvCancel.setOnSingleClickListener {
                listener.invoke(false)
                dismiss()
            }
            tvConfirm.setOnSingleClickListener {
                if (flag == -1) {
                    listener.invoke(false)
                    dismiss()
                    return@setOnSingleClickListener
                }
                PermissionManager.getInstance().grantAppPermissionGroup(
                    BaseApplication.application.packageName,
                    Manifest.permission_group.MICROPHONE,
                    flag
                )
                dismiss()
            }

            rgPermission.setOnCheckedChangeListener { _, checkedId ->
                when (checkedId) {
                    R.id.rb_close -> {
                        flag = -1
                        setRadioButtonFont(rbClose)
                    }

                    R.id.rb_once -> {
                        flag = 1
                        setRadioButtonFont(rbOnce)
                    }

                    R.id.rb_three_month -> {
                        flag = 2
                        setRadioButtonFont(rbThreeMonth)
                    }

                    R.id.rb_one_year -> {
                        flag = 3
                        setRadioButtonFont(rbOneYear)
                    }
                }
            }
        }
        registerChangeListener()
    }

    /**
     * 根据RadioButton的选中状态设置不同的字体
     * 选中时使用sans-serif-medium，未选中时使用sans-serif-regular
     */
    private fun setRadioButtonFont(radioButton: RadioButton) {
        val sansSerifMedium = Typeface.create("sans-serif-medium", Typeface.NORMAL)
        val sansSerifRegular = Typeface.create("sans-serif-regular", Typeface.NORMAL)
        mBinding.apply {
            rbClose.setTypeface(sansSerifRegular, Typeface.NORMAL)
            rbOnce.setTypeface(sansSerifRegular, Typeface.NORMAL)
            rbThreeMonth.setTypeface(sansSerifRegular, Typeface.NORMAL)
            rbOneYear.setTypeface(sansSerifRegular, Typeface.NORMAL)
        }
        radioButton.setTypeface(sansSerifMedium, Typeface.NORMAL)
    }

    private val permissionChangeListener = object : PermissionChangeListener {
        override fun permissionChanges(data: String?) {
            try {
                val permissionApps: List<PermissionApp> =
                    GsonUtils.fromJson(data, object : TypeToken<List<PermissionApp?>?>() {
                    }.type)
                for (app in permissionApps) {
                    if (TextUtils.equals(app.packageName, BaseApplication.application.packageName)) {
                        for (groups in app.permissionGroups) {
                            if (TextUtils.equals(groups.groupName, Manifest.permission_group.MICROPHONE)) {
                                val isGrant = groups.isGrant
                                Log.d(TAG, "permissionChanges isGrant: $isGrant")
                                listener.invoke(isGrant)
                                return
                            }
                        }
                    }
                    listener.invoke(false)
                }
            } catch (e: Exception) {
                Log.e(TAG, "permissionChanges " + e.message)
                listener.invoke(false)
            }
        }

    }

    private fun registerChangeListener() {
        if (BuildConfig.IS_PHONE) {
            return
        } else {
            PermissionManager.getInstance().registerChangeListener(permissionChangeListener)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        PermissionManager.getInstance().unregisterChangeListener(permissionChangeListener)
    }

    companion object {
        private const val TAG = "PermissionDialogFragment"
    }

}