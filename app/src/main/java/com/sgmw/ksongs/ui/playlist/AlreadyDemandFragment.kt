package com.sgmw.ksongs.ui.playlist

import android.annotation.SuppressLint
import android.graphics.drawable.AnimationDrawable
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.sgmw.common.mvvm.v.BaseFrameFragment
import com.sgmw.common.utils.Log
import com.sgmw.common.widget.StateLayout
import com.sgmw.ksongs.R
import com.sgmw.ksongs.databinding.FragmentAlreadyDemandBinding
import com.sgmw.ksongs.db.entity.DemandSongInfo
import com.sgmw.ksongs.phonestatus.PhoneStatusManager
import com.sgmw.ksongs.track.BigDataConstants
import com.sgmw.ksongs.ui.songplay.KaraokeConsole
import com.sgmw.ksongs.ui.songplay.KaraokePlayerManager
import com.sgmw.ksongs.viewmodel.playlist.AlreadyDemandViewModel
import com.sgmw.ksongs.widget.AccessibilityLinearLayoutManager
import com.sgmw.ksongs.widget.FadeOutDeleteAnimator
import com.tme.ktv.video.api.VideoState

/**
 * @author: 董俊帅
 * @time: 2025/1/19
 * @desc: 已点歌曲列表
 */
class AlreadyDemandFragment :
    BaseFrameFragment<FragmentAlreadyDemandBinding, AlreadyDemandViewModel>() {

    private val mAdapter by lazy { AlreadyDemandAdapter() }
    private var isAnimating = false

    override fun onResume() {
        super.onResume()
        initRequestData()
    }
    override fun needSkinApply() = true
    override fun FragmentAlreadyDemandBinding.initView() {
        mBinding?.let { binding ->
            binding.rvDemand.layoutManager =
                AccessibilityLinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            binding.rvDemand.adapter = mAdapter
            binding.rvDemand.itemAnimator = FadeOutDeleteAnimator()
            mAdapter.addChildClickViewIds(
                R.id.ivDelete,
                R.id.ivUp
            )
            mAdapter.setOnItemChildClickListener { adapter, view, position ->
                when (view.id) {
                    R.id.ivDelete -> {
                        val songId = mAdapter.getItem(position).songInfo.song_id
                        // 标记开始动画
                        isAnimating = true
                        // 执行动画
                        mAdapter.removeAt(position)
                        // 动画开始后更新数据库
                        mViewModel?.removeDemandSongInfo(songId)
                        // 动画结束后重置标记
                        binding.rvDemand.postDelayed({
                            isAnimating = false
                        }, FadeOutDeleteAnimator.WAITE_DURATION) // 使用比动画总时长稍长的时间
                    }

                    R.id.ivUp -> {
                        mViewModel?.topDemandSongInfo(
                            DemandSongInfo(
                                songInfo = mAdapter.getItem(
                                    position
                                ).songInfo
                            )
                        )
                    }
                }
            }

            mAdapter.setOnItemClickListener { _, _, position ->
                Log.d(TAG, "current destination ----> $")
                KaraokePlayerManager.playSong(
                    requireActivity(),
                    mAdapter.getItem(position).songInfo,
                    BigDataConstants.CARD_NAME_ALREADY_DEMAND
                )
                // 关闭对话框
                parentFragment?.let {
                    if (it is SongListDialogFragment && (!PhoneStatusManager.isInCall(false))) {
                        it.dismiss()
                    }
                }
            }
        }
    }

    @SuppressLint("SetTextI18n")
    override fun initObserve() {
        super.initObserve()
        mViewModel?.let { viewModel ->
            viewModel.demandList.observe(viewLifecycleOwner) {
                if (!isAnimating) {
                    Log.d(TAG, "demandList size: ${it.size}")
                    mAdapter.setList(it)
                }
            }
        }

        PlayListManager.getPlayingSongInfoLiveData().observe(viewLifecycleOwner) {
            if (it == null) {
                mBinding?.apply {
                    ivSinging.visibility = View.GONE
                    tvSinging.visibility = View.GONE
                }
            } else {
                mBinding?.apply {
                    ivSinging.visibility = View.VISIBLE
                    tvSinging.visibility = View.VISIBLE
                    tvSinging.text = resources.getString(R.string.demand_playing) + it.songInfo.song_name
                }
            }
        }

        PlayListManager.getAllDemandSongInfoLiveData().observe(viewLifecycleOwner) {
            if (!isAnimating) {
                Log.d(TAG, "getAllDemandSongInfoLiveData size: ${it.size}")
                mAdapter.setList(it)
            }
        }

        KaraokeConsole.playState.observe(viewLifecycleOwner) {
            animationAction(it == VideoState.STATE_PLAYING)
            //解决播放状态改变后，列表数据不刷新的问题
            mViewModel?.getDemandListData()
        }
    }

    override fun initRequestData() {
        mViewModel?.getDemandListData()
    }

    override fun setStateLayoutParameters(stateLayout: StateLayout) {
        stateLayout.setShowEmptyBtn(false)
            .setEmptyDataHintTxt(resources.getString(R.string.demand_list_empty))
            .setEmptyIvTopMargin(resources.getDimension(R.dimen.dp_121))
    }

    override fun getLoadViewRoot(): View? {
        return mBinding?.root
    }

    fun shuffleDemandList() {
        mViewModel?.shuffleDemandList()
    }

    fun clearDemandList() {
        mViewModel?.clearDemandList()
    }

    private fun animationAction(start: Boolean) {
        mBinding?.ivSinging?.background?.let { background ->
            if (background is AnimationDrawable) {
                if (start) background.start() else background.stop()
            }
        }
    }

    override fun onDestroyView() {
        // 清理RecyclerView和Adapter，防止内存泄露
        mBinding?.rvDemand?.adapter = null
        super.onDestroyView()
    }

    companion object {
        private const val TAG = "AlreadyDemandFragment"
    }

}