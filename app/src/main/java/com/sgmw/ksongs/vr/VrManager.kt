package com.sgmw.ksongs.vr

import android.content.Intent
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.AppUtils
import com.sgmw.common.BaseApplication.Companion.context
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.BuildConfig
import com.sgmw.ksongs.utils.AppForegroundUtils
import com.sgmw.voice_engine.manager.sdk.SdkManager
import org.json.JSONObject


object VrManager {

    private const val TAG = "VrManager"
    private const val VR_ID = "sgmw.focus.setting.app.receiving"
    private const val VR_SEARCH_RESPONSE_ID = "sgmw.focus.music.ksong.receiving"
    private const val VR_KSONG_STATUS = "sgmw.action.music.ksong.status"
    private const val APP_NAME = "全民K歌"
    const val VR_KSONG_PLAY = "vr_ksong_play"

    //返回状态
    //失败
    const val RET_FAIELD = 0

    //成功
    const val RET_SUCCESS = 1

    //已是当前状态
    const val RET_IS_CURRENT = 2

    //没有登录
    const val RET_NO_LOGIN = 3

    //没有同意协议 麦克风权限没给
    const val RET_NO_AGREE = 4

    //没有网络
    const val RET_NO_NETWORK = 5

    //用户取消
    const val RET_CANCEL = 6

    //用户操作询问
    const val RET_ASK = 10

    //打开
    const val DEVICE_ON = "on"

    //关闭
    const val DEVICE_OFF = "off"

    //确认
    const val DEVICE_CONFIRM = "confirm"

    //取消
    const val DEVICE_CANCEL = "cancel"

    fun openCloseKSong(isOpen: Boolean) {
        var isForeground: Boolean
        if (AppForegroundUtils.isAppInForeground()) {
            Log.d(TAG, "k歌在前台")
            isForeground = true
        } else {
            Log.d(TAG, "k歌在后台")
            isForeground = false
        }

        if (isOpen) {
            if (isForeground) {
                Log.d(TAG, "K歌已是打开状态")
                sendDeviceOnOffAction(isOpen, RET_IS_CURRENT)
            } else {
                try {
                    AppUtils.launchApp(BuildConfig.APPLICATION_ID)
                    Log.d(TAG, "打开成功")
                    sendDeviceOnOffAction(isOpen, RET_SUCCESS)
                } catch (e: Exception) {
                    sendDeviceOnOffAction(isOpen, RET_FAIELD)
                    Log.d(TAG, "打开失败")
                }
            }
        } else {
            if (isForeground) {
                try {
                    ActivityUtils.finishAllActivities()
                    Log.d(TAG, "关闭成功")
                    sendDeviceOnOffAction(isOpen, RET_SUCCESS)
                } catch (e: Exception) {
                    Log.d(TAG, "关闭失败")
                    sendDeviceOnOffAction(isOpen, RET_FAIELD)
                }
            } else {
                Log.d(TAG, "K歌已是关闭状态")
                sendDeviceOnOffAction(isOpen, RET_IS_CURRENT)
            }
        }
    }

    private fun sendDeviceOnOffAction(isOpen: Boolean, ret: Int) {
        try {
            val obj = JSONObject()
            obj.put("id", VR_ID)
            obj.put("app_name", APP_NAME) //打开应用名称
            val deviceOnOff: String
            if (isOpen) {
                deviceOnOff = DEVICE_ON
            } else {
                deviceOnOff = DEVICE_OFF
            }
            obj.put("device_onoff", deviceOnOff) //表示打开
            obj.put("ret", ret) //执行结果
            Log.d(TAG, "sendDeviceOnOffAction ${obj.toString()}")
            SdkManager.getInstance().requestTtsWithId(VR_ID, obj.toString())
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 发送是否正在K歌
     * isPlaying true 正在K歌 false 不在K歌
     */

    fun sendKSongStatus(isPlaying: Boolean) {
        try {
            val obj = JSONObject()
            // sgmw.action.music.ksong.status
            obj.put("id", VR_KSONG_STATUS)
            var status = "stop"
            if (isPlaying) {
                status = "start"
            }
            obj.put("e_ksong_status", status)
            Log.d(TAG, "sendKSongStatus $obj")
            SdkManager.getInstance().requestTtsWithId(VR_KSONG_STATUS, obj.toString())
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun kSongPlay(jsonData: String) {
        Log.d(TAG, "播放歌曲 $jsonData")
        val intent = Intent("android.intent.action.MAIN")
        intent.addCategory("android.intent.category.LAUNCHER")
        intent.setClassName("com.sgmw.ksongs", "com.sgmw.ksongs.MainActivity")
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        intent.putExtra(VR_KSONG_PLAY, jsonData)
        context.startActivity(intent)
    }

    private fun getSearchList(data: String): ArrayList<String> {
        val jsonObject = JSONObject(data)
        val arrayList = ArrayList<String>()
        if (jsonObject.has("e_music_artist")) {
            arrayList.add(jsonObject["e_music_artist"].toString())
        }
        if (jsonObject.has("e_music_name")) {
            arrayList.add(jsonObject["e_music_name"].toString())
        }
        if (jsonObject.has("e_music_album")) {
            arrayList.add(jsonObject["e_music_album"].toString())
        }
        if (jsonObject.has("e_music_genre")) {
            arrayList.add(jsonObject["e_music_genre"].toString())
        }
        if (jsonObject.has("e_music_board")) {
            arrayList.add(jsonObject["e_music_board"].toString())
        }

        return arrayList
    }

    fun getSearchWord(data: String): String {
        val word = StringBuilder()
        getSearchList(data).map {
            word.append(it)
        }
        return word.toString()
    }

    fun getSearchMessage(singerName: String?, songName: String?): String {
        val stringBuilder = StringBuilder("确认播放")
        stringBuilder.append(singerName).append("的").append(songName).append("吗？")
        return stringBuilder.toString()
    }

    fun sendKSongPlayAction(artist: String = "", songName: String = "", ret: Int) {
        val jsonObject = JSONObject()
        jsonObject.put("id", VR_SEARCH_RESPONSE_ID)
        jsonObject.put("e_music_artist", artist)
        jsonObject.put("e_music_name", songName)
        jsonObject.put("ret", ret)
        SdkManager.getInstance().requestTtsWithId(VR_SEARCH_RESPONSE_ID, jsonObject.toString())
    }


}