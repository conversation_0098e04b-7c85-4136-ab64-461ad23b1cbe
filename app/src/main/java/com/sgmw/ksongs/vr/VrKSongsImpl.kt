package com.sgmw.ksongs.vr

import com.sgmw.common.utils.Log
import com.sgmw.voice_engine.manager.adapter.VrKSongManager

/**
 * k歌语音识别实现类
 */
class VrKSongsImpl : VrKSongManager.IKSongListener {

    companion object{
        const val TAG = "VrKSongsImpl"
    }

    /**
     * 打开/关闭 K歌
     * @param isOpen
     */
    override fun openCloseKSong(isOpen: Boolean) {
        Log.d(TAG, "openCloseKSong $isOpen")
        VrManager.openCloseKSong(isOpen)
    }

    /**
     * 我要K歌，以及二次确认弹窗交互
     * @param jsonData jsonObj.put("e_music_name",song); //歌曲名
     *                 jsonObj.put("e_music_artist",artist); //歌手名
     *                 jsonObj.put("e_music_album",album); // 专辑名
     *                 jsonObj.put("e_music_genre",type); // 类型
     *                 jsonObj.put("e_music_board",board); // 榜单名
     *                 jsonObj.put("e_device_onoff",on/confirm/cancel);
     *                 //on: 打开；confirm：用户确认；cancel：用户取消
     */
    override fun KSongPlay(jsonData: String?) {
        Log.d(TAG,"KSongPlay $jsonData")
        if (jsonData != null) {
            VrManager.kSongPlay(jsonData)
        }
    }
}