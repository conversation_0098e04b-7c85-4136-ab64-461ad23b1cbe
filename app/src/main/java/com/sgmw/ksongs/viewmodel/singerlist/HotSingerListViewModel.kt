package com.sgmw.ksongs.viewmodel.singerlist

import androidx.lifecycle.MutableLiveData
import com.sgmw.common.mvvm.vm.BaseViewModel
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.Result
import com.sgmw.ksongs.model.bean.SearchResultBean
import com.sgmw.ksongs.model.bean.onFailure
import com.sgmw.ksongs.model.bean.onSuccess
import com.sgmw.ksongs.model.repository.SearchRepository
import com.sgmw.ksongs.model.repository.SearchRepository.Action
import com.sgmw.ksongs.model.repository.SearchRepository.ContentFlag

class HotSingerListViewModel : BaseViewModel() {

    private val TAG = HotSingerListViewModel::class.java.simpleName

    private val mSearchRepository by lazy {
        SearchRepository()
    }

    //搜索开始页
    private var mSearchPage = 1

    val mSingerListBean = MutableLiveData<Result<SearchResultBean?>>()

    fun getSingerList(filterSingerArea: Int, filterSingerType: Int,operation:Operation) {

        if(operation == Operation.LoadMore){
            mSearchPage ++
        } else {
            mSearchPage = 1
        }

        mSearchRepository.getSearchResult(
            Action.LETTER.value,
            ContentFlag.CONTENT_FLAG_SINGER.value,
            filterSingerArea,
            filterSingerType,
            mSearchPage,
            SearchRepository.DEFAULT_PAGE_NUM,
            "",
            operation
        ) {
            it.onSuccess { value, operation ->
                mSingerListBean.postValue(it)
                Log.d(TAG, "post getSingerList $mSingerListBean")
            }.onFailure { resultCode, operation ->
                mSingerListBean.postValue(it)
            }
        }
    }


}