package com.sgmw.ksongs.viewmodel.playlist

import com.sgmw.common.mvvm.vm.BaseViewModel
import com.sgmw.common.utils.StateLayoutEnum
import com.sgmw.ksongs.ui.playlist.SungListManager

/**
 * @author: 董俊帅
 * @time: 2025/1/19
 * @desc:
 */
class AlreadySungViewModel : BaseViewModel() {

    val sungList = SungListManager.getSungListLiveData()

    fun getSungList() {
        sungList.postValue(sungList.value)
    }

    fun updateUI() {
        if (sungList.value == null) {
            stateViewLD.postValue(StateLayoutEnum.NO_DATA)
        } else {
            if (sungList.value?.isEmpty() == true) {
                stateViewLD.postValue(StateLayoutEnum.NO_DATA)
            } else {
                stateViewLD.postValue(StateLayoutEnum.SUCCESS)
            }
        }
    }

    fun getSungListSize(): Int {
        return sungList.value?.size ?: 0
    }
}