package com.sgmw.ksongs.viewmodel.settings

import android.util.Log
import androidx.lifecycle.MutableLiveData
import com.sgmw.common.mvvm.vm.BaseViewModel
import com.sgmw.common.utils.MMKVUtils
import com.sgmw.ksongs.constant.MMKVConstant
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.VipInfoBean
import com.sgmw.ksongs.model.bean.onFailure
import com.sgmw.ksongs.model.bean.onSuccess
import com.sgmw.ksongs.model.repository.MineRepository
import com.sgmw.ksongs.ui.songplay.VipAndLimitManager

class SettingsViewModel : BaseViewModel() {

    val mVipInfoBean by lazy {
        MutableLiveData<VipInfoBean?>()
    }

    private val mMineRepository by lazy {
        MineRepository()
    }

    fun getVipInfo() {
        VipAndLimitManager.getVipInfo{
            Log.d(TAG,"getVipInfo $it")
            mVipInfoBean.postValue(it)
        }
    }

    companion object {
        var cacheDirPath: String = ""
        private const val TAG = "SettingsViewModel"
    }
}