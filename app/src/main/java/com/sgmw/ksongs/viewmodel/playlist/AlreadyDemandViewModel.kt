package com.sgmw.ksongs.viewmodel.playlist

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.sgmw.common.mvvm.vm.BaseViewModel
import com.sgmw.common.utils.Log
import com.sgmw.common.utils.StateLayoutEnum
import com.sgmw.ksongs.db.entity.DemandSongInfo
import com.sgmw.ksongs.model.repository.AlreadyDemandRepository
import com.sgmw.ksongs.ui.songplay.KaraokeConsole
import com.tme.ktv.video.api.VideoState
import kotlinx.coroutines.launch

/**
 * @author: 董俊帅
 * @time: 2025/1/19
 * @desc: 已点歌曲列表
 */
class AlreadyDemandViewModel : BaseViewModel() {

    private val mRepository by lazy { AlreadyDemandRepository() }

    val demandList = MutableLiveData<MutableList<DemandSongInfo>>()

    fun getDemandListData() {
        viewModelScope.launch {
            getDemandList()
        }
    }

    private suspend fun getDemandList() {
        val demandSongInfoList = mRepository.getDemandSongInfoList()
        val playingSongInfo = mRepository.getPlayingSongInfo()
        Log.d(TAG, "getDemandList size: ${demandSongInfoList.size} playState:${KaraokeConsole.playState.value}")
        if (demandSongInfoList.isEmpty()
            && playingSongInfo == null
            && (KaraokeConsole.playState.value == VideoState.STATE_IDLE
                    || KaraokeConsole.playState.value == null
                    || KaraokeConsole.playState.value == VideoState.STATE_ENDED)
        ) {
            Log.d(TAG, "getDemandList 无数据")
            stateViewLD.postValue(StateLayoutEnum.NO_DATA)
        } else {
            Log.d(TAG, "getDemandList 有数据 size: ${demandSongInfoList.size}")
            stateViewLD.postValue(StateLayoutEnum.SUCCESS)
            demandList.postValue(demandSongInfoList)
        }
    }

    fun removeDemandSongInfo(songId: String) {
        viewModelScope.launch {
            mRepository.deleteDemandSongInfo(songId)
            getDemandList()
        }
    }

    fun clearDemandList() {
        viewModelScope.launch {
            mRepository.deleteAllDemandSongInfo()
            getDemandList()
        }
    }

    fun topDemandSongInfo(songInfo: DemandSongInfo) {
        viewModelScope.launch {
            mRepository.topDemandSongInfo(songInfo)
            getDemandList()
        }
    }

    fun shuffleDemandList() {
        viewModelScope.launch {
            mRepository.shuffleDemandList()
            getDemandList()
        }
    }

    companion object {
        private const val TAG = "AlreadyDemandViewModel"
    }


}