package com.sgmw.ksongs.viewmodel.singerlist

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.sgmw.common.mvvm.vm.BaseViewModel
import com.sgmw.ksongs.db.entity.CollectSongInfo
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.Result
import com.sgmw.ksongs.model.bean.SongInfoBean
import com.sgmw.ksongs.model.bean.SongsBySingerBean
import com.sgmw.ksongs.model.bean.onFailure
import com.sgmw.ksongs.model.bean.onSuccess
import com.sgmw.ksongs.model.repository.CollectRepository
import com.sgmw.ksongs.model.repository.SearchRepository
import com.sgmw.ksongs.ui.playlist.PlayListManager
import com.sgmw.ksongs.utils.updateCollectStatus
import com.sgmw.ksongs.utils.updateDemandStatus
import kotlinx.coroutines.launch

class SongListBySingerViewModel: BaseViewModel() {

    private val TAG = HotSingerListViewModel::class.java.simpleName

    // 收藏数据库数据
    val collectSongChangeLiveData: LiveData<Int>  = CollectRepository().getCount()
    // 添加到歌单数据
    val demandSongInfo = PlayListManager.getAllDemandSongInfoLiveData()

    private val mSearchRepository by lazy {
        SearchRepository()
    }

    val mSongsListBySingerBean = MutableLiveData<Result<SongsBySingerBean?>>()
    private var mIndex:Int = 0

    fun getSongsListBySingerId(singerId:String,operation: Operation) {

        val hotSearchBean = mSongsListBySingerBean.value
        if (hotSearchBean == null) {
            mIndex = 0
        } else {
            hotSearchBean.value?.next_index?.let {
                if (hotSearchBean != null && it >= 0 && hotSearchBean.value.has_more) {
                    mIndex = it
                }
            }
        }

        mSearchRepository.getSongsBySinger(
            mIndex,
            SearchRepository.DEFAULT_PAGE_NUM,
            "",
            singerId,
            1,
            operation
        ) {
            it.onSuccess { value, operation ->
                updateSongsStatus(it,mSongsListBySingerBean)
            }.onFailure { resultCode, operation ->
                mSongsListBySingerBean.postValue(it)
            }
        }
    }

    private fun updateSongsStatus(songsBySingerBean :  Result<SongsBySingerBean?>, liveData:MutableLiveData<Result<SongsBySingerBean?>>) {
        viewModelScope.launch {
            songsBySingerBean?.value?.songs?.updateCollectStatus()
            songsBySingerBean?.value?.songs?.updateDemandStatus()
            liveData.postValue(songsBySingerBean)
        }
    }

    fun updateCollectStatus(songList: MutableList<SongInfoBean>) {
        viewModelScope.launch {
            songList.updateCollectStatus()
            mSongsListBySingerBean.value?.value?.songs = songList
            mSongsListBySingerBean.value?.operation = Operation.UpdateStatus
            mSongsListBySingerBean.postValue(mSongsListBySingerBean.value)
        }
    }

    fun updateDemandStatus(songList: MutableList<SongInfoBean>) {
        viewModelScope.launch {
            songList.updateDemandStatus()
            mSongsListBySingerBean.value?.value?.songs = songList
            mSongsListBySingerBean.value?.operation = Operation.UpdateStatus
            mSongsListBySingerBean.postValue(mSongsListBySingerBean.value)
        }
    }

}