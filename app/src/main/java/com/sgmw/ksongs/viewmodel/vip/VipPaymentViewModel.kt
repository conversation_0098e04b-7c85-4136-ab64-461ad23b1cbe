package com.sgmw.ksongs.viewmodel.vip


import androidx.lifecycle.MutableLiveData
import com.sgmw.common.mvvm.vm.BaseViewModel
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.UserInfoBean
import com.sgmw.ksongs.model.bean.onFailure
import com.sgmw.ksongs.model.bean.onSuccess
import com.sgmw.ksongs.model.repository.MineRepository
import com.tme.ktv.demo.bean.VipProductBean

class VipPaymentViewModel : BaseViewModel() {

    private val TAG = "VipPaymentViewModel"

    private val mMineRepository by lazy {
        MineRepository()
    }

    val mUserInfoBean by lazy {
        MutableLiveData<UserInfoBean?>()
    }

    val mVipProductBean by lazy {
        MutableLiveData<VipProductBean?>()
    }

    fun getUserInfo() {
        mMineRepository.getUserInfo(640, 1,Operation.NewData) {
            it.onSuccess { value, operation ->
                mUserInfoBean.postValue(value)
            }.onFailure { resultCode, operation ->
                Log.d(TAG,"getUserInfo == $resultCode  == operation = $operation")
            }
        }
    }

    fun getVipProduct() {
        mMineRepository.getVipProduct(Operation.NewData) {
            it.onSuccess { value, operation ->
                mVipProductBean.postValue(value)
            }.onFailure { resultCode, operation ->
                Log.d(TAG,"getVipProduct == $resultCode  == operation = $operation")
                mVipProductBean.postValue(null)

            }
        }
    }


}