package com.sgmw.ksongs.viewmodel.search

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.blankj.utilcode.util.ToastUtils
import com.sgmw.common.mvvm.vm.BaseViewModel
import com.sgmw.common.utils.Log
import com.sgmw.common.utils.MMKVUtils
import com.sgmw.ksongs.R
import com.sgmw.ksongs.constant.MMKVConstant
import com.sgmw.ksongs.model.bean.RankingsBean
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.Result
import com.sgmw.ksongs.model.bean.SearchResultBean
import com.sgmw.ksongs.model.bean.SongInfoBean
import com.sgmw.ksongs.model.bean.onFailure
import com.sgmw.ksongs.model.bean.onSuccess
import com.sgmw.ksongs.model.repository.CollectRepository
import com.sgmw.ksongs.model.repository.SearchRepository
import com.sgmw.ksongs.model.repository.SearchRepository.Action
import com.sgmw.ksongs.model.repository.SearchRepository.ContentFlag
import com.sgmw.ksongs.model.repository.SearchRepository.FilterSingerArea
import com.sgmw.ksongs.model.repository.SearchRepository.FilterSingerType
import com.sgmw.ksongs.ui.playlist.PlayListManager
import com.sgmw.ksongs.utils.updateCollectStatus
import com.sgmw.ksongs.utils.updateDemandStatus
import kotlinx.coroutines.launch
import java.util.Random

class SearchHomeViewModel : BaseViewModel() {

    // 收藏数据库数据
    val collectSongChangeLiveData: LiveData<Int> = CollectRepository().getCount()

    // 添加到歌单数据
    val demandSongInfo = PlayListManager.getAllDemandSongInfoLiveData()

    companion object {
        const val DEFAULT_SUGGESTION_SINGER_NUM = 4
        private const val MAX_HISTORY_WORD_COUNT = 50
        private const val TAG = "SearchHomeViewModel"
    }

    // 搜索意图枚举
    enum class SearchIntent {
        SUGGESTION,    // 联想搜索
        FORMAL_SEARCH  // 正式搜索
    }

    private val mSearchRepository by lazy {
        SearchRepository()
    }


    var mSearchText: String? = null
    val mHintText = MutableLiveData<String?>()

    val mHotSearchBean = MutableLiveData<Result<RankingsBean?>>()

    private var mSuggestionSongPageIndex = 1
    val mSearchSuggestionResult = MutableLiveData<Result<SearchResultBean?>>()

    private var mSearchPageIndex = 1
    val mSearchResultBean = MutableLiveData<Result<SearchResultBean?>>()

    val mHistoryInfoList = MutableLiveData<MutableList<String>?>()

    // 搜索状态管理
    private var currentSearchIntent: SearchIntent = SearchIntent.SUGGESTION
    private var suggestionRequestId: Long = 0
    private var formalSearchRequestId: Long = 0
    private var isFormalSearching: Boolean = false


    //搜索热词
    fun getHotWords(operation: Operation) {
        mSearchRepository.getHotWords(operation) {
            it.onSuccess { value, operation ->
                val wordsList = value?.words
                val hint = wordsList?.getOrNull(Random().nextInt(wordsList.size))?.query
                mHintText.postValue(hint)
            }.onFailure { resultCode, operation ->
                Log.d(TAG, "error code $resultCode ")
            }
        }
    }

    //首次获取搜索历史
    fun getHistoryWords() {
        val historyStr = MMKVUtils.getString(MMKVConstant.SEARCH_HISTORY, null)
        Log.d(TAG, "getHistoryWords")
        if (historyStr.isNullOrEmpty()) {
            mHistoryInfoList.postValue(null)
        } else {
            val historyList = historyStr.split("|")
            Log.d(TAG, "historyList size: ${historyList.size}")
            mHistoryInfoList.postValue(historyList.toMutableList())
        }
    }

    /*
    * 保存搜索历史
    * */
    fun saveHistoryWords(newStr: String) {
        val newList = listOf(newStr)
        val oldList: MutableList<String>? = mHistoryInfoList.value
        val set = mutableSetOf<String>()
        val dataList = mutableListOf<String>()
        if (null != newList) {
            for (item in newList) {
                val success = set.add(item)
                if (success) {
                    dataList.add(item)
                    if (dataList.size >= MAX_HISTORY_WORD_COUNT) {
                        break
                    }
                }
            }
        }
        if (null != oldList) {
            for (item in oldList) {
                val success = set.add(item)
                if (success) {
                    dataList.add(item)
                    if (dataList.size >= MAX_HISTORY_WORD_COUNT) {
                        break
                    }
                }
            }
        }
        mHistoryInfoList.postValue(dataList)
        saveHistoryToMMKV(dataList)
    }

    //删除某个历史
    fun deleteHistoryWords(str: String) {
        Log.d(TAG, "deleteHistoryWords $str")
        var histList = mHistoryInfoList.value
        if (histList != null) {
            histList.remove(str)
            Log.d(TAG, "deleteHistoryWords $histList")
            mHistoryInfoList.postValue(histList)
            saveHistoryToMMKV(histList)
        }
    }

    //清除历史
    fun clearHistoryWords() {
        try {
            Log.d(TAG, "clearHistoryWords")
            mHistoryInfoList.postValue(mutableListOf())
            MMKVUtils.putString(MMKVConstant.SEARCH_HISTORY, null)
        } catch (e: Exception) {
            ToastUtils.showLong(R.string.search_clear_toast)
        }

    }

    //保存历史到MMKV
    private fun saveHistoryToMMKV(list: MutableList<String>) {
        Log.d(TAG, "saveHistoryToMMKV")
        var historyStr = StringBuilder()
        list.forEachIndexed { index, s ->
            if (index == 0) {
                historyStr.append(s)
            } else {
                historyStr.append("|")
                historyStr.append(s)
            }
        }
        Log.d(TAG, "save to MMKV history length= ${historyStr.length}")
        MMKVUtils.putString(MMKVConstant.SEARCH_HISTORY, historyStr.toString())
    }

    /*
    * 获取热门搜索列表，只获取一次
    * */
    fun getHotSearchList(operation: Operation): Boolean {

        val hotSearchBean = mHotSearchBean.value?.value
        var nextIndex = 0
        if (hotSearchBean == null) {
            nextIndex = 0
        } else {
            if (hotSearchBean.next_index > 0 && hotSearchBean.has_more == 1) {
                nextIndex = hotSearchBean.next_index
            } else {
                return false
            }
        }

        mSearchRepository.getHotSearch(
            nextIndex,
            SearchRepository.DEFAULT_PAGE_NUM,
            operation
        ) {
            it.onSuccess { value, operation ->
                updateHotSearchStatus(it, mHotSearchBean)
            }.onFailure { resultCode, operation ->
                Log.d(TAG, "onFailure $resultCode $operation")
                mHotSearchBean.postValue(it)
            }
        }
        return true
    }

    /*
    * 联想搜索
    * searText 搜索词
    * 返回 是否可加载更多
    * */
    fun getSuggestionResult(searchText: String, operation: Operation): Boolean {
        // 如果正在进行正式搜索，忽略联想搜索
        if (isFormalSearching) {
            Log.d(TAG, "正在进行正式搜索，忽略联想搜索请求")
            return false
        }

        currentSearchIntent = SearchIntent.SUGGESTION
        suggestionRequestId = System.currentTimeMillis()
        val currentRequestId = suggestionRequestId

        mSearchText = searchText

        if (operation == Operation.NewData || operation == Operation.Refresh) {
            mSuggestionSongPageIndex = 1
        } else {
            if (mSearchSuggestionResult.value?.value?.has_more == false) {
                return false
            } else {
                mSuggestionSongPageIndex++
            }
        }

        Log.d(TAG, "getSuggestionResult word = $searchText $mSuggestionSongPageIndex, requestId = $currentRequestId")
        mSearchRepository.getSearchResult(
            Action.LETTER.value,
            ContentFlag.CONTENT_FLAG_SINGER_SONG.value,
            FilterSingerArea.FILTER_SINGER_AREA_ALL.value,
            FilterSingerType.FILTER_SINGER_TYPE_ALL.value,
            mSuggestionSongPageIndex,
            SearchRepository.DEFAULT_PAGE_NUM,
            searchText,
            operation
        ) { newData ->
            // 检查请求是否过期
            if (currentRequestId != suggestionRequestId || isFormalSearching) {
                Log.d(TAG, "联想搜索请求已过期或被正式搜索覆盖，忽略结果")
                return@getSearchResult
            }

            newData.onSuccess { data, operation ->
                updateSearchStatus(newData, mSearchSuggestionResult)
                if (operation == Operation.NewData) {
                    data?.singers?.mapIndexed { index, singer ->
                        if (index < DEFAULT_SUGGESTION_SINGER_NUM) {
                            mSearchRepository.getSongsBySinger(
                                0,
                                1,
                                "",
                                singer.singer_id,
                                1,
                                operation
                            ) {
                                it.onSuccess { value, operation ->
                                    value?.let {
                                        singer.songs_num = it.total_num
                                        if (index == (DEFAULT_SUGGESTION_SINGER_NUM - 1)) {
                                            mSearchSuggestionResult.postValue(newData)
                                            Log.d(TAG, "songbysinger post mSearchSuggestionResult $data")
                                        }
                                    }
                                }.onFailure { resultCode, operation ->
                                    Log.d(TAG, "onFailure $resultCode $operation")
                                    mSearchSuggestionResult.postValue(newData)
                                }
                            }
                        }

                    }
                }
            }.onFailure { resultCode, operation ->
                Log.d(TAG, "onFailure $resultCode $operation")
                mSearchSuggestionResult.postValue(newData)
            }
        }
        return true
    }

    /*
   * 搜索歌手结果
   * searText 搜索词
   * 返回是否加载更多
   * */
    fun getSearchResult(searchText: String, operation: Operation): Boolean {
        currentSearchIntent = SearchIntent.FORMAL_SEARCH
        isFormalSearching = true
        formalSearchRequestId = System.currentTimeMillis()

        mSearchText = searchText

        if (operation == Operation.NewData || operation == Operation.Refresh) {
            mSearchPageIndex = 1
        } else {
            if (mSearchResultBean.value?.value?.has_more == true) {
                mSearchPageIndex++
            } else {
                return false
            }

        }
        Log.d(TAG, "getSearchResult - 开始正式搜索，requestId = $formalSearchRequestId")
        mSearchRepository.getSearchResult(
            Action.LETTER.value,
            ContentFlag.CONTENT_FLAG_SINGER_SONG.value,
            FilterSingerArea.FILTER_SINGER_AREA_ALL.value,
            FilterSingerType.FILTER_SINGER_TYPE_ALL.value,
            mSearchPageIndex,
            SearchRepository.DEFAULT_PAGE_NUM,
            searchText,
            operation
        ) {
            it.onSuccess { data, operation ->
                updateSearchStatus(it, mSearchResultBean)
                // 注意：不在这里重置状态，在Fragment的观察者中重置
            }.onFailure { resultCode, operation ->
                Log.d(TAG, "正式搜索失败 $resultCode $operation")
                isFormalSearching = false  // 失败时重置状态
                mSearchResultBean.postValue(it)
            }
        }

        return true

    }

    private fun updateSearchStatus(
        searchResultBean: Result<SearchResultBean?>,
        liveData: MutableLiveData<Result<SearchResultBean?>>
    ) {
        Log.d(
            TAG,
            "get suggestion songs size = ${searchResultBean.value?.songs?.size} total_num = ${searchResultBean.value?.total_num} hash_more ${searchResultBean.value?.has_more}"
        )
        viewModelScope.launch {
            searchResultBean.value?.songs?.updateCollectStatus()
            searchResultBean.value?.songs?.updateDemandStatus()
            liveData.postValue(searchResultBean)
            Log.d(
                TAG,
                "updateSearchStatus songs size = ${searchResultBean.value?.songs?.size} total_num = ${searchResultBean.value?.total_num} has_more = ${searchResultBean.value?.has_more}"
            )
        }
    }

    private fun updateHotSearchStatus(
        hotRankingsBean: Result<RankingsBean?>,
        liveData: MutableLiveData<Result<RankingsBean?>>
    ) {
        viewModelScope.launch {
            hotRankingsBean.value?.songs?.updateCollectStatus()
            hotRankingsBean.value?.songs?.updateDemandStatus()
            liveData.postValue(hotRankingsBean)
            Log.d(TAG, "updateHotSearchStatus ${hotRankingsBean.value}")
        }
    }

    fun updateCollectStatus(songList: MutableList<SongInfoBean>, showType: Int) {
        Log.d(TAG, "updateCollectStatus before = $songList")
        viewModelScope.launch {
            songList.updateCollectStatus()
            postValueAfterUpdateStatus(songList, showType)
        }
    }

    fun updateDemandStatus(songList: MutableList<SongInfoBean>, showType: Int) {
        Log.d(TAG, "updateDemandStatus before = $songList")
        viewModelScope.launch {
            songList.updateDemandStatus()
            postValueAfterUpdateStatus(songList, showType)
        }
    }

    private fun postValueAfterUpdateStatus(songList: MutableList<SongInfoBean>, showType: Int) {
        when (showType) {
            1 -> {
                mHotSearchBean.value?.value?.songs = songList
                mHotSearchBean.value?.operation = Operation.UpdateStatus
                mHotSearchBean.postValue(mHotSearchBean.value)
                Log.d(TAG, "postValueAfterUpdateStatus $mHotSearchBean.value")
            }

            2 -> {
                mSearchSuggestionResult.value?.value?.songs = songList
                mSearchSuggestionResult.value?.operation = Operation.UpdateStatus
                mSearchSuggestionResult.postValue(mSearchSuggestionResult.value)
                Log.d(TAG, "postValueAfterUpdateStatus $mSearchSuggestionResult.value")
            }

            3 -> {
                mSearchResultBean.value?.value?.songs = songList
                mSearchResultBean.value?.operation = Operation.UpdateStatus
                mSearchResultBean.postValue(mSearchResultBean.value)
                Log.d(TAG, "postValueAfterUpdateStatus $mSearchResultBean.value")
            }
        }
    }

    // 重置正式搜索状态
    fun resetFormalSearchState() {
        Log.d(TAG, "resetFormalSearchState: isFormalSearching=$isFormalSearching")
        isFormalSearching = false
    }

    // 获取当前搜索意图
    fun getCurrentSearchIntent(): SearchIntent = currentSearchIntent

    // 检查是否应该响应联想搜索结果
    fun shouldRespondToSuggestion(): Boolean {
        val shouldRespond = !isFormalSearching
        Log.d(TAG, "shouldRespondToSuggestion: $shouldRespond (isFormalSearching=$isFormalSearching)")
        return shouldRespond
    }

}