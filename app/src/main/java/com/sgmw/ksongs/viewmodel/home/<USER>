package com.sgmw.ksongs.viewmodel.home

import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.sgmw.common.mvvm.vm.BaseViewModel
import com.sgmw.common.utils.StateLayoutEnum
import com.sgmw.ksongs.model.bean.RankingsBean
import com.sgmw.ksongs.model.bean.HomeDataWrapper
import com.sgmw.ksongs.model.bean.HotTopicBean
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.SingerBean
import com.sgmw.ksongs.model.bean.UserLikeBean
import com.sgmw.ksongs.model.bean.onFailure
import com.sgmw.ksongs.model.bean.onSuccess
import com.sgmw.ksongs.model.repository.SongStationRepository
import com.sgmw.ksongs.ui.playlist.PlayListManager
import com.sgmw.ksongs.ui.user.feature.UserFeatureSongType
import com.sgmw.ksongs.utils.DayRank
import com.sgmw.ksongs.utils.HotRank
import com.sgmw.ksongs.utils.RANK_MAX_PAGE_SIZE
import com.sgmw.ksongs.utils.RankInfo
import com.sgmw.ksongs.utils.updateDemandStatus
import kotlinx.coroutines.launch

class SongStationViewModel : BaseViewModel() {
    val hotSingersData = MutableLiveData<SingerBean?>()
    val hotRankingsData = MutableLiveData<RankingsBean?>()
    val hotWeeklyRankingsData = MutableLiveData<RankingsBean?>()
    val userLikeData = MutableLiveData<UserLikeBean?>()
    val hotTopicsData = MutableLiveData<HotTopicBean?>()
    val demandSongInfo = PlayListManager.getAllDemandSongInfoLiveData()

    val homeDateWrapperLiveData = MutableLiveData<HomeDataWrapper>()

    companion object {
        const val TAG = "SongStationViewModel"
    }

    private val songStationRepository by lazy { SongStationRepository() }

    /**
     * 获取热门歌手
     */
    fun getHotSingers(operation: Operation, num: Int, type: Int) {
        stateViewLD.postValue(StateLayoutEnum.LOADING)
        songStationRepository.getHotSinger(operation, num, type) { result ->
            result.onSuccess { value, operation ->
                hotSingersData.postValue(value)
                stateViewLD.postValue(StateLayoutEnum.SUCCESS)
            }.onFailure { resultCode, operation ->
                stateViewLD.postValue(StateLayoutEnum.ERROR)
            }
        }
    }

    /**
     * 获取排行榜
     * @param startIndex 开始位置
     * @param num 数量
     * @param listId 排行榜id  1 热门榜  100 周榜
     */
    fun getMultiPlaylist(
        rankInfo: RankInfo,
        operation: Operation,
        startIndex: Int,
        pageSize: Int = RANK_MAX_PAGE_SIZE
    ) {
        songStationRepository.getMultiPlaylist(rankInfo, operation, startIndex, pageSize) { result ->
            Log.d(TAG, "getMultiPlaylist = ${result.value}")
            result.onSuccess { value, operation ->
                viewModelScope.launch {
                    value?.songs?.updateDemandStatus()
                    if (rankInfo == HotRank) {
                        hotRankingsData.postValue(value)
                    } else if (rankInfo == DayRank) {
                        hotWeeklyRankingsData.postValue(value)
                    }
                }
            }
        }
    }

    fun getUserFeatureSongList(
        @UserFeatureSongType sheetType: Int,
        operation: Operation,
        startIndex: Int,
        pageSize: Int
    ) {
        songStationRepository.getUserFeatureSongList(sheetType, operation, startIndex, pageSize) { result ->
            result.onSuccess { value, operation ->
                viewModelScope.launch {
                    value?.map_song_info?.songList?.updateDemandStatus()
                    userLikeData.postValue(value)
                }
            }
        }
    }

    /**
     * 获取热门专题
     */
    fun getHotTopics(operation: Operation) {
        songStationRepository.getHotTopics(operation) {
            it.onSuccess { value, operation ->
                hotTopicsData.postValue(value)
            }
        }
    }

    fun updateHomeDataStatus(homeDataWrapper: HomeDataWrapper) {
        viewModelScope.launch {
            homeDataWrapper.hotRankData.updateDemandStatus()
            homeDataWrapper.hotWeekRankData.updateDemandStatus()
            homeDataWrapper.useLikeData.updateDemandStatus()
            homeDateWrapperLiveData.postValue(homeDataWrapper)
        }
    }

}