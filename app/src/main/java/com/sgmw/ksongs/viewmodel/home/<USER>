package com.sgmw.ksongs.viewmodel.home

import android.util.Log
import androidx.lifecycle.MutableLiveData
import com.sgmw.common.mvvm.vm.BaseViewModel
import com.sgmw.common.utils.MMKVUtils
import com.sgmw.ksongs.constant.MMKVConstant
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.UserInfoBean
import com.sgmw.ksongs.model.bean.VipInfoBean
import com.sgmw.ksongs.model.bean.onFailure
import com.sgmw.ksongs.model.bean.onSuccess
import com.sgmw.ksongs.model.repository.MineRepository
import com.sgmw.ksongs.ui.songplay.VipAndLimitManager

class MineViewModel : BaseViewModel() {

    private val TAG = "MineViewModel"

    private val mMineRepository by lazy {
        MineRepository()
    }

    val mUserInfoBean by lazy {
        MutableLiveData<UserInfoBean?>()
    }

    val mVipInfoBean by lazy {
        MutableLiveData<VipInfoBean?>()
    }

    fun getUserInfo() {
        mMineRepository.getUserInfo(640, 1, Operation.NewData) {
            it.onSuccess { value, operation ->
                Log.d(TAG,"getUserInfo $value")
                mUserInfoBean.postValue(value)
            }.onFailure { resultCode, operation ->
                mUserInfoBean.postValue(null)
            }

        }
    }


    fun getVipInfo() {
        VipAndLimitManager.getVipInfo{
            Log.d(TAG,"getVipInfo $it")
            mVipInfoBean.postValue(it)
        }
    }


}