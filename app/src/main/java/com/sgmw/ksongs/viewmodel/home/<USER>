package com.sgmw.ksongs.viewmodel.home
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.sgmw.common.ktx.launchMain
import com.sgmw.common.mvvm.vm.BaseViewModel
import com.sgmw.common.utils.StateLayoutEnum
import kotlinx.coroutines.delay

class HomeViewModel : BaseViewModel() {

    private val _text = MutableLiveData<String>().apply {
        value = "This is home Fragment"
    }
    val text: LiveData<String> = _text



    var song_id = 987654321

}