package com.sgmw.ksongs.phonestatus

import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothHeadsetClient
import android.bluetooth.BluetoothHeadsetClientCall
import android.bluetooth.BluetoothProfile
import android.content.Context
import android.content.IntentFilter
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.sgmw.common.BaseApplication
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.R
import com.sgmw.ksongs.ui.songplay.KaraokePlayerManager
import com.sgmw.ksongs.utils.AppForegroundUtils
import com.sgmw.ksongs.utils.showToast

object PhoneStatusManager {

    private val tag = "PhoneStatusManager"

    private val HEADSET_CLIENT = 16
    private var phoneStatus = BluetoothHeadsetClientCall.CALL_STATE_TERMINATED

    // 来电时，是否正在k歌中
    private var isPlayingWhenInCall = false

    // 通话状态liveData
    private val isPhoneCall = MutableLiveData<Boolean>()
    val phoneCallState: LiveData<Boolean> = isPhoneCall

    // 保存BroadcastReceiver引用，用于取消注册
    // 不保存Context引用，直接使用BaseApplication.context避免内存泄露
    private var phoneStatusReceiver: PhoneStatusReceiver? = null

    /**
     * 是否在通话中
     */
    fun isInCall(needShowToast: Boolean = true): Boolean {
        Log.d(tag, "isInCall phoneStatus: $phoneStatus needShowToast: $needShowToast")
        return when (phoneStatus) {
            BluetoothHeadsetClientCall.CALL_STATE_ACTIVE,
            BluetoothHeadsetClientCall.CALL_STATE_HELD,
            BluetoothHeadsetClientCall.CALL_STATE_DIALING,
            BluetoothHeadsetClientCall.CALL_STATE_ALERTING,
            BluetoothHeadsetClientCall.CALL_STATE_INCOMING,
            BluetoothHeadsetClientCall.CALL_STATE_WAITING,
            BluetoothHeadsetClientCall.CALL_STATE_HELD_BY_RESPONSE_AND_HOLD -> {
                if (needShowToast && AppForegroundUtils.isAppInForeground()) {
                    // 应用在前台
                    showToast(R.string.in_call_hint)
                }
                true
            }

            BluetoothHeadsetClientCall.CALL_STATE_TERMINATED -> {
                false
            }

            else -> {
                false
            }
        }
    }


    internal fun updatePhoneStatus(phoneStatus: Int) {
        this.phoneStatus = phoneStatus
        isPhoneCall.postValue(isInCall(false))
    }

    fun initPhoneStatus(context: Context) {
        val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter() ?: return
        if (!bluetoothAdapter.isEnabled) {
            Log.d(tag, "蓝牙未开启")
            return
        }
        val serviceListener = object : BluetoothProfile.ServiceListener {
            override fun onServiceConnected(profile: Int, proxy: BluetoothProfile?) {
                Log.d(tag, "onServiceConnected: $profile")
                if (profile == HEADSET_CLIENT && proxy is BluetoothHeadsetClient) {
                    Log.d(tag, "connectDevice size: ${proxy.connectedDevices?.size}")
                    proxy.connectedDevices?.getOrNull(0)?.let {
                        Log.d(tag, "current calls size: ${proxy.getCurrentCalls(it)?.size}")
                        proxy.getCurrentCalls(it)?.forEach { call ->
                            val callState = call.state
                            Log.d(tag, "callState: $callState")
                            updatePhoneStatus(callState)
                            pauseOrResumePlayerIfNeed(callState)
                        }
                    }
                }
                bluetoothAdapter.closeProfileProxy(profile, proxy)
            }

            override fun onServiceDisconnected(profile: Int) {
                Log.d(tag, "disconnected: $profile")
            }

        }
        bluetoothAdapter.getProfileProxy(context.applicationContext, serviceListener, HEADSET_CLIENT)
    }


    fun registerPhoneStatusReceiver() {
        try {
            // 先取消之前的注册，避免重复注册
            unregisterPhoneStatusReceiver()

            val intentFilter = IntentFilter()
            intentFilter.addAction(BluetoothHeadsetClient.ACTION_CALL_CHANGED)
            intentFilter.addAction(BluetoothHeadsetClient.ACTION_CONNECTION_STATE_CHANGED)
            intentFilter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED)

            // 创建新的Receiver实例并保存引用
            phoneStatusReceiver = PhoneStatusReceiver()

            // 直接使用BaseApplication.context，避免保存Context引用导致内存泄露
            BaseApplication.context.registerReceiver(phoneStatusReceiver, intentFilter)
            Log.d(tag, "PhoneStatusReceiver registered successfully with BaseApplication.context")
        } catch (e: Exception) {
            Log.e(tag, "Failed to register PhoneStatusReceiver", e)
        }
    }

    /**
     * 取消注册BroadcastReceiver，防止内存泄露
     */
    fun unregisterPhoneStatusReceiver() {
        try {
            phoneStatusReceiver?.let { receiver ->
                // 直接使用BaseApplication.context，避免保存Context引用
                BaseApplication.context.unregisterReceiver(receiver)
                Log.d(tag, "PhoneStatusReceiver unregistered successfully")
            }
        } catch (e: Exception) {
            Log.e(tag, "Failed to unregister PhoneStatusReceiver", e)
        } finally {
            phoneStatusReceiver = null
        }
    }

    /**
     * 暂停或者恢复播放器播放
     * 暂停前如果是播放状态， 通话结束恢复播放状态
     */
    fun pauseOrResumePlayerIfNeed(status: Int) {
        if (status == BluetoothHeadsetClientCall.CALL_STATE_TERMINATED) {
            isPlayingWhenInCall = false
        } else if (!isPlayingWhenInCall && KaraokePlayerManager.isPlaying()) {
            isPlayingWhenInCall = true
            if (AppForegroundUtils.isAppInForeground()) {
                // 应用在前台
                showToast(R.string.in_call_hint)
            }
            Log.d(tag, "通话时，正在k歌中，暂停播放.....")
            KaraokePlayerManager.pause()
        }

    }


}