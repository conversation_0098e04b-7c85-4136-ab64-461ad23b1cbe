package com.sgmw.ksongs.phonestatus

import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothHeadsetClient
import android.bluetooth.BluetoothHeadsetClientCall
import android.bluetooth.BluetoothProfile
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.sgmw.common.utils.Log

class PhoneStatusReceiver : BroadcastReceiver() {

    private val tag = "PhoneStatusReceiver"

    override fun onReceive(context: Context, intent: Intent?) {
        Log.d(tag, "action: ${intent?.action}")
        if (BluetoothHeadsetClient.ACTION_CALL_CHANGED == intent?.action) { // 蓝牙电话状态变化
            val client = intent.getParcelableExtra<BluetoothHeadsetClientCall>(BluetoothHeadsetClient.EXTRA_CALL)
            val phoneStatus = client?.state ?: BluetoothHeadsetClientCall.CALL_STATE_TERMINATED
            Log.d(tag, "status: ${client?.state}")
            PhoneStatusManager.updatePhoneStatus(phoneStatus)
            PhoneStatusManager.pauseOrResumePlayerIfNeed(phoneStatus)
        } else if (BluetoothHeadsetClient.ACTION_CONNECTION_STATE_CHANGED == intent?.action) { // 蓝牙耳机的连接状态
            val connectState = intent.getIntExtra(BluetoothProfile.EXTRA_STATE, BluetoothProfile.STATE_DISCONNECTING)
            Log.d(tag, "connectState: $connectState")
            if (connectState == BluetoothProfile.STATE_CONNECTED) { // 连接上后，尝试获取一次蓝牙通话状态
                PhoneStatusManager.initPhoneStatus(context.applicationContext)
            } else if (connectState == BluetoothProfile.STATE_DISCONNECTED) {
                PhoneStatusManager.updatePhoneStatus(BluetoothHeadsetClientCall.CALL_STATE_TERMINATED)
                PhoneStatusManager.pauseOrResumePlayerIfNeed(BluetoothHeadsetClientCall.CALL_STATE_TERMINATED)
            }
        } else if (BluetoothAdapter.ACTION_STATE_CHANGED == intent?.action) { // 蓝牙开关
            val openState = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.ERROR)
            Log.d(tag, "openState: $openState")
            if (openState == BluetoothAdapter.STATE_OFF) {
                PhoneStatusManager.updatePhoneStatus(BluetoothHeadsetClientCall.CALL_STATE_TERMINATED)
                PhoneStatusManager.pauseOrResumePlayerIfNeed(BluetoothHeadsetClientCall.CALL_STATE_TERMINATED) // 通话过程中，关闭蓝牙后，
            } else if (openState == BluetoothAdapter.STATE_ON) {
                // 蓝牙打开，后会继续发出耳机连接状态，此处不用处理
            }
        }
    }



}