package com.sgmw.ksongs.crash

import android.os.Handler
import android.os.Looper
import com.sgmw.common.utils.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicBoolean

/**
 * FireEye拦截器
 * 专门用于检测和拦截FireEye对异常处理器的覆盖行为
 * 
 * 策略：
 * 1. 监控Thread.setDefaultUncaughtExceptionHandler的调用
 * 2. 检测FireEye相关的异常处理器设置
 * 3. 在FireEye设置后立即重新设置我们的处理器
 * 4. 提供Hook机制拦截FireEye的设置行为
 */
object FireEyeInterceptor {
    
    private const val TAG = "FireEyeInterceptor"
    private const val MONITOR_INTERVAL = 1000L // 监控间隔
    
    private val isIntercepting = AtomicBoolean(false)
    private val isHookEnabled = AtomicBoolean(false)
    private var interceptScope: CoroutineScope? = null
    private var ourSafeHandler: SafeUncaughtExceptionHandler? = null
    
    // 统计信息
    private var fireEyeDetectionCount = 0
    private var lastFireEyeDetectionTime = 0L
    
    /**
     * 开始拦截FireEye
     */
    fun startIntercepting(safeHandler: SafeUncaughtExceptionHandler) {
        if (isIntercepting.compareAndSet(false, true)) {
            ourSafeHandler = safeHandler
            interceptScope = CoroutineScope(Dispatchers.Default + SupervisorJob())
            
            Log.d(TAG, "开始FireEye拦截")
            
            // 尝试Hook Thread.setDefaultUncaughtExceptionHandler
            tryHookExceptionHandlerSetter()
            
            // 开始监控
            startMonitoring()
        }
    }
    
    /**
     * 停止拦截
     */
    fun stopIntercepting() {
        if (isIntercepting.compareAndSet(true, false)) {
            Log.d(TAG, "停止FireEye拦截")
            interceptScope?.cancel()
            interceptScope = null
            isHookEnabled.set(false)
        }
    }
    
    /**
     * 尝试Hook Thread.setDefaultUncaughtExceptionHandler方法
     * 这是一个实验性功能，可能在某些Android版本上不工作
     */
    private fun tryHookExceptionHandlerSetter() {
        try {
            Log.d(TAG, "尝试Hook Thread.setDefaultUncaughtExceptionHandler")
            
            // 这里可以实现更复杂的Hook逻辑
            // 由于Android的安全限制，直接Hook系统方法比较困难
            // 我们主要依靠监控机制
            
            Log.d(TAG, "Hook尝试完成（当前版本使用监控机制）")
            
        } catch (e: Exception) {
            Log.w(TAG, "Hook Thread.setDefaultUncaughtExceptionHandler失败，使用监控机制", e)
        }
    }
    
    /**
     * 开始监控异常处理器变化
     */
    private fun startMonitoring() {
        interceptScope?.launch {
            Log.d(TAG, "开始监控异常处理器变化")
            
            while (isIntercepting.get()) {
                try {
                    checkForFireEyeOverride()
                    delay(MONITOR_INTERVAL)
                } catch (e: Exception) {
                    Log.e(TAG, "监控过程中发生错误", e)
                    delay(MONITOR_INTERVAL * 2) // 出错时延长间隔
                }
            }
        }
    }
    
    /**
     * 检查FireEye是否覆盖了我们的异常处理器
     */
    private fun checkForFireEyeOverride() {
        try {
            val currentHandler = Thread.getDefaultUncaughtExceptionHandler()
            val expectedHandler = ourSafeHandler

            // 检查当前处理器是否是我们的SafeUncaughtExceptionHandler类型
            val isOurHandlerType = currentHandler is SafeUncaughtExceptionHandler
            val isExactMatch = currentHandler == expectedHandler

            if (!isOurHandlerType) {
                // 当前处理器不是SafeUncaughtExceptionHandler类型，可能被FireEye等覆盖
                val handlerClassName = currentHandler?.javaClass?.name ?: "null"
                val isFireEyeHandler = isFireEyeRelated(handlerClassName)

                if (isFireEyeHandler) {
                    handleFireEyeOverride(currentHandler, handlerClassName)
                } else {
                    Log.w(TAG, "检测到未知组件覆盖异常处理器: $handlerClassName")
                    // 对于非FireEye的覆盖，也要重新设置我们的处理器
                    restoreOurHandler(currentHandler)
                }
            } else if (!isExactMatch) {
                // 是SafeUncaughtExceptionHandler但不是我们期望的实例，更新引用
                Log.d(TAG, "检测到SafeUncaughtExceptionHandler实例变化，更新引用")
                ourSafeHandler = currentHandler as SafeUncaughtExceptionHandler
            }

        } catch (e: Exception) {
            Log.e(TAG, "检查FireEye覆盖时发生错误", e)
        }
    }
    
    /**
     * 处理FireEye覆盖我们的异常处理器
     */
    private fun handleFireEyeOverride(fireEyeHandler: Thread.UncaughtExceptionHandler?, handlerClassName: String) {
        // 避免对SafeUncaughtExceptionHandler的误报
        if (fireEyeHandler is SafeUncaughtExceptionHandler) {
            Log.d(TAG, "误报：当前处理器实际上是SafeUncaughtExceptionHandler")
            ourSafeHandler = fireEyeHandler
            return
        }

        fireEyeDetectionCount++
        lastFireEyeDetectionTime = System.currentTimeMillis()

        Log.w(TAG, "检测到真正的FireEye覆盖异常处理器！")
        Log.w(TAG, "FireEye处理器: $handlerClassName")
        Log.w(TAG, "检测次数: $fireEyeDetectionCount")

        // 立即重新设置我们的处理器，将FireEye作为原始处理器
        restoreOurHandler(fireEyeHandler)

        // 如果FireEye频繁覆盖，采取更激进的措施
        if (fireEyeDetectionCount >= 3) {
            Log.w(TAG, "FireEye频繁覆盖异常处理器，启用强化拦截模式")
            enableEnhancedInterception()
        }
    }
    
    /**
     * 恢复我们的异常处理器
     */
    private fun restoreOurHandler(originalHandler: Thread.UncaughtExceptionHandler?) {
        try {
            // 只有当原始处理器不是SafeUncaughtExceptionHandler时才重新创建
            val currentHandler = Thread.getDefaultUncaughtExceptionHandler()

            if (currentHandler !is SafeUncaughtExceptionHandler) {
                // 创建新的SafeUncaughtExceptionHandler，将当前处理器作为原始处理器
                val newSafeHandler = SafeUncaughtExceptionHandler(originalHandler)
                ourSafeHandler = newSafeHandler

                // 在主线程设置异常处理器
                val mainHandler = Handler(Looper.getMainLooper())
                mainHandler.post {
                    try {
                        Thread.setDefaultUncaughtExceptionHandler(newSafeHandler)
                        Log.d(TAG, "成功恢复SafeUncaughtExceptionHandler")
                    } catch (e: Exception) {
                        Log.e(TAG, "在主线程恢复异常处理器失败", e)
                    }
                }
            } else {
                // 当前已经是SafeUncaughtExceptionHandler，只需要更新引用
                Log.d(TAG, "当前已经是SafeUncaughtExceptionHandler，更新引用")
                ourSafeHandler = currentHandler as SafeUncaughtExceptionHandler
            }

        } catch (e: Exception) {
            Log.e(TAG, "恢复异常处理器失败", e)
        }
    }
    
    /**
     * 启用强化拦截模式
     * 在FireEye频繁覆盖时使用更激进的策略
     */
    private fun enableEnhancedInterception() {
        try {
            Log.w(TAG, "启用强化拦截模式")
            
            // 提高监控频率
            // 这里可以实现更复杂的拦截逻辑
            
            // 通知ExceptionHandlerGuard启用强制保护模式
            ExceptionHandlerGuard.enableForceProtectionMode()
            
        } catch (e: Exception) {
            Log.e(TAG, "启用强化拦截模式失败", e)
        }
    }
    
    /**
     * 检查是否是FireEye相关的处理器
     * 注意：排除我们自己的SafeUncaughtExceptionHandler
     */
    private fun isFireEyeRelated(handlerClassName: String): Boolean {
        // 首先排除我们自己的处理器
        if (handlerClassName.contains("SafeUncaughtExceptionHandler")) {
            return false
        }

        return handlerClassName.contains("FireEye", ignoreCase = true) ||
                handlerClassName.contains("fireeye", ignoreCase = true) ||
                handlerClassName.contains("com.tme", ignoreCase = true) ||
                handlerClassName.contains("CrashHandle", ignoreCase = true) ||
                (handlerClassName.contains("crash", ignoreCase = true) &&
                 !handlerClassName.contains("com.sgmw.ksongs.crash"))
    }
    
    /**
     * 获取拦截状态信息
     */
    fun getInterceptionStatus(): String {
        val currentHandler = Thread.getDefaultUncaughtExceptionHandler()
        val isOurHandlerType = currentHandler is SafeUncaughtExceptionHandler
        val isExactMatch = currentHandler == ourSafeHandler

        return """
            拦截状态: ${if (isIntercepting.get()) "活跃" else "停止"}
            Hook状态: ${if (isHookEnabled.get()) "启用" else "禁用"}
            处理器类型正确: ${if (isOurHandlerType) "是" else "否"}
            处理器实例匹配: ${if (isExactMatch) "是" else "否"}
            当前处理器: ${currentHandler?.javaClass?.name}
            FireEye检测次数: $fireEyeDetectionCount
            最后检测时间: ${if (lastFireEyeDetectionTime > 0) java.text.SimpleDateFormat("HH:mm:ss").format(lastFireEyeDetectionTime) else "无"}
        """.trimIndent()
    }
    
    /**
     * 强制执行一次检查
     */
    fun forceCheck() {
        if (isIntercepting.get()) {
            interceptScope?.launch {
                checkForFireEyeOverride()
            }
        }
    }
}
