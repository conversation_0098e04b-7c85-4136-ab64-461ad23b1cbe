package com.sgmw.ksongs.crash

import com.sgmw.common.utils.Log
import kotlinx.coroutines.*

/**
 * 崩溃保护调试器
 * 用于监控和调试异常处理器的状态，帮助分析ANR问题
 */
object CrashProtectionDebugger {

    private const val TAG = "CrashProtectionDebugger"

    // 定期报告的协程作用域和任务
    private var reportScope: CoroutineScope? = null
    private var reportJob: Job? = null
    
    /**
     * 打印当前崩溃保护状态的完整报告
     */
    fun printFullStatus() {
        try {
            Log.d(TAG, "=== 崩溃保护状态报告 ===")
            
            // 当前异常处理器信息
            val currentHandler = Thread.getDefaultUncaughtExceptionHandler()
            Log.d(TAG, "当前异常处理器: ${currentHandler?.javaClass?.name}")
            Log.d(TAG, "处理器对象: $currentHandler")
            
            // ExceptionHandlerGuard状态
            Log.d(TAG, "\n--- ExceptionHandlerGuard状态 ---")
            Log.d(TAG, ExceptionHandlerGuard.getGuardStatus())
            
            // FireEyeInterceptor状态
            Log.d(TAG, "\n--- FireEyeInterceptor状态 ---")
            Log.d(TAG, FireEyeInterceptor.getInterceptionStatus())

            // UltimateCrashProtector状态
            Log.d(TAG, "\n--- UltimateCrashProtector状态 ---")
            Log.d(TAG, UltimateCrashProtector.getProtectionStatus())

            // 系统信息
            Log.d(TAG, "\n--- 系统信息 ---")
            Log.d(TAG, "主线程: ${Thread.currentThread() == android.os.Looper.getMainLooper().thread}")
            Log.d(TAG, "当前线程: ${Thread.currentThread().name}")
            Log.d(TAG, "进程ID: ${android.os.Process.myPid()}")
            
            Log.d(TAG, "=== 崩溃保护状态报告结束 ===")
            
        } catch (e: Exception) {
            Log.e(TAG, "打印状态报告时发生错误", e)
        }
    }
    
    /**
     * 执行崩溃保护系统的健康检查
     */
    fun performHealthCheck() {
        try {
            Log.d(TAG, "开始崩溃保护系统健康检查")
            
            // 检查异常处理器是否正确设置
            val currentHandler = Thread.getDefaultUncaughtExceptionHandler()
            val isSafeHandler = currentHandler is SafeUncaughtExceptionHandler
            
            if (isSafeHandler) {
                Log.d(TAG, "✓ 异常处理器状态正常")
            } else {
                Log.w(TAG, "✗ 异常处理器被覆盖: ${currentHandler?.javaClass?.name}")
                
                // 尝试修复
                ExceptionHandlerGuard.forceCheck()
                FireEyeInterceptor.forceCheck()
            }
            
            // 检查各个组件的运行状态
            checkComponentStatus()
            
            Log.d(TAG, "崩溃保护系统健康检查完成")
            
        } catch (e: Exception) {
            Log.e(TAG, "健康检查时发生错误", e)
        }
    }
    
    /**
     * 检查各个保护组件的状态
     */
    private fun checkComponentStatus() {
        try {
            // 这里可以添加更多的组件状态检查
            Log.d(TAG, "检查保护组件状态...")
            
            // 可以检查内存监控、ANR监控等组件的状态
            
        } catch (e: Exception) {
            Log.e(TAG, "检查组件状态时发生错误", e)
        }
    }
    
    /**
     * 模拟异常测试崩溃保护系统
     * 仅在Debug模式下使用
     */
    fun simulateException(exceptionType: String = "IndexOutOfBounds") {
        try {
            Log.w(TAG, "模拟异常测试: $exceptionType")
            
            when (exceptionType) {
                "IndexOutOfBounds" -> {
                    val emptyList = emptyList<String>()
                    emptyList[0] // 这会抛出IndexOutOfBoundsException
                }
                "NullPointer" -> {
                    val nullString: String? = null
                    nullString!!.length // 这会抛出NullPointerException
                }
                "IllegalState" -> {
                    throw IllegalStateException("模拟的IllegalStateException")
                }
                else -> {
                    throw RuntimeException("模拟的RuntimeException: $exceptionType")
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "模拟异常: $exceptionType", e)
            throw e // 重新抛出，让异常处理器处理
        }
    }
    
    /**
     * 启动定期状态报告
     * 用于长期监控异常处理器的状态
     */
    fun startPeriodicStatusReport(intervalMs: Long = 30000L) {
        // 如果已经在运行，先停止
        stopPeriodicStatusReport()

        // 创建新的协程作用域
        reportScope = CoroutineScope(Dispatchers.Default + SupervisorJob())

        reportJob = reportScope?.launch {
            Log.d(TAG, "开始定期状态报告，间隔: ${intervalMs}ms")

            while (isActive) {
                try {
                    delay(intervalMs)
                    if (isActive) {
                        printFullStatus()
                    }
                } catch (e: Exception) {
                    if (isActive) {
                        Log.e(TAG, "定期状态报告时发生错误", e)
                        delay(intervalMs * 2) // 出错时延长间隔
                    }
                }
            }
        }
    }

    /**
     * 停止定期状态报告
     */
    fun stopPeriodicStatusReport() {
        try {
            Log.d(TAG, "停止定期状态报告")

            reportJob?.cancel()
            reportJob = null

            reportScope?.cancel()
            reportScope = null

        } catch (e: Exception) {
            Log.e(TAG, "停止定期状态报告时发生错误", e)
        }
    }
}
