package com.sgmw.ksongs.crash

import android.os.Looper
import android.os.Process
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.utils.MemoryMonitor
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import java.io.PrintWriter
import java.io.StringWriter
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 安全的全局异常处理器
 * 优先级高于FireEye，防止异常传播导致ANR
 *
 * 设计原则：
 * 1. 异常处理不能在主线程进行阻塞操作
 * 2. 异常处理本身不能抛出异常
 * 3. 在内存压力高时简化处理逻辑
 * 4. 提供降级处理机制
 * 5. 增强版：防止FireEye等第三方组件导致ANR
 */
class SafeUncaughtExceptionHandler(
    private val defaultHandler: Thread.UncaughtExceptionHandler?
) : Thread.UncaughtExceptionHandler {

    companion object {
        private const val TAG = "SafeUncaughtExceptionHandler"
        private const val MAX_STACK_TRACE_LENGTH = 2000 // 限制堆栈信息长度
        private const val EMERGENCY_EXIT_TIMEOUT = 1000L // 紧急退出超时时间（缩短到1秒）
    }

    // 使用后台线程处理异常，避免阻塞主线程
    private val exceptionHandlingScope = CoroutineScope(
        Dispatchers.IO + SupervisorJob()
    )

    // 防止重复处理同一个异常
    private val isHandling = AtomicBoolean(false)

    override fun uncaughtException(thread: Thread, exception: Throwable) {
        // 防止重复处理同一个异常
        if (!isHandling.compareAndSet(false, true)) {
            android.util.Log.w(TAG, "异常正在处理中，跳过重复处理")
            return
        }

        val startTime = System.currentTimeMillis()

        try {
            // 立即记录异常发生
            android.util.Log.e(TAG, "捕获到未处理异常: ${exception.javaClass.simpleName} in thread: ${thread.name}")

            // 检查是否在主线程
            val isMainThread = thread == Looper.getMainLooper().thread

            if (isMainThread) {
                // 主线程异常：立即触发终极保护，然后安全退出
                android.util.Log.e(TAG, "主线程异常：触发终极保护器")
                UltimateCrashProtector.triggerUltimateProtection(thread, exception)

                // 同时执行我们自己的立即退出逻辑
                handleMainThreadExceptionWithImmediateExit(thread, exception)
                // 理论上不会执行到这里
                return
            } else {
                // 后台线程异常：可以同步处理，但要快速
                handleBackgroundThreadException(thread, exception)

                // 后台线程异常才需要调用最终处理逻辑
                handleFinalProcessing(thread, exception)
            }

        } catch (handlerException: Exception) {
            // 异常处理器本身出现异常时的最后防线
            try {
                android.util.Log.e(TAG, "Exception in SafeUncaughtExceptionHandler: $handlerException")
                android.util.Log.e(TAG, "Original exception: $exception")
            } catch (e: Exception) {
                // 连日志都无法记录时，静默处理
            }

            // 如果是主线程异常且处理器出错，立即强制退出
            if (thread == Looper.getMainLooper().thread) {
                android.util.Log.e(TAG, "主线程异常处理器出错，立即强制退出")
                try {
                    Process.killProcess(Process.myPid())
                } catch (e: Exception) {
                    System.exit(1)
                }
            }
        } finally {
            val processingTime = System.currentTimeMillis() - startTime
            android.util.Log.d(TAG, "异常处理耗时: ${processingTime}ms")

            // 重置处理状态
            isHandling.set(false)
        }
    }

    /**
     * 处理主线程异常（最激进版本）
     * 立即安全退出，不给FireEye任何执行机会
     */
    private fun handleMainThreadExceptionWithImmediateExit(thread: Thread, exception: Throwable) {
        try {
            // 立即记录关键信息
            android.util.Log.e(TAG, "主线程未捕获异常，立即安全退出: ${exception.javaClass.simpleName}")

            // 快速记录异常信息（同步方式，确保记录）
            try {
                val stackTrace = getStackTraceString(exception)
                android.util.Log.e(TAG, "异常详情: ${exception.message}")
                android.util.Log.e(TAG, "堆栈信息: $stackTrace")
            } catch (e: Exception) {
                android.util.Log.e(TAG, "记录异常详情失败: $e")
            }

            // 立即强制退出，不等待任何异步操作
            android.util.Log.e(TAG, "主线程异常：立即执行Process.killProcess防止ANR")

            try {
                // 不使用任何延迟，立即退出
                Process.killProcess(Process.myPid())
            } catch (e: Exception) {
                android.util.Log.e(TAG, "Process.killProcess失败，使用System.exit: $e")
                System.exit(1)
            }

        } catch (e: Exception) {
            android.util.Log.e(TAG, "处理主线程异常时出错，强制退出: $e")
            try {
                Process.killProcess(Process.myPid())
            } catch (ex: Exception) {
                System.exit(1)
            }
        }
    }

    /**
     * 处理后台线程异常
     * 可以同步处理，但要快速完成
     */
    private fun handleBackgroundThreadException(thread: Thread, exception: Throwable) {
        try {
            // 检查是否是内存相关异常
            if (isMemoryRelated(exception)) {
                android.util.Log.e(TAG, "Memory-related exception detected: ${exception.javaClass.simpleName} in ${thread.name}")
                // 立即触发紧急内存清理
                try {
                    MemoryMonitor.performEmergencyCleanup()
                } catch (cleanupError: Exception) {
                    android.util.Log.e(TAG, "Emergency memory cleanup failed: $cleanupError")
                }
            }

            // 检查内存压力，决定处理策略
            if (MemoryMonitor.isMemoryPressureHigh()) {
                // 内存压力高时，只记录简单信息
                android.util.Log.e(TAG, "Background thread exception under memory pressure: ${exception.javaClass.simpleName} in ${thread.name}")
            } else {
                // 正常情况下记录详细信息，但要快速处理
                processExceptionDetails(thread, exception, false)
            }

        } catch (e: Exception) {
            android.util.Log.e(TAG, "Error handling background thread exception: $e")
        }
    }

    /**
     * 处理异常详细信息
     * @param isMainThread 是否为主线程异常
     */
    private fun processExceptionDetails(thread: Thread, exception: Throwable, isMainThread: Boolean) {
        try {
            val threadInfo = "Thread: ${thread.name} (id=${thread.id}, main=$isMainThread)"
            val exceptionInfo = "Exception: ${exception.javaClass.name}: ${exception.message}"
            
            // 获取堆栈信息，但限制长度避免内存问题
            val stackTrace = getStackTraceString(exception)
            
            // 使用自定义Log记录（如果可用）
            if (MemoryMonitor.isMemoryPressureHigh()) {
                Log.e(TAG, "$threadInfo - $exceptionInfo")
            } else {
                Log.e(TAG, "$threadInfo - $exceptionInfo\nStackTrace:\n$stackTrace")
            }
            
            // 记录内存状态
            logMemoryStatus()
            
        } catch (e: Exception) {
            android.util.Log.e(TAG, "Error processing exception details: $e")
        }
    }

    /**
     * 检查异常是否与内存相关
     */
    private fun isMemoryRelated(exception: Throwable): Boolean {
        return try {
            when {
                exception is OutOfMemoryError -> true
                exception.message?.contains("bad_alloc", ignoreCase = true) == true -> true
                exception.message?.contains("memory", ignoreCase = true) == true -> true
                exception.message?.contains("heap", ignoreCase = true) == true -> true
                exception.javaClass.simpleName.contains("Memory", ignoreCase = true) -> true
                // 检查堆栈信息中是否包含内存相关关键词
                exception.stackTraceToString().contains("OutOfMemoryError", ignoreCase = true) -> true
                exception.stackTraceToString().contains("bad_alloc", ignoreCase = true) -> true
                else -> false
            }
        } catch (e: Exception) {
            // 检查过程中出错，保守地返回false
            false
        }
    }

    /**
     * 获取异常堆栈信息，限制长度
     */
    private fun getStackTraceString(exception: Throwable): String {
        return try {
            val sw = StringWriter()
            val pw = PrintWriter(sw)
            exception.printStackTrace(pw)
            val stackTrace = sw.toString()
            
            // 限制堆栈信息长度，避免内存问题
            if (stackTrace.length > MAX_STACK_TRACE_LENGTH) {
                stackTrace.substring(0, MAX_STACK_TRACE_LENGTH) + "\n... (truncated)"
            } else {
                stackTrace
            }
        } catch (e: Exception) {
            "Failed to get stack trace: $e"
        }
    }

    /**
     * 记录内存状态
     */
    private fun logMemoryStatus() {
        try {
            val runtime = Runtime.getRuntime()
            val usedMemory = runtime.totalMemory() - runtime.freeMemory()
            val maxMemory = runtime.maxMemory()
            val memoryUsageRatio = usedMemory.toFloat() / maxMemory.toFloat()
            
            android.util.Log.d(TAG, "Memory usage: ${(memoryUsageRatio * 100).toInt()}% (${usedMemory / 1024 / 1024}MB / ${maxMemory / 1024 / 1024}MB)")
        } catch (e: Exception) {
            // 内存状态记录失败时静默处理
        }
    }

    /**
     * 最终处理逻辑（最激进版本）
     * 主线程异常已经在handleMainThreadExceptionWithImmediateExit中处理并退出
     * 这里只处理后台线程异常
     */
    private fun handleFinalProcessing(thread: Thread, exception: Throwable) {
        try {
            val isMainThread = thread == Looper.getMainLooper().thread

            if (isMainThread) {
                // 主线程异常：理论上不应该到达这里，因为已经在handleMainThreadExceptionWithImmediateExit中退出
                android.util.Log.e(TAG, "警告：主线程异常到达最终处理逻辑，立即强制退出")
                try {
                    Process.killProcess(Process.myPid())
                } catch (e: Exception) {
                    System.exit(1)
                }
            } else {
                // 后台线程异常：检查是否应该调用原始处理器
                val shouldCallOriginalHandler = shouldCallOriginalHandler(thread, exception)

                if (shouldCallOriginalHandler && defaultHandler != null) {
                    try {
                        defaultHandler.uncaughtException(thread, exception)
                    } catch (e: Exception) {
                        android.util.Log.e(TAG, "调用原始处理器出错: $e")
                    }
                } else {
                    android.util.Log.d(TAG, "后台线程异常：跳过原始处理器")
                }
            }

        } catch (e: Exception) {
            android.util.Log.e(TAG, "最终处理逻辑出错: $e")

            // 如果是主线程异常且处理逻辑出错，强制退出
            if (thread == Looper.getMainLooper().thread) {
                try {
                    Process.killProcess(Process.myPid())
                } catch (ex: Exception) {
                    System.exit(1)
                }
            }
        }
    }

    /**
     * 判断是否应该调用原始异常处理器（最激进版本）
     * 采用最严格的策略防止ANR：主线程异常完全不调用原始处理器
     */
    private fun shouldCallOriginalHandler(thread: Thread, exception: Throwable): Boolean {
        return try {
            val isMainThread = thread == Looper.getMainLooper().thread
            val isMemoryPressureHigh = MemoryMonitor.isMemoryPressureHigh()

            when {
                // 主线程异常：完全跳过所有原始处理器，无论是否是FireEye
                isMainThread -> {
                    android.util.Log.w(TAG, "主线程异常：完全跳过所有原始处理器防止ANR")
                    false
                }
                // 内存不足异常：跳过原始处理器
                exception is OutOfMemoryError -> {
                    android.util.Log.w(TAG, "内存不足异常：跳过原始处理器")
                    false
                }
                // 内存压力高时：跳过原始处理器
                isMemoryPressureHigh -> {
                    android.util.Log.w(TAG, "内存压力高：跳过原始处理器")
                    false
                }
                // 检查是否是FireEye相关的处理器
                else -> {
                    val handlerClassName = defaultHandler?.javaClass?.name ?: "null"
                    val isFireEyeHandler = handlerClassName.contains("FireEye", ignoreCase = true) ||
                            handlerClassName.contains("fireeye", ignoreCase = true) ||
                            handlerClassName.contains("com.tme", ignoreCase = true) ||
                            handlerClassName.contains("CrashHandle", ignoreCase = true)

                    if (isFireEyeHandler) {
                        android.util.Log.w(TAG, "FireEye后台线程异常：跳过原始处理器防止潜在问题")
                        false
                    } else {
                        android.util.Log.d(TAG, "非FireEye后台线程异常：允许调用原始处理器 (${thread.name})")
                        true
                    }
                }
            }
        } catch (e: Exception) {
            // 判断逻辑出错时，保守地跳过原始处理器
            android.util.Log.e(TAG, "判断是否调用原始处理器时出错: $e")
            false
        }
    }
}
