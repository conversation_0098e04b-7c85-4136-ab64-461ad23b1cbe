package com.sgmw.ksongs.crash

import android.os.Handler
import android.os.Looper
import com.sgmw.common.utils.Log
import kotlinx.coroutines.*
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicReference

/**
 * 异常处理器守护者
 * 负责监控和保护SafeUncaughtExceptionHandler不被其他组件（如FireEye）覆盖
 * 
 * 设计原则：
 * 1. 定期检查当前的异常处理器是否是我们设置的
 * 2. 如果被覆盖，立即重新设置我们的处理器
 * 3. 记录所有异常处理器的变更，便于调试
 * 4. 提供强制保护模式，在关键时刻确保我们的处理器生效
 */
object ExceptionHandlerGuard {
    
    private const val TAG = "ExceptionHandlerGuard"
    private const val CHECK_INTERVAL = 2000L // 每2秒检查一次
    private const val FORCE_PROTECTION_INTERVAL = 500L // 强制保护模式下每500ms检查一次
    
    private val isGuarding = AtomicBoolean(false)
    private val isForceProtectionMode = AtomicBoolean(false)
    private val ourHandler = AtomicReference<SafeUncaughtExceptionHandler?>(null)
    private val lastKnownHandler = AtomicReference<Thread.UncaughtExceptionHandler?>(null)
    
    private var guardScope: CoroutineScope? = null
    private val mainHandler = Handler(Looper.getMainLooper())
    private var guardRunnable: Runnable? = null
    
    // 统计信息
    private var handlerOverrideCount = 0
    private var lastOverrideTime = 0L
    private var lastCheckTime = 0L
    private var consecutiveNormalChecks = 0
    private var lastLogTime = 0L
    private const val LOG_THROTTLE_INTERVAL = 10000L // 10秒内最多输出一次相同的日志
    
    /**
     * 开始守护异常处理器
     * @param safeHandler 我们的安全异常处理器
     */
    fun startGuarding(safeHandler: SafeUncaughtExceptionHandler) {
        if (isGuarding.compareAndSet(false, true)) {
            ourHandler.set(safeHandler)
            guardScope = CoroutineScope(Dispatchers.Default + SupervisorJob())
            
            Log.d(TAG, "开始守护异常处理器")
            
            // 立即设置我们的处理器
            setOurHandler()
            
            // 开始定期检查
            scheduleNextCheck()
        }
    }
    
    /**
     * 停止守护
     */
    fun stopGuarding() {
        if (isGuarding.compareAndSet(true, false)) {
            Log.d(TAG, "停止守护异常处理器")
            
            guardRunnable?.let { mainHandler.removeCallbacks(it) }
            guardScope?.cancel()
            guardScope = null
            isForceProtectionMode.set(false)
        }
    }
    
    /**
     * 启用强制保护模式
     * 在检测到异常处理器被频繁覆盖时启用，提高检查频率
     */
    fun enableForceProtectionMode() {
        if (isForceProtectionMode.compareAndSet(false, true)) {
            Log.w(TAG, "启用强制保护模式 - 检查频率提升到${FORCE_PROTECTION_INTERVAL}ms")
            
            // 取消当前的检查任务
            guardRunnable?.let { mainHandler.removeCallbacks(it) }
            
            // 重新安排更频繁的检查
            scheduleNextCheck()
        }
    }
    
    /**
     * 禁用强制保护模式
     */
    fun disableForceProtectionMode() {
        if (isForceProtectionMode.compareAndSet(true, false)) {
            Log.d(TAG, "禁用强制保护模式 - 恢复正常检查频率")
            
            // 取消当前的检查任务
            guardRunnable?.let { mainHandler.removeCallbacks(it) }
            
            // 重新安排正常频率的检查
            scheduleNextCheck()
        }
    }
    
    /**
     * 安排下一次检查
     */
    private fun scheduleNextCheck() {
        if (!isGuarding.get()) return
        
        guardRunnable = Runnable {
            checkAndProtectHandler()
            scheduleNextCheck()
        }
        
        val interval = if (isForceProtectionMode.get()) {
            FORCE_PROTECTION_INTERVAL
        } else {
            CHECK_INTERVAL
        }
        
        mainHandler.postDelayed(guardRunnable!!, interval)
    }
    
    /**
     * 检查并保护异常处理器
     */
    private fun checkAndProtectHandler() {
        try {
            val currentTime = System.currentTimeMillis()
            val currentHandler = Thread.getDefaultUncaughtExceptionHandler()
            val expectedHandler = ourHandler.get()

            // 检查当前处理器是否是我们的SafeUncaughtExceptionHandler
            val isOurHandler = currentHandler is SafeUncaughtExceptionHandler
            val isExpectedHandler = currentHandler == expectedHandler

            if (!isOurHandler) {
                // 当前处理器不是我们的SafeUncaughtExceptionHandler，需要重新设置
                consecutiveNormalChecks = 0 // 重置正常检查计数
                handleHandlerOverride(currentHandler, expectedHandler)
            } else if (!isExpectedHandler) {
                // 是SafeUncaughtExceptionHandler但不是我们期望的实例，更新我们的引用
                consecutiveNormalChecks = 0 // 重置正常检查计数
                Log.d(TAG, "检测到SafeUncaughtExceptionHandler实例变化，更新引用")
                ourHandler.set(currentHandler as SafeUncaughtExceptionHandler)
                lastKnownHandler.set(currentHandler)
            } else {
                // 处理器正常
                consecutiveNormalChecks++
                lastKnownHandler.set(currentHandler)

                // 如果连续多次检查都正常，可以考虑降低检查频率或退出强制保护模式
                if (consecutiveNormalChecks >= 10 && isForceProtectionMode.get()) {
                    val now = System.currentTimeMillis()
                    if (now - lastLogTime > LOG_THROTTLE_INTERVAL) {
                        Log.d(TAG, "连续${consecutiveNormalChecks}次检查正常，退出强制保护模式")
                        lastLogTime = now
                    }
                    disableForceProtectionMode()
                }

                // 定期输出正常状态（降低频率）
                if (consecutiveNormalChecks % 30 == 0) { // 每30次检查输出一次
                    val now = System.currentTimeMillis()
                    if (now - lastLogTime > LOG_THROTTLE_INTERVAL) {
                        Log.d(TAG, "异常处理器状态正常，连续检查${consecutiveNormalChecks}次")
                        lastLogTime = now
                    }
                }
            }

            lastCheckTime = currentTime

        } catch (e: Exception) {
            Log.e(TAG, "检查异常处理器时发生错误", e)
        }
    }
    
    /**
     * 处理异常处理器被覆盖的情况
     */
    private fun handleHandlerOverride(
        currentHandler: Thread.UncaughtExceptionHandler?,
        expectedHandler: SafeUncaughtExceptionHandler?
    ) {
        // 避免对SafeUncaughtExceptionHandler的误报
        if (currentHandler is SafeUncaughtExceptionHandler) {
            Log.d(TAG, "当前处理器已经是SafeUncaughtExceptionHandler，更新引用")
            ourHandler.set(currentHandler)
            return
        }

        handlerOverrideCount++
        lastOverrideTime = System.currentTimeMillis()

        Log.w(TAG, "检测到异常处理器被真正覆盖！")
        Log.w(TAG, "当前处理器: ${currentHandler?.javaClass?.name}")
        Log.w(TAG, "期望处理器: ${expectedHandler?.javaClass?.name}")
        Log.w(TAG, "覆盖次数: $handlerOverrideCount")

        // 检查是否是FireEye覆盖的
        val isFireEyeHandler = currentHandler?.javaClass?.name?.contains("FireEye") == true ||
                currentHandler?.javaClass?.name?.contains("fireeye") == true ||
                currentHandler?.javaClass?.name?.contains("com.tme") == true ||
                currentHandler?.javaClass?.name?.contains("CrashHandle") == true

        if (isFireEyeHandler) {
            Log.w(TAG, "检测到FireEye覆盖了我们的异常处理器")

            // 如果覆盖次数过多，启用强制保护模式
            if (handlerOverrideCount >= 3 && !isForceProtectionMode.get()) {
                enableForceProtectionMode()
            }
        }

        // 重新设置我们的处理器，并将当前处理器作为原始处理器传递
        try {
            val newSafeHandler = SafeUncaughtExceptionHandler(currentHandler)
            ourHandler.set(newSafeHandler)
            Thread.setDefaultUncaughtExceptionHandler(newSafeHandler)
            Log.d(TAG, "成功重新设置SafeUncaughtExceptionHandler")
        } catch (e: Exception) {
            Log.e(TAG, "重新设置异常处理器失败", e)
        }
    }
    
    /**
     * 强制设置我们的异常处理器
     * 在应用启动和关键时刻调用
     */
    fun setOurHandler() {
        try {
            val currentHandler = Thread.getDefaultUncaughtExceptionHandler()
            val expectedHandler = ourHandler.get()
            
            if (currentHandler != expectedHandler && expectedHandler != null) {
                Thread.setDefaultUncaughtExceptionHandler(expectedHandler)
                Log.d(TAG, "强制设置SafeUncaughtExceptionHandler")
                Log.d(TAG, "替换的处理器: ${currentHandler?.javaClass?.name}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "强制设置异常处理器失败", e)
        }
    }
    
    /**
     * 获取守护状态信息
     */
    fun getGuardStatus(): String {
        val currentHandler = Thread.getDefaultUncaughtExceptionHandler()
        val expectedHandler = ourHandler.get()
        val isOurHandlerType = currentHandler is SafeUncaughtExceptionHandler
        val isExactMatch = currentHandler == expectedHandler

        return """
            守护状态: ${if (isGuarding.get()) "活跃" else "停止"}
            强制保护模式: ${if (isForceProtectionMode.get()) "启用" else "禁用"}
            处理器类型正确: ${if (isOurHandlerType) "是" else "否"}
            处理器实例匹配: ${if (isExactMatch) "是" else "否"}
            当前处理器: ${currentHandler?.javaClass?.name}
            期望处理器: ${expectedHandler?.javaClass?.name}
            覆盖次数: $handlerOverrideCount
            最后覆盖时间: ${if (lastOverrideTime > 0) java.text.SimpleDateFormat("HH:mm:ss").format(lastOverrideTime) else "无"}
        """.trimIndent()
    }
    
    /**
     * 立即执行一次检查
     */
    fun forceCheck() {
        if (isGuarding.get()) {
            checkAndProtectHandler()
        }
    }
}
