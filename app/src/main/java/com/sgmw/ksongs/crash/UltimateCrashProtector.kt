package com.sgmw.ksongs.crash

import android.os.Looper
import android.os.Process
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 终极崩溃保护器
 * 这是最后的防线，采用最激进的策略防止ANR
 * 
 * 策略：
 * 1. 在异常发生时立即启动倒计时
 * 2. 如果在指定时间内进程没有正常退出，强制杀死进程
 * 3. 不依赖任何第三方组件，直接使用系统API
 */
object UltimateCrashProtector {
    
    private const val TAG = "UltimateCrashProtector"
    private const val FORCE_EXIT_TIMEOUT = 800L // 800ms后强制退出
    
    private val isProtecting = AtomicBoolean(false)
    private val hasTriggered = AtomicBoolean(false)
    
    /**
     * 启动终极保护
     * 在检测到主线程异常时调用
     */
    fun triggerUltimateProtection(thread: Thread, exception: Throwable) {
        // 防止重复触发
        if (!hasTriggered.compareAndSet(false, true)) {
            android.util.Log.w(TAG, "终极保护已经触发，跳过重复调用")
            return
        }
        
        val isMainThread = thread == Looper.getMainLooper().thread
        
        try {
            android.util.Log.e(TAG, "=== 触发终极崩溃保护 ===")
            android.util.Log.e(TAG, "异常线程: ${thread.name}")
            android.util.Log.e(TAG, "是否主线程: $isMainThread")
            android.util.Log.e(TAG, "异常类型: ${exception.javaClass.simpleName}")
            android.util.Log.e(TAG, "异常消息: ${exception.message}")
            
            if (isMainThread) {
                // 主线程异常：立即启动强制退出倒计时
                startForceExitCountdown()
                
                // 立即尝试安全退出
                performImmediateExit("主线程异常")
            } else {
                // 后台线程异常：记录但不强制退出
                android.util.Log.w(TAG, "后台线程异常，不触发强制退出")
            }
            
        } catch (e: Exception) {
            android.util.Log.e(TAG, "触发终极保护时发生错误: $e")
            // 即使出错也要尝试退出
            if (isMainThread) {
                performImmediateExit("终极保护出错")
            }
        }
    }
    
    /**
     * 启动强制退出倒计时
     */
    private fun startForceExitCountdown() {
        if (!isProtecting.compareAndSet(false, true)) {
            return
        }
        
        try {
            android.util.Log.w(TAG, "启动强制退出倒计时: ${FORCE_EXIT_TIMEOUT}ms")
            
            // 使用系统线程池，不依赖协程
            val thread = Thread {
                try {
                    Thread.sleep(FORCE_EXIT_TIMEOUT)
                    android.util.Log.e(TAG, "强制退出倒计时到期，杀死进程")
                    Process.killProcess(Process.myPid())
                } catch (e: Exception) {
                    android.util.Log.e(TAG, "强制退出倒计时失败: $e")
                    System.exit(1)
                }
            }
            
            thread.isDaemon = true
            thread.name = "UltimateCrashProtector-ForceExit"
            thread.start()
            
        } catch (e: Exception) {
            android.util.Log.e(TAG, "启动强制退出倒计时失败: $e")
        }
    }
    
    /**
     * 立即执行安全退出
     */
    private fun performImmediateExit(reason: String) {
        try {
            android.util.Log.e(TAG, "立即安全退出，原因: $reason")
            
            // 方法1：使用Process.killProcess
            try {
                Process.killProcess(Process.myPid())
            } catch (e: Exception) {
                android.util.Log.e(TAG, "Process.killProcess失败: $e")
                
                // 方法2：使用System.exit
                try {
                    System.exit(1)
                } catch (ex: Exception) {
                    android.util.Log.e(TAG, "System.exit失败: $ex")
                    
                    // 方法3：使用Runtime.halt（最激进）
                    Runtime.getRuntime().halt(1)
                }
            }
            
        } catch (e: Exception) {
            android.util.Log.e(TAG, "执行安全退出时发生错误: $e")
            // 最后的手段
            Runtime.getRuntime().halt(1)
        }
    }
    
    /**
     * 检查保护状态
     */
    fun getProtectionStatus(): String {
        return """
            终极保护状态: ${if (isProtecting.get()) "激活" else "待机"}
            已触发: ${if (hasTriggered.get()) "是" else "否"}
            当前线程: ${Thread.currentThread().name}
            是否主线程: ${Thread.currentThread() == Looper.getMainLooper().thread}
        """.trimIndent()
    }
    
    /**
     * 重置保护状态（仅用于测试）
     */
    fun resetForTesting() {
        isProtecting.set(false)
        hasTriggered.set(false)
        android.util.Log.d(TAG, "重置终极保护状态（测试用）")
    }
}
