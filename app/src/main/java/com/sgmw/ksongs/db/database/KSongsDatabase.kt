package com.sgmw.ksongs.db.database

import androidx.room.RoomDatabase
import com.sgmw.ksongs.db.dao.CollectDao
import com.sgmw.ksongs.db.dao.DemandDao
import com.sgmw.ksongs.db.dao.PlayRecordDao
import com.sgmw.ksongs.db.entity.CollectSongInfo
import com.sgmw.ksongs.db.entity.DemandSongInfo
import com.sgmw.ksongs.db.entity.PlayRecordSongInfo

@androidx.room.Database(
    entities = [DemandSongInfo::class, CollectSongInfo::class, PlayRecordSongInfo::class],
    version = 1,
    exportSchema = false
)
abstract class KSongsDatabase : RoomDatabase() {

    abstract fun getDemandSongInfoDao(): DemandDao

    abstract fun getCollectDao(): CollectDao

    abstract fun getPlayRecordDao(): PlayRecordDao

}