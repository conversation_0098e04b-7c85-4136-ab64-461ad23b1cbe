package com.sgmw.ksongs.db

import android.content.Context
import androidx.room.Room.databaseBuilder
import androidx.room.withTransaction
import com.sgmw.common.utils.DaemonThreadDispatcher
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.db.database.KSongsDatabase
import com.sgmw.ksongs.ui.songplay.KaraokeConsole
import com.sgmw.ksongs.utils.MemoryMonitor
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch

object DbManager {

    private const val TAG = "DbManager"

    private lateinit var KSongsDb: KSongsDatabase
    private var isDbClosed = false
    private lateinit var appContext: Context

    // 数据库专用异常处理器
    // 增强版本：完全消化异常，防止传播到全局处理器
    private val dbExceptionHandler = CoroutineExceptionHandler { context, throwable ->
        try {
            // 立即记录关键信息
            android.util.Log.e(TAG, "Database exception caught: ${throwable.javaClass.simpleName}")

            // 检查内存压力，决定处理策略
            if (MemoryMonitor.isMemoryPressureHigh()) {
                android.util.Log.e(TAG, "Database exception under memory pressure: ${throwable.javaClass.simpleName}")
            } else {
                // 异步记录详细信息，避免阻塞数据库线程
                CoroutineScope(Dispatchers.IO + SupervisorJob()).launch {
                    try {
                        Log.e(TAG, "Database operation exception\nContext: $context", throwable)

                        // 记录数据库状态信息
                        logDatabaseStatus(throwable)
                    } catch (e: Exception) {
                        android.util.Log.e(TAG, "Error logging database exception details: $e")
                    }
                }
            }

            // 重要：异常已被完全处理，不再向上传播
            // 这样可以防止数据库异常传播到全局异常处理器

        } catch (e: Exception) {
            // 异常处理器本身出现异常时的最后防线
            try {
                android.util.Log.e(TAG, "Critical error in DB exception handler: $e")
                android.util.Log.e(TAG, "Original database exception: $throwable")
            } catch (logError: Exception) {
                // 连日志都无法记录时，静默处理
            }
        }
    }

    /**
     * 记录数据库异常时的状态信息
     */
    private fun logDatabaseStatus(throwable: Throwable) {
        try {
            val dbInfo = if (::KSongsDb.isInitialized) {
                "Database initialized: ${!isDbClosed}"
            } else {
                "Database not initialized"
            }

            Log.d(TAG, "Database status at exception: $dbInfo")

            // 如果是特定类型的异常，记录更多信息
            when (throwable) {
                is android.database.sqlite.SQLiteException -> {
                    Log.e(TAG, "SQLite exception details: ${throwable.message}")
                }
                is java.util.concurrent.TimeoutException -> {
                    Log.e(TAG, "Database operation timeout")
                }
            }

        } catch (e: Exception) {
            android.util.Log.e(TAG, "Error logging database status: $e")
        }
    }

    // 使用守护线程的数据库作用域，避免阻塞应用关闭，添加异常处理器
    private var dbScope = CoroutineScope(SupervisorJob() + DaemonThreadDispatcher.Database + dbExceptionHandler)

    /**
     * 初始化数据库 - 使用守护线程避免阻塞应用关闭
     */
    fun initDb(context: Context) {
        appContext = context.applicationContext
        isDbClosed = false

        dbScope.launch {
            try {
                KSongsDb = databaseBuilder(appContext, KSongsDatabase::class.java, "KSongs")
                    .build()

                // 安全地获取播放中的歌曲信息
                try {
                    KaraokeConsole.currSongInfo = getDemandSongInfoDao().getPlayingSongInfo()?.songInfo
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to get playing song info during initialization", e)
                    KaraokeConsole.currSongInfo = null
                }

                Log.d(TAG, "Database initialized successfully")
            } catch (e: Exception) {
                // 数据库初始化异常处理，避免崩溃
                Log.e(TAG, "Database initialization failed", e)
            }
        }
    }

    fun getDemandSongInfoDao() = KSongsDb.getDemandSongInfoDao()

    /**
     * 获取收藏数据库操作dao
     */
    fun getCollectDao() = KSongsDb.getCollectDao()

    /**
     * 播放记录Dao
     */
    fun getPlayRecordDao() = KSongsDb.getPlayRecordDao()

    /**
     * 执行数据库事务，确保操作的原子性
     */
    suspend fun <T> runInTransaction(block: suspend () -> T): T {
        return KSongsDb.withTransaction {
            block()
        }
    }

}