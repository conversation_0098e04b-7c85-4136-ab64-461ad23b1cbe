package com.sgmw.ksongs.db.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.sgmw.ksongs.db.entity.PlayRecordSongInfo
import kotlinx.coroutines.flow.Flow

/**
 * 操作播放记录表的Dao
 */
@Dao
interface PlayRecordDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(playRecord: PlayRecordSongInfo)

    @Query("SELECT * FROM play_record_songInfo WHERE song_id = :songId")
    suspend fun find(songId: String): PlayRecordSongInfo?

    @Query("SELECT * FROM play_record_songInfo ORDER BY insert_time DESC LIMIT 1")
    suspend fun findLast(): PlayRecordSongInfo?

    @Query("SELECT * FROM play_record_songInfo ORDER BY insert_time DESC")
    fun findAll(): Flow<List<PlayRecordSongInfo>>

    @Query("SELECT song_id FROM play_record_songInfo")
    suspend fun findPlayRecordSongIds(): List<String>

    @Update
    suspend fun update(collectInfo: PlayRecordSongInfo)

    @Delete
    suspend fun delete(vararg item: PlayRecordSongInfo)

    @Query("SELECT COUNT(*) FROM collect_songInfo")
    suspend fun getTotalCount(): Int
}