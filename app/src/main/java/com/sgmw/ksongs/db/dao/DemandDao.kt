package com.sgmw.ksongs.db.dao

import androidx.lifecycle.LiveData
import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Update
import androidx.room.OnConflictStrategy
import com.sgmw.ksongs.db.entity.DemandSongInfo

@Dao
interface DemandDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertDemandSongInfo(vararg item: DemandSongInfo)

    @Update
    suspend fun update(items: List<DemandSongInfo>)

    @Delete
    suspend fun deleteDemandSongInfo(vararg item: DemandSongInfo?)

    @Query("DELETE FROM demand_songInfo WHERE song_id = :songId")
    suspend fun deleteDemandSongInfo(songId: String?)

    @Query("DELETE FROM demand_songInfo WHERE isPlaying = 0")
    suspend fun deleteAllDemandSongInfo()

    @Query("SELECT * FROM demand_songInfo ORDER BY insert_time")
    suspend fun getAllDemandSongInfo(): MutableList<DemandSongInfo>

    @Query("SELECT * FROM demand_songInfo WHERE isPlaying = 0 ORDER BY insert_time")
    suspend fun getAllDemandSongInfoWithOutPlaying(): MutableList<DemandSongInfo>

    @Query("SELECT * FROM demand_songInfo WHERE isPlaying = 1")
    fun getPlayingSongInfoLiveData(): LiveData<DemandSongInfo?>

    @Query("SELECT * FROM demand_songInfo WHERE isPlaying = 1")
    suspend fun getPlayingSongInfo(): DemandSongInfo?

    suspend fun getDemandListSize(): Int {
        return getAllDemandSongInfo().size - 1
    }

    @Query("SELECT * FROM demand_songInfo WHERE isPlaying = 0 ORDER BY insert_time")
    fun getAllDemandSongInfoLiveData(): LiveData<MutableList<DemandSongInfo>>

    /**
     * 查询全部的已点歌曲信息，包含正在播放的歌曲
     */
    @Query("SELECT * FROM demand_songInfo ORDER BY insert_time")
    fun getAllDemandSongInfoWithPlayingLiveData(): LiveData<MutableList<DemandSongInfo>>

    @Query("SELECT EXISTS(SELECT 1 FROM demand_songInfo WHERE song_id = :songId)")
    suspend fun isSongIdExists(songId: String): Boolean

    @Query("SELECT * FROM demand_songInfo WHERE song_id = :songId")
    suspend fun getSongInfoById(songId: String): DemandSongInfo?

    @Query("SELECT * FROM demand_songInfo WHERE isPlaying = 0 ORDER BY insert_time LIMIT 1")
    suspend fun getTopDemandSongInfo(): DemandSongInfo?

}