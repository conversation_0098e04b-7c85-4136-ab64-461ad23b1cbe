package com.sgmw.ksongs.db.dao

import androidx.lifecycle.LiveData
import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.sgmw.ksongs.db.entity.CollectSongInfo

/**
 * 操作收藏表的Dao
 */
@Dao
interface CollectDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(collectInfo: CollectSongInfo)

    @Query("SELECT * FROM collect_songInfo WHERE song_id = :songId")
    suspend fun find(songId: String): CollectSongInfo?

    @Query("SELECT * FROM collect_songInfo ORDER BY insert_time DESC")
    fun findAll(): LiveData<List<CollectSongInfo>>

    @Query("SELECT song_id FROM collect_songInfo")
    suspend fun findCollectedSongIds(): List<String>

    @Update
    suspend fun update(collectInfo: CollectSongInfo)

    @Delete
    suspend fun delete(items: List<CollectSongInfo>)

    @Query("DELETE FROM collect_songInfo WHERE song_id = :songId")
    suspend fun delete(songId: String)

    @Query("SELECT COUNT(*) FROM collect_songInfo")
    suspend fun getTotalCount(): Int

    @Query("SELECT COUNT(*) FROM collect_songInfo")
    fun getCount(): LiveData<Int>


}