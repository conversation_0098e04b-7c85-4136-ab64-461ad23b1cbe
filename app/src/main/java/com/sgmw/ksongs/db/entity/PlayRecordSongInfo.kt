package com.sgmw.ksongs.db.entity

import androidx.room.ColumnInfo
import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import androidx.room.RoomWarnings
import com.sgmw.ksongs.model.bean.SongInfoBean
/**
 * 收藏对应的音乐信息实体映射表
 */
@Entity(tableName = "play_record_songInfo")
data class PlayRecordSongInfo(
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "record_id")
    val collectId: Int = 0,
    @ColumnInfo(name = "insert_time")
    var insertTime: Long = System.currentTimeMillis(),
    @SuppressWarnings(RoomWarnings.PRIMARY_KEY_FROM_EMBEDDED_IS_DROPPED)
    @Embedded
    val songInfo: SongInfoBean
) {

    /**
     * 编辑态下是否被选中，默认false
     */
    @Ignore
    var isSelect: Boolean = false

}