package com.sgmw.ksongs.db.entity

import androidx.room.ColumnInfo
import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import com.sgmw.ksongs.model.bean.SongInfoBean

/**
 * 已点列表对应的音乐信息实体映射表
 * 优化版本：添加song_id唯一索引，防止重复数据
 */
@Entity(
    tableName = "demand_songInfo",
    indices = [Index(value = ["song_id"], unique = true)]
)
data class DemandSongInfo(
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "demand_id")
    var demandId: Long = 0L,
    @ColumnInfo(name = "insert_time")
    var insertTime: Long = System.currentTimeMillis(),
    @Embedded
    val songInfo: SongInfoBean
)