package com.sgmw.ksongs.config

import android.content.Context
import com.bumptech.glide.Glide
import com.bumptech.glide.GlideBuilder
import com.bumptech.glide.Registry
import com.bumptech.glide.annotation.GlideModule
import com.bumptech.glide.load.DecodeFormat
import com.bumptech.glide.load.engine.cache.InternalCacheDiskCacheFactory
import com.bumptech.glide.load.engine.cache.LruResourceCache
import com.bumptech.glide.module.AppGlideModule
import com.bumptech.glide.request.RequestOptions
import com.sgmw.common.config.MemoryOptimizationConfig

/**
 * Glide配置模块
 * 应用内存优化配置到Glide
 */
@GlideModule
class GlideAppModule : AppGlideModule() {

    override fun applyOptions(context: Context, builder: GlideBuilder) {
        // 应用内存缓存配置
        val memoryCacheSizeBytes = MemoryOptimizationConfig.GlideConfig.MEMORY_CACHE_SIZE_MB * 1024 * 1024
        builder.setMemoryCache(LruResourceCache(memoryCacheSizeBytes.toLong()))

        // 应用磁盘缓存配置
        val diskCacheSizeBytes = MemoryOptimizationConfig.GlideConfig.DISK_CACHE_SIZE_MB * 1024 * 1024
        builder.setDiskCache(InternalCacheDiskCacheFactory(context, diskCacheSizeBytes.toLong()))

        // 设置默认请求选项
        val defaultRequestOptions = RequestOptions()
            .format(DecodeFormat.PREFER_RGB_565) // 使用RGB_565格式减少内存占用
            .disallowHardwareConfig() // 禁用硬件位图以避免某些设备上的问题

        builder.setDefaultRequestOptions(defaultRequestOptions)
    }

    override fun registerComponents(context: Context, glide: Glide, registry: Registry) {
        // 可以在这里注册自定义组件
    }

    override fun isManifestParsingEnabled(): Boolean {
        // 禁用清单解析以提高性能
        return false
    }
}
