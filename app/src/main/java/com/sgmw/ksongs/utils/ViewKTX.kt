package com.sgmw.ksongs.utils

import android.view.View
import androidx.viewpager.widget.ViewPager
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import kotlin.math.abs


fun View.gone(isGone: Boolean = true) {
    visibility = if (isGone) {
        View.GONE
    } else {
        View.VISIBLE
    }
}

fun View.invisible(isInVisible: Boolean = true) {
    visibility = if (isInVisible) {
        View.INVISIBLE
    } else {
        View.VISIBLE
    }
}

fun View.visibleOrGone(isVisible: Boolean = true) {
    visibility = if (isVisible) {
        View.VISIBLE
    } else {
        View.GONE
    }
}

fun View.visibleOrNot(isVisible: Boolean = true) {
    visibility = if (isVisible) {
        View.VISIBLE
    } else {
        View.INVISIBLE
    }
}

fun TabLayout.setupTabClickEvents(viewPager: ViewPager2) {
    (0 until tabCount).forEach { tabPosition ->
        getTabAt(tabPosition)?.view?.setOnClickListener {
            viewPager.setCurrentItem(tabPosition, false)
        }
    }
}

fun TabLayout.setupTabClickEvents(viewPager: ViewPager) {
    (0 until tabCount).forEach { tabPosition ->
        getTabAt(tabPosition)?.view?.setOnClickListener {
            viewPager.setCurrentItem(tabPosition, false)
        }
    }
}