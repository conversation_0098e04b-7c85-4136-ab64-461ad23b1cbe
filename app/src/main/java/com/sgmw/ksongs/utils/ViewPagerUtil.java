package com.sgmw.ksongs.utils;

import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;

import com.sgmw.ksongs.utils.viewpager.ReflectLayoutManager;


public class ViewPagerUtil {

    public static void setOverScrollMode(ViewPager2 viewPager2, int overScrollMode) {
        if (viewPager2 == null) {
            return;
        }
        RecyclerView recyclerView = (RecyclerView) viewPager2.getChildAt(0);
        if (recyclerView != null) {
            recyclerView.setOverScrollMode(overScrollMode);
        }
    }

    /**
     * 设置ViewPager滚动动画时长
     */
    public static void setScrollAnimDuration(ViewPager2 viewPager) {
        if (viewPager == null) {
            return;
        }
        ReflectLayoutManager.reflectLayoutManager(viewPager, 300);
    }
}
