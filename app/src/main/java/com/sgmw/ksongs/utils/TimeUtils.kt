package com.sgmw.ksongs.utils

/**
 * @author: 董俊帅
 * @time: 2025/2/28
 * @desc: 时间转换工具类
 */
object TimeUtils {

    /**
     * 把毫秒转换为分秒格式
     * 例如: 03:22
     */
    fun formatMillisecondsWithRound(millis: Long): String {
        val totalSeconds = (millis + 500) / 1000 // 四舍五入
        val minutes = (totalSeconds / 60).toInt()
        val seconds = (totalSeconds % 60).toInt()
        return "%02d:%02d".format(minutes, seconds)
    }

}