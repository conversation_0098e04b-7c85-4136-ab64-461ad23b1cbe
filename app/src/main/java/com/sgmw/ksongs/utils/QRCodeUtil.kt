package com.sgmw.ksongs.utils

import android.graphics.Bitmap
import com.google.zxing.BarcodeFormat
import com.google.zxing.EncodeHintType
import com.google.zxing.WriterException
import com.google.zxing.common.BitMatrix
import com.google.zxing.qrcode.QRCodeWriter

object QRCodeUtil {
    /**
     * 生成二维码Bitmap
     *
     * @param text 要生成二维码的字符串
     * @param widthAndHeight 二维码的宽高
     * @return 生成的二维码Bitmap
     */
    fun generateQRCode(text: String, widthAndHeight: Int): Bitmap? {
        val qrCodeWriter = QRCodeWriter()
        return try {
            val bitMatrix: BitMatrix = qrCodeWriter.encode(text, BarcodeFormat.QR_CODE, widthAndHeight, widthAndHeight)
            val width = bitMatrix.width
            val height = bitMatrix.height
            val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.RGB_565)
            for (x in 0 until width) {
                for (y in 0 until height) {
                    bitmap.setPixel(x, y, if (bitMatrix[x, y]) 0xFF000000.toInt() else 0xFFFFFFFF.toInt())
                }
            }
            bitmap
        } catch (e: WriterException) {
            e.printStackTrace()
            null
        }
    }

    /**
     * 生成二维码Bitmap并去除白边
     *
     * @param text 要生成二维码的字符串
     * @param widthAndHeight 二维码的宽高
     * @return 生成的二维码Bitmap
     */
    fun generateQRCodeNew1(text: String, widthAndHeight: Int): Bitmap? {
        val qrCodeWriter = QRCodeWriter()
        val hints = mapOf(EncodeHintType.MARGIN to 0) // 设置边距为0
        return try {
            val bitMatrix: BitMatrix = qrCodeWriter.encode(
                text,
                BarcodeFormat.QR_CODE,
                widthAndHeight,
                widthAndHeight,
                hints
            )
            createBitmap(bitMatrix)
        } catch (e: WriterException) {
            e.printStackTrace()
            null
        }
    }

    /**
     * 生成二维码Bitmap并去除白边
     *
     * @param text 要生成二维码的字符串
     * @param widthAndHeight 二维码的宽高
     * @return 生成的二维码Bitmap
     */
    fun generateQRCodeNew(text: String, widthAndHeight: Int): Bitmap? {
        val qrCodeWriter = QRCodeWriter()
        val hints = mapOf(EncodeHintType.MARGIN to 0) // 设置边距为0
        return try {
            val bitMatrix: BitMatrix = qrCodeWriter.encode(
                text,
                BarcodeFormat.QR_CODE,
                widthAndHeight,
                widthAndHeight,
                hints
            )
            val croppedMatrix = removeWhiteBorder(bitMatrix)
            createBitmap(croppedMatrix)
        } catch (e: WriterException) {
            e.printStackTrace()
            null
        }
    }

    /**
     * 去除BitMatrix中的白边
     */
    private fun removeWhiteBorder(matrix: BitMatrix): BitMatrix {
        val width = matrix.width
        val height = matrix.height
        var left = width
        var right = 0
        var top = height
        var bottom = 0

        for (y in 0 until height) {
            for (x in 0 until width) {
                if (matrix[x, y]) {
                    if (x < left) left = x
                    if (x > right) right = x
                    if (y < top) top = y
                    if (y > bottom) bottom = y
                }
            }
        }
        val croppedWidth = right - left + 1
        val croppedHeight = bottom - top + 1
        return BitMatrix(croppedWidth, croppedHeight).apply {
            for (y in 0 until croppedHeight) {
                for (x in 0 until croppedWidth) {
                    if (matrix[left + x, top + y]) {
                        set(x, y)
                    }
                }
            }
        }
    }

    /**
     * 将BitMatrix转换为Bitmap
     */
    private fun createBitmap(matrix: BitMatrix): Bitmap {
        val width = matrix.width
        val height = matrix.height
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.RGB_565)
        for (x in 0 until width) {
            for (y in 0 until height) {
                bitmap.setPixel(x, y, if (matrix[x, y]) 0xFF000000.toInt() else 0xFFFFFFFF.toInt())
            }
        }
        return bitmap
    }
}
