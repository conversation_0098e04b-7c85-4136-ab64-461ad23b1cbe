package com.sgmw.ksongs.utils

import android.os.Bundle
import androidx.navigation.NavController
import androidx.navigation.NavDirections
import androidx.navigation.NavOptions
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.R
import java.lang.ref.WeakReference

/**
 * @author: 董俊帅
 * @time: 2025/5/15
 * @desc: Navigation跳转工具类 解决Navigation跳转时的异常问题
 * 对应Jira - http://10.5.1.52:8080/jira/browse/F710SSW-612
 */
object NavigationUtils {

    private const val TAG = "NavigationUtils"

    private var  navControllerRef: WeakReference<NavController>? = null

    fun setNavController(navController: NavController?){
        navControllerRef = WeakReference(navController)
    }
    fun getNavController():NavController?{
        return navControllerRef?.get()
    }
    fun clearNavController(){
        navControllerRef?.clear()
    }
    /**
     * 安全导航到目标页面
     * @param navController 导航控制器
     * @param actionId 导航动作ID
     */
    fun navigateSafely(navController: NavController, actionId: Int) {
        try {
            // 检查导航控制器状态，避免在无效状态下导航
            if (navController.currentDestination == null) {
                Log.w(TAG, "navigateSafely: NavController has no current destination")
                return
            }

            // 使用主线程执行导航，避免线程问题
            if (android.os.Looper.myLooper() == android.os.Looper.getMainLooper()) {
                navController.navigate(actionId)
            } else {
                android.os.Handler(android.os.Looper.getMainLooper()).post {
                    try {
                        navController.navigate(actionId)
                    } catch (e: Exception) {
                        Log.e(TAG, "Navigation failed in main thread: actionId: $actionId", e)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Navigation failed: actionId: $actionId cause: ${e.cause}")
        }
    }

    /**
     * 安全导航到目标页面（使用NavDirections）
     * @param navController 导航控制器
     * @param directions 导航方向
     */
    fun navigateSafely(navController: NavController, directions: NavDirections) {
        try {
            navController.navigate(directions)
        } catch (e: Exception) {
            Log.e(TAG, "Navigation failed: directions: $directions message: ${e.message}")
        }
    }

    /**
     * 安全导航到目标页面（带参数）
     * @param navController 导航控制器
     * @param actionId 导航动作ID
     * @param bundle 导航参数
     */
    fun navigateSafely(navController: NavController, actionId: Int, bundle: Bundle) {
        try {
            navController.navigate(actionId, bundle)
        } catch (e: Exception) {
            Log.e(TAG, "Navigation failed: actionId: $actionId message: ${e.message}")
        }
    }

    /**
     * 安全导航到目标页面（带导航选项）
     * @param navController 导航控制器
     * @param actionId 导航动作ID
     * @param navOptions 导航选项
     */
    fun navigateSafely(navController: NavController, actionId: Int, navOptions: NavOptions) {
        try {
            navController.navigate(actionId, null, navOptions)
        } catch (e: Exception) {
            Log.e(TAG, "Navigation failed: actionId: $actionId ${e.message}")
        }
    }

    /**
     * 当前页面是否是登录页面
     */
    fun isCurrentLoginFragment(): Boolean {
        val navController = getNavController() ?: return false
        val currentDestination = navController.currentDestination ?: return false
        return currentDestination.id == R.id.navigation_login
    }

    /**
     * 当前页面是否是VIP支付页面
     */
    fun isCurrentVipPaymentFragment(): Boolean {
        val navController = getNavController() ?: return false
        val currentDestination = navController.currentDestination ?: return false
        return currentDestination.id == R.id.navigation_vip_payment
    }

    /**
     * 安全执行popBackStack操作
     * @param navController 导航控制器
     * @return 是否成功执行
     */
    fun popBackStackSafely(navController: NavController): Boolean {
        return try {
            // 检查是否可以返回
            if (navController.previousBackStackEntry != null) {
                navController.popBackStack()
                true
            } else {
                Log.w(TAG, "popBackStackSafely: No previous back stack entry")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "popBackStackSafely failed: ${e.message}")
            false
        }
    }

    /**
     * 检查导航控制器是否处于有效状态
     */
    fun isNavControllerValid(): Boolean {
        val navController = getNavController()
        return navController != null && navController.currentDestination != null
    }

    /**
     * 获取当前Fragment的标签名称（用于调试）
     */
    fun getCurrentFragmentTag(): String? {
        val navController = getNavController() ?: return null
        val currentDestination = navController.currentDestination ?: return null
        return when (currentDestination.id) {
            R.id.navigation_vip_payment -> "VipPaymentFragment"
            R.id.navigation_login -> "LoginFragment"
            else -> "Unknown_${currentDestination.id}"
        }
    }

}