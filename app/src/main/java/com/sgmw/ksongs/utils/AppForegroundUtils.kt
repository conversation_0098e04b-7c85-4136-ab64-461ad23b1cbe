package com.sgmw.ksongs.utils

import android.app.Activity
import android.app.Application
import android.os.Bundle
import com.sgmw.common.utils.Log

/**
 * @author: 董俊帅
 * @time: 2025/7/10
 * @desc: 应用前后台状态管理工具类
 */
object AppForegroundUtils {

    private const val TAG = "AppForegroundUtils"

    // 应用是否在前台
    private var isAppInForeground = false

    // 前台Activity计数
    private var foregroundActivityCount = 0

    /**
     * 初始化，在Application中调用
     */
    fun init(application: Application) {
        application.registerActivityLifecycleCallbacks(object : Application.ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {}

            override fun onActivityStarted(activity: Activity) {
                foregroundActivityCount++
                if (foregroundActivityCount == 1) {
                    // 从后台切换到前台
                    isAppInForeground = true
                    Log.d(TAG, "应用切换到前台")
                }
            }

            override fun onActivityResumed(activity: Activity) {
                // Activity可见且可交互
                Log.d(TAG, "${activity.javaClass.simpleName} 已恢复")
            }

            override fun onActivityPaused(activity: Activity) {
                // Activity失去焦点，但仍可见
                Log.d(TAG, "${activity.javaClass.simpleName} 已暂停")
            }

            override fun onActivityStopped(activity: Activity) {
                foregroundActivityCount--
                if (foregroundActivityCount == 0) {
                    // 从前台切换到后台
                    isAppInForeground = false
                    Log.d(TAG, "应用切换到后台")
                }
            }

            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}

            override fun onActivityDestroyed(activity: Activity) {}
        })
    }

    /**
     * 判断应用是否在前台
     */
    fun isAppInForeground(): Boolean {
        Log.d(TAG, "isAppInForeground: $isAppInForeground")
        return isAppInForeground
    }
}
