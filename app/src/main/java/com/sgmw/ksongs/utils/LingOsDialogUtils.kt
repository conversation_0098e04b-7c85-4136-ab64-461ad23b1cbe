package com.sgmw.ksongs.utils

import android.car.bus.SGMWBus
import android.car.bus.SGMWBusEvent
import android.car.bus.SGMWBusEventType
import android.content.Context
import android.database.ContentObserver
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import com.sgmw.common.BaseApplication
import com.sgmw.common.utils.Log
import java.lang.ref.WeakReference


/**
 * 启动时展示 系统欢迎页面弹框工具类
 */
object LingOsDialogUtils {
    const val TAG = "LingOsDialogUtils"
    const val EVENT_LINGOS_SHOW = "WINDOW/LingOS"
    const val EVENT_CLOSE_WELCOME_PAGE = "launcher/closeWelcomePage"
    const val LINGOS_AGREEMENT = "lingos_agree_agreement"
    const val ZERO = 0
    const val ONE = 1

    // 使用WeakReference避免内存泄露，不持有强引用
    private var sAgreementListenerRef: WeakReference<IAgreementListener>? = null
    private var settingsObserver: SettingsObserver? = null

    interface IAgreementListener {
        fun onAgreeState(agree: Boolean)
    }

    fun showPrivacyAgreement(context: Context, listener: IAgreementListener?) {
        val isAgree = Settings.Global.getInt(
            context.contentResolver,
            LINGOS_AGREEMENT,
            LingOsDialogUtils.ZERO
        ) == LingOsDialogUtils.ONE
        Log.d(LingOsDialogUtils.TAG, "showPrivacyAgreement: isAgree = $isAgree")
        if (isAgree) {
            listener?.onAgreeState(true)
            return
        }
        // 使用WeakReference避免内存泄露
        sAgreementListenerRef = listener?.let { WeakReference(it) }
        subscribeAgreeEvent(context)
        showWelcomeDialog()
    }

    //展示弹窗 - 使用BaseApplication.context避免内存泄露
    private fun showWelcomeDialog() {
        try {
            val sGMWBus = SGMWBus(BaseApplication.context)
            val sgmwBusEvent = SGMWBusEvent()
            sgmwBusEvent.mEventType = SGMWBusEventType.EVENT_LINGOS_SHOW
            sGMWBus.publish(sgmwBusEvent)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to show welcome dialog", e)
        }
    }


    private class SettingsObserver(handler: Handler?) : ContentObserver(handler) {
        override fun onChange(selfChange: Boolean, uri: Uri?) {
            super.onChange(selfChange, uri)
            val isAgree = Settings.Global.getInt(
                 BaseApplication.context.contentResolver,
                LINGOS_AGREEMENT,
                ZERO
            ) == ONE
            Log.d(TAG, "SettingsObserver onChange:$isAgree")
            // 使用WeakReference获取Listener，避免内存泄露
            sAgreementListenerRef?.get()?.let {
                it.onAgreeState(isAgree)
            }
            if (isAgree) {
                // 清理所有引用，防止内存泄露
                cleanup()
            }
        }
    }

    private fun subscribeAgreeEvent(context: Context) {
        try {
            // 先清理之前的Observer
            cleanup()

            // 创建新的Observer，使用ApplicationContext避免内存泄露
            settingsObserver = SettingsObserver(Handler(Looper.getMainLooper()))
            settingsObserver?.let { observer ->
                val specificUri = Settings.Global.getUriFor(LINGOS_AGREEMENT)
                BaseApplication.context.contentResolver.registerContentObserver(
                    specificUri,  // 监听的URI
                    false,  // 是否监听子URI（通常设为false）
                    observer
                )
                Log.d(TAG, "ContentObserver registered successfully")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to subscribe agree event", e)
        }
    }

    /**
     * 清理所有引用，防止内存泄露
     */
    private fun cleanup() {
        try {
            // 取消注册ContentObserver
            settingsObserver?.let { observer ->
                BaseApplication.context.contentResolver.unregisterContentObserver(observer)
                Log.d(TAG, "ContentObserver unregistered successfully")
            }
            settingsObserver = null

            // 清理Listener引用
            sAgreementListenerRef = null

            Log.d(TAG, "LingOsDialogUtils cleanup completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error during LingOsDialogUtils cleanup", e)
        }
    }
}