package com.sgmw.ksongs.utils

import android.content.Context
import android.view.View
import android.view.inputmethod.InputMethodManager

object InputMethodUtils {

    fun hideInputMethod(context: Context?, view: View?): Boolean {
        if (context == null || view == null) {
            return false
        }
        val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        return imm?.hideSoftInputFromWindow(view.windowToken, 0) ?: false
    }

    fun showInputMethod(context: Context?, view: View?): Boolean {
        if (context == null || view == null) {
            return false
        }
        view.requestFocus()
        val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        return imm?.showSoftInput(view, InputMethodManager.SHOW_FORCED)
            ?: false
    }

}