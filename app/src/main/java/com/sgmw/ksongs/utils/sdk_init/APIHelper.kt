package com.sgmw.ksongs.utils.sdk_init

import com.sgmw.common.utils.Log
import com.sgmw.ksongs.BuildConfig
import java.math.BigInteger
import java.security.MessageDigest

object APIHelper {

    /**
     * 获取签名
     * 应用接口请求签名，计算规则如下:
     * sign = md5(KG_APPID_TS_SECRET)
     * 比如, appID为10001, 时间戳为1675748252，secret为xxxabc
     * 那么要加密的字符串为 KG_10001_1675748252_xxxabc
     * 再对字符串进行md5计算得到签名(32位小写)
     * https://apifox.com/apidoc/project-951819/api-145891419
     */
    fun sign(): String{
        val kg = "KG"
        val appid = BuildConfig.KTV_SDK_APP_ID
        val ts = System.currentTimeMillis() / 1000
        val secret = BuildConfig.KTV_SDK_TEST_APP_KEY
        val sign = kg + "_" + appid + "_" + ts + "_" + secret
        Log.d("APIHelper", "kg: $kg appid: $appid ts: $ts secret $secret")
        Log.d("APIHelper", "sign: $sign")
        val md5String = md5(sign)
        return md5String
    }

    private fun md5(input: String): String {
        val md = MessageDigest.getInstance("MD5")
        val digest = md.digest(input.toByteArray())
        return BigInteger(1, digest).toString(16).padStart(32, '0')
    }

}