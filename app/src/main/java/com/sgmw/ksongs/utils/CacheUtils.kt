package com.sgmw.ksongs.utils

import java.io.File

object CacheUtils {

    fun byteToFileSize(byteLength: Long): String {
        val kb = byteLength / 1024
        val mb = kb / 1024
        val gb = mb / 1024
        if (gb < 1) { // 最小单位按 MB 显示
            return mb.toString() + "MB"
        }
        val tb = gb / 1024
        if (tb < 1) {
            return gb.toString() + "GB"
        }
        return tb.toString() + "TB"
    }

    fun getFolderSize(file: File?): Long {
        var size: Long = 0
        if (file != null) {
            val fileList = file.listFiles()
            if (fileList != null) {
                for (i in fileList.indices) {
                    size += if (fileList[i].isDirectory) {
                        getFolderSize(fileList[i])
                    } else {
                        fileList[i].length()
                    }
                }
            }
        }
        return size
    }

    fun deleteDir(dir: File): Boolean {
        if (dir.isDirectory) {
            val children = dir.list()
            for (i in 0 until (children?.size ?: 0)) {
                val success = deleteDir(File(dir, children!![i]))
                if (!success) {
                    return false
                }
            }
        }
        return dir.delete()
    }

}