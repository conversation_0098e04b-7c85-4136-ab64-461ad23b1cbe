package com.sgmw.ksongs.utils

import com.sgmw.common.utils.DaemonThreadDispatcher
import com.sgmw.common.utils.Log
import com.sgmw.common.utils.ioLaunch
import com.sgmw.ksongs.db.DbManager
import com.sgmw.ksongs.ui.playlist.PlayListManager
import com.sgmw.ksongs.ui.playlist.SungListManager
import kotlinx.coroutines.withContext

/**
 * 数据一致性检查工具
 * 用于检查和修复已点列表和已唱列表之间的数据一致性问题
 * 
 * @author: AI Assistant
 * @time: 2025/8/5
 * @desc: 数据一致性检查和修复工具
 */
object DataConsistencyChecker {

    private const val TAG = "DataConsistencyChecker"

    /**
     * 检查数据一致性报告
     */
    data class ConsistencyReport(
        val duplicateSongs: List<String> = emptyList(), // 同时存在于已点和已唱列表的歌曲
        val multiplePlayingSongs: List<String> = emptyList(), // 多个正在播放的歌曲
        val invalidSongs: List<String> = emptyList(), // 无效的歌曲数据
        val isConsistent: Boolean = duplicateSongs.isEmpty() && multiplePlayingSongs.isEmpty() && invalidSongs.isEmpty()
    ) {
        fun getReportSummary(): String {
            return buildString {
                appendLine("=== 数据一致性检查报告 ===")
                appendLine("状态: ${if (isConsistent) "一致" else "存在问题"}")
                if (duplicateSongs.isNotEmpty()) {
                    appendLine("重复歌曲 (${duplicateSongs.size}首): ${duplicateSongs.joinToString(", ")}")
                }
                if (multiplePlayingSongs.isNotEmpty()) {
                    appendLine("多个正在播放歌曲 (${multiplePlayingSongs.size}首): ${multiplePlayingSongs.joinToString(", ")}")
                }
                if (invalidSongs.isNotEmpty()) {
                    appendLine("无效歌曲数据 (${invalidSongs.size}首): ${invalidSongs.joinToString(", ")}")
                }
                appendLine("========================")
            }
        }
    }

    /**
     * 执行完整的数据一致性检查
     * @param autoRepair 是否自动修复发现的问题
     * @return 检查报告
     */
    suspend fun checkDataConsistency(autoRepair: Boolean = false): ConsistencyReport {
        return try {
            val report = performConsistencyCheck()

            Log.i(TAG, report.getReportSummary())

            if (!report.isConsistent && autoRepair) {
                Log.i(TAG, "开始自动修复数据一致性问题...")
                repairDataConsistency(report)

                // 修复后重新检查
                val afterRepairReport = performConsistencyCheck()
                Log.i(TAG, "修复后检查结果:")
                Log.i(TAG, afterRepairReport.getReportSummary())
                afterRepairReport
            } else {
                report
            }
        } catch (e: Exception) {
            Log.e(TAG, "数据一致性检查失败: ${e.message}", e)
            ConsistencyReport() // 返回空报告
        }
    }

    /**
     * 执行数据一致性检查的核心逻辑
     */
    private suspend fun performConsistencyCheck(): ConsistencyReport {
        return withContext(DaemonThreadDispatcher.Database) {
            val duplicateSongs = mutableListOf<String>()
            val multiplePlayingSongs = mutableListOf<String>()
            val invalidSongs = mutableListOf<String>()

            try {
                // 1. 检查重复歌曲（同时存在于已点和已唱列表）
                val demandSongs = PlayListManager.getDemandSongInfoList()
                val sungSongs = SungListManager.getSungListLiveData().value ?: emptyList()

                demandSongs.forEach { demandSong ->
                    val songId = demandSong.songInfo.song_id
                    val songName = demandSong.songInfo.song_name
                    
                    // 检查数据有效性
                    if (songName.isBlank() || demandSong.songInfo.singer_name.isBlank()) {
                        invalidSongs.add("$songName (ID: $songId)")
                    }
                    
                    // 检查是否在已唱列表中
                    if (sungSongs.any { it.song_id == songId }) {
                        duplicateSongs.add("$songName (ID: $songId)")
                    }
                }

                // 2. 检查多个正在播放的歌曲
                val playingSongs = demandSongs.filter { it.songInfo.isPlaying }
                if (playingSongs.size > 1) {
                    playingSongs.forEach { song ->
                        multiplePlayingSongs.add("${song.songInfo.song_name} (ID: ${song.songInfo.song_id})")
                    }
                }

                // 3. 检查已唱列表中的无效数据
                sungSongs.forEach { sungSong ->
                    if (sungSong.song_name.isBlank() || sungSong.singer_name.isBlank()) {
                        invalidSongs.add("${sungSong.song_name} (ID: ${sungSong.song_id})")
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "执行数据一致性检查时出错: ${e.message}", e)
            }

            ConsistencyReport(
                duplicateSongs = duplicateSongs,
                multiplePlayingSongs = multiplePlayingSongs,
                invalidSongs = invalidSongs
            )
        }
    }

    /**
     * 修复数据一致性问题
     */
    private suspend fun repairDataConsistency(report: ConsistencyReport) {
        withContext(DaemonThreadDispatcher.Database) {
            try {
                DbManager.runInTransaction {
                    val demandDao = DbManager.getDemandSongInfoDao()

                    // 1. 修复重复歌曲问题
                    report.duplicateSongs.forEach { duplicateSongInfo ->
                        val songId = extractSongId(duplicateSongInfo)
                        if (songId.isNotEmpty()) {
                            // 从已点列表删除重复的歌曲
                            demandDao.deleteDemandSongInfo(songId)
                            Log.d(TAG, "修复重复歌曲: 从已点列表删除 $duplicateSongInfo")
                        }
                    }

                    // 2. 修复多个正在播放歌曲的问题
                    if (report.multiplePlayingSongs.isNotEmpty()) {
                        val allDemandSongs = demandDao.getAllDemandSongInfo()
                        val playingSongs = allDemandSongs.filter { it.songInfo.isPlaying }
                        
                        if (playingSongs.size > 1) {
                            // 保留最新的一个，其他的设为非播放状态
                            val latestPlayingSong = playingSongs.maxByOrNull { it.insertTime }
                            playingSongs.forEach { song ->
                                if (song.demandId != latestPlayingSong?.demandId) {
                                    song.songInfo.isPlaying = false
                                    demandDao.insertDemandSongInfo(song)
                                    Log.d(TAG, "修复多播放状态: 设置 ${song.songInfo.song_name} 为非播放状态")
                                }
                            }
                        }
                    }

                    // 3. 清理无效数据
                    report.invalidSongs.forEach { invalidSongInfo ->
                        val songId = extractSongId(invalidSongInfo)
                        if (songId.isNotEmpty()) {
                            demandDao.deleteDemandSongInfo(songId)
                            Log.d(TAG, "清理无效数据: 删除 $invalidSongInfo")
                        }
                    }
                }
                
                Log.i(TAG, "数据一致性修复完成")
            } catch (e: Exception) {
                Log.e(TAG, "修复数据一致性时出错: ${e.message}", e)
            }
        }
    }

    /**
     * 从歌曲信息字符串中提取歌曲ID
     */
    private fun extractSongId(songInfo: String): String {
        val regex = "ID: ([^)]+)".toRegex()
        return regex.find(songInfo)?.groupValues?.get(1) ?: ""
    }

    /**
     * 定期检查数据一致性（可在应用启动时调用）
     */
    fun schedulePeriodicCheck() {
        Log.d(TAG, "启动定期数据一致性检查")
        ioLaunch {
            checkDataConsistency(autoRepair = true)
        }
    }

    /**
     * 手动触发数据一致性检查和修复
     */
    fun manualCheckAndRepair() {
        Log.i(TAG, "手动触发数据一致性检查和修复")
        ioLaunch {
            checkDataConsistency(autoRepair = true)
        }
    }
}
