package com.sgmw.ksongs.utils.sdk_init

import com.sgmw.common.utils.Log
import com.sgmw.ksongs.BuildConfig
import com.tme.ktv.api.KAppInfoAdapter
import com.tme.ktv.api.KtvSdk

/**
 * Created by jack<PERSON><PERSON> on 2023/6/25
 */
class SampleHostInfo : KAppInfoAdapter() {

    override fun getAppId(): String {
        return BuildConfig.KTV_SDK_APP_ID
    }

    override fun getAppKey(): String {
        Log.d(TAG, "KtvSdk.isProductEnv(): ${KtvSdk.isProductEnv()}")
//        if (KtvSdk.isProductEnv()) {
//            return BuildConfig.KTV_SDK_APP_KEY
//        } else {
//            return BuildConfig.KTV_SDK_TEST_APP_KEY
//        }
        return ""
    }

    /**
     *  平台标识
     *  车机：52
     *  智能音箱或其它：53
     * @return
     */
    override fun getPlatformId(): String {
        return "52"
    }

    /**
     * 设备唯一id
     *
     * @return
     */
    override fun getImei(): String {
        return "test"
    }

    /**
     * 用户id
     *
     * @return
     */
    override fun getUid(): String? {
        return null
    }

    /**
     * 渠道号，需改为真实的渠道号
     */
    override fun getChannelId(): String {
        return "wuling"
    }

    /**
     * 是否为开发调试模式，若是，sdk内部应用一些开发调试配置，有助于问题定位，但对sdk性能可能有影响。
     */
    override fun isDevMode(): Boolean {
        return BuildConfig.DEBUG
    }

    /**
     * app唯一版本标识
     * 可选
     * @return
     */
    override fun getQua(): String? {
        return null
    }

    /**
     * 宿主要给sdk的额外信息，默认为空
     */
    override fun getExtraInfo(): Map<String, String?> {
        return emptyMap()
    }

    companion object {
        private const val TAG = "SampleHostInfo"
    }
}