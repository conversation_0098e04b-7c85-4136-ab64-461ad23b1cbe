package com.sgmw.ksongs.utils

import androidx.annotation.IntDef
import java.io.Serializable
/**
 * 排行榜标题信息
 * @param id: 排行榜id， 具体参考：https://apifox.com/apidoc/project-951819/doc-1720163
 * @param title: 排行榜页面展示标题
 */
data class RankInfo(val id: String, val title: String) : Serializable

/**
 * 排行榜的歌单类型，其他type未使用到，详情见歌单说明
 * 歌单说明：https://docs.qq.com/sheet/DSkRDbW5iTVBEbkdT?tab=BB08J2
 */
//排行榜
const val PLAY_LIST_TYPE_TOP = "toplist"
//专题
const val PLAY_LIST_TYPE_THEME = "theme"

/**
 * 排行榜默认起始页index
 */
const val RANK_DEFAULT_INDEX = 0
/**
 * 首页排行榜接口请求大小
 */
const val RANK_HOME_PAGE_SIZE = 3
/**
 * 排行榜分页大小
 */
const val RANK_MAX_PAGE_SIZE = DEFAULT_PAGE_LIST_SIZE

val HotRank = RankInfo("1", "热门榜")
val ClassicRank = RankInfo("2", "经典榜")
val BSRank = RankInfo("3", "彪升榜")
val NewSongRank = RankInfo("4", "新歌榜")
val DYHotRank = RankInfo("5", "抖音热榜")

val DayRank = RankInfo("201", "点唱日榜")
val WeekRank = RankInfo("202", "点唱周榜")
val MonthRank = RankInfo("203", "点唱月榜")

val RANK60 = RankInfo("117", "60后")
val RANK70 = RankInfo("116", "70后")
val RANK80 = RankInfo("115", "80后")
val RANK90 = RankInfo("114", "90后")
val RANK00 = RankInfo("113", "00后")



/**
 * 默认排行榜
 */
const val RANK_TYPE_DEFAULT_LIST = 0

/**
 * 时代榜
 */
const val RANK_TYPE_AGE_LIST = 1

@IntDef(
    RANK_TYPE_DEFAULT_LIST,
    RANK_TYPE_AGE_LIST
)
@Retention(AnnotationRetention.SOURCE)
annotation class RankType{}

/**
 * 类型为：默认排行榜(RANK_TYPE_DEFAULT_LIST)时，可传入参数
 */
const val RANK_POSITION_DEFAULT = 0
const val RANK_POSITION_WEEK = 5

@IntDef(
    RANK_POSITION_DEFAULT,
    RANK_POSITION_WEEK
)
@Retention(AnnotationRetention.SOURCE)
annotation class RankTypeDefaultAvailablePosition{}

/**
 * 类型为：时代榜(RANK_TYPE_AGE_LIST)
 * 可出入类型
 */
const val RANK_POSITION_60 = 0
const val RANK_POSITION_70 = 1
const val RANK_POSITION_80 = 2
const val RANK_POSITION_90 = 3
const val RANK_POSITION_00 = 4
@IntDef(
    RANK_POSITION_60,
    RANK_POSITION_70,
    RANK_POSITION_80,
    RANK_POSITION_90,
    RANK_POSITION_00,
)
@Retention(AnnotationRetention.SOURCE)
annotation class RankTypeAgeAvailablePosition{}

/**
 * 获取要展示标题
 */
fun getRankInfoList(@RankType type: Int): List<RankInfo> {
    return when(type) {
        RANK_TYPE_AGE_LIST -> {
            listOf(RANK60, RANK70, RANK80, RANK90, RANK00)
        }
        else -> {
            listOf(HotRank, ClassicRank, BSRank, NewSongRank, DYHotRank,
                DayRank, WeekRank, MonthRank)
        }
    }
}


