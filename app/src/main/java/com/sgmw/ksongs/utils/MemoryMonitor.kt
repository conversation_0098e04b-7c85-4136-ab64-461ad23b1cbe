package com.sgmw.ksongs.utils

import android.app.ActivityManager
import android.content.Context
import android.os.Debug
import android.util.Log
import com.sgmw.common.BaseApplication
import com.sgmw.common.config.MemoryOptimizationConfig
import kotlinx.coroutines.*

/**
 * 内存监控工具类
 * 用于监控应用内存使用情况，预防OOM异常
 */
object MemoryMonitor {

    private const val TAG = "MemoryMonitor"
    // 使用配置文件中的参数
    private val MEMORY_WARNING_THRESHOLD = MemoryOptimizationConfig.MonitorConfig.MEMORY_WARNING_THRESHOLD
    private val MEMORY_CRITICAL_THRESHOLD = MemoryOptimizationConfig.MonitorConfig.MEMORY_CRITICAL_THRESHOLD
    private val MONITOR_INTERVAL = MemoryOptimizationConfig.MonitorConfig.MONITOR_INTERVAL_MS

    // 应用级别的协程作用域，用于管理所有内存相关的协程
    private var applicationScope: CoroutineScope? = null
    private var monitorJob: Job? = null
    private var isMonitoring = false

    // 添加节流控制，避免频繁的内存清理操作
    private var lastCleanupTime = 0L
    private var lastCriticalCleanupTime = 0L
    private var lastFinalizerCheckTime = 0L
    private const val CLEANUP_THROTTLE_INTERVAL = 30000L // 30秒内最多执行一次普通清理
    private const val CRITICAL_CLEANUP_THROTTLE_INTERVAL = 10000L // 10秒内最多执行一次关键清理
    private const val FINALIZER_CHECK_INTERVAL = 60000L // 60秒检查一次终结器队列
    
    /**
     * 初始化应用级别的协程作用域
     */
    private fun initApplicationScope() {
        if (applicationScope == null) {
            applicationScope = CoroutineScope(Dispatchers.Default + SupervisorJob())
            Log.d(TAG, "Application scope initialized for memory monitoring")
        }
    }

    /**
     * 开始内存监控
     */
    fun startMonitoring() {
        if (!MemoryOptimizationConfig.MonitorConfig.ENABLE_MEMORY_MONITORING) {
            Log.d(TAG, "Memory monitoring is disabled in config")
            return
        }

        if (isMonitoring) {
            Log.d(TAG, "Memory monitoring is already running")
            return
        }

        // 初始化应用级别的协程作用域
        initApplicationScope()

        isMonitoring = true
        monitorJob = applicationScope?.launch {
            while (isMonitoring) {
                try {
                    checkMemoryUsage()
                    delay(MONITOR_INTERVAL)
                } catch (e: Exception) {
                    Log.e(TAG, "Error during memory monitoring", e)
                }
            }
        }
        Log.d(TAG, "Memory monitoring started")
    }
    
    /**
     * 停止内存监控
     */
    fun stopMonitoring() {
        isMonitoring = false
        monitorJob?.cancel()
        monitorJob = null
        Log.d(TAG, "Memory monitoring stopped")
    }

    /**
     * 清理所有资源，在应用退出时调用
     */
    fun cleanup() {
        try {
            Log.d(TAG, "Cleaning up MemoryMonitor resources")
            stopMonitoring()

            // 取消应用级别的协程作用域
            applicationScope?.cancel()
            applicationScope = null

            Log.d(TAG, "MemoryMonitor cleanup completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error during MemoryMonitor cleanup", e)
        }
    }

    /**
     * 检查是否处于内存压力状态
     * 用于异常处理器中判断是否应该进行复杂操作
     */
    fun isMemoryPressureHigh(): Boolean {
        return try {
            val runtime = Runtime.getRuntime()
            val usedMemory = runtime.totalMemory() - runtime.freeMemory()
            val maxMemory = runtime.maxMemory()
            val memoryUsageRatio = usedMemory.toFloat() / maxMemory.toFloat()

            // 如果内存使用率超过警告阈值，认为处于内存压力状态
            memoryUsageRatio >= MEMORY_WARNING_THRESHOLD
        } catch (e: Exception) {
            // 如果检查内存状态时出现异常，保守地认为处于内存压力状态
            true
        }
    }
    
    /**
     * 检查内存使用情况
     */
    private fun checkMemoryUsage() {
        try {
            val context = BaseApplication.context
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val memoryInfo = ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memoryInfo)

            // 获取应用内存使用情况
            val runtime = Runtime.getRuntime()
            val usedMemory = runtime.totalMemory() - runtime.freeMemory()
            val maxMemory = runtime.maxMemory()
            val memoryUsageRatio = usedMemory.toFloat() / maxMemory.toFloat()

            // 获取详细内存信息
            val memoryInfo2 = Debug.MemoryInfo()
            Debug.getMemoryInfo(memoryInfo2)

            Log.d(TAG, "Memory usage: ${(memoryUsageRatio * 100).toInt()}% " +
                    "(${usedMemory / 1024 / 1024}MB / ${maxMemory / 1024 / 1024}MB)")
            Log.d(TAG, "Native heap: ${memoryInfo2.nativePrivateDirty}KB, " +
                    "Dalvik heap: ${memoryInfo2.dalvikPrivateDirty}KB")

            // 检查终结器队列状态
            checkFinalizerQueue()

            when {
                memoryUsageRatio >= MEMORY_CRITICAL_THRESHOLD -> {
                    Log.w(TAG, "Critical memory usage detected: ${(memoryUsageRatio * 100).toInt()}%")
                    performCriticalMemoryCleanup()
                }
                memoryUsageRatio >= MEMORY_WARNING_THRESHOLD -> {
                    Log.w(TAG, "High memory usage detected: ${(memoryUsageRatio * 100).toInt()}%")
                    performMemoryCleanup()
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error checking memory usage", e)
        }
    }
    
    /**
     * 执行内存清理（温和版本，添加节流控制）
     */
    private fun performMemoryCleanup() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastCleanupTime < CLEANUP_THROTTLE_INTERVAL) {
            Log.d(TAG, "Memory cleanup skipped due to throttling")
            return
        }

        try {
            Log.d(TAG, "Performing memory cleanup")
            lastCleanupTime = currentTime

            // 清理图片缓存 - 使用应用级别的协程作用域，必须在主线程执行
            applicationScope?.launch(Dispatchers.Main) {
                try {
                    com.bumptech.glide.Glide.get(BaseApplication.context).clearMemory()
                    Log.d(TAG, "Glide memory cache cleared successfully")
                } catch (e: Exception) {
                    Log.e(TAG, "Error clearing Glide memory cache", e)
                }
            }

            // 移除强制垃圾回收，让系统自然回收
            // System.gc() // 注释掉强制GC，避免触发FinalizerDaemon过载

            Log.d(TAG, "Memory cleanup completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error during memory cleanup", e)
        }
    }
    
    /**
     * 执行关键内存清理（优化版本，避免FinalizerDaemon过载）
     */
    private fun performCriticalMemoryCleanup() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastCriticalCleanupTime < CRITICAL_CLEANUP_THROTTLE_INTERVAL) {
            Log.d(TAG, "Critical memory cleanup skipped due to throttling")
            return
        }

        try {
            Log.w(TAG, "Performing critical memory cleanup")
            lastCriticalCleanupTime = currentTime

            // 清理所有图片缓存 - 使用应用级别的协程作用域，必须在主线程执行
            applicationScope?.launch(Dispatchers.Main) {
                try {
                    val glide = com.bumptech.glide.Glide.get(BaseApplication.context)
                    glide.clearMemory()
                    Log.d(TAG, "Glide memory cache cleared successfully")

                    // 在后台线程清理磁盘缓存，避免阻塞
                    applicationScope?.launch(Dispatchers.IO) {
                        try {
                            glide.clearDiskCache()
                            Log.d(TAG, "Glide disk cache cleared successfully")
                        } catch (e: Exception) {
                            Log.e(TAG, "Error clearing Glide disk cache", e)
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error clearing Glide caches", e)
                }
            }

            // 同步MMKV数据
            try {
                com.sgmw.common.utils.MMKVUtils.sync()
            } catch (e: Exception) {
                Log.e(TAG, "Error syncing MMKV", e)
            }

            // 避免频繁调用System.gc()和runFinalization()，这可能导致FinalizerDaemon过载
            // 改为更温和的内存回收建议
            // System.gc()
            // System.runFinalization() // 注释掉，避免FinalizerDaemon线程过载
            // System.gc()

            Log.w(TAG, "Critical memory cleanup completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error during critical memory cleanup", e)
        }
    }
    
    /**
     * 获取当前内存使用情况
     */
    fun getCurrentMemoryUsage(): MemoryUsageInfo {
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        val memoryUsageRatio = usedMemory.toFloat() / maxMemory.toFloat()
        
        return MemoryUsageInfo(
            usedMemoryMB = usedMemory / 1024 / 1024,
            maxMemoryMB = maxMemory / 1024 / 1024,
            usageRatio = memoryUsageRatio
        )
    }
    
    /**
     * 检查终结器队列状态，防止FinalizerDaemon线程过载
     */
    private fun checkFinalizerQueue() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastFinalizerCheckTime < FINALIZER_CHECK_INTERVAL) {
            return
        }

        try {
            lastFinalizerCheckTime = currentTime
            val finalizerQueueSize = getFinalizerQueueSize()

            if (finalizerQueueSize > 1000) { // 阈值可根据实际情况调整
                Log.w(TAG, "Finalizer queue size is high: $finalizerQueueSize, triggering gentle cleanup")
                // 触发温和的内存清理，不使用强制GC
                performGentleMemoryCleanup()
            } else if (finalizerQueueSize > 500) {
                Log.d(TAG, "Finalizer queue size: $finalizerQueueSize (moderate)")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking finalizer queue", e)
        }
    }

    /**
     * 获取终结器队列大小
     */
    private fun getFinalizerQueueSize(): Int {
        return try {
            val vmDebugClass = Class.forName("dalvik.system.VMDebug")
            val method = vmDebugClass.getMethod("countInstancesOfClass", Class::class.java, Boolean::class.javaPrimitiveType)
            val finalizerClass = Class.forName("java.lang.ref.FinalizerReference")
            method.invoke(null, finalizerClass, false) as Int
        } catch (e: Exception) {
            // 如果无法获取，返回0，不影响正常流程
            0
        }
    }

    /**
     * 执行温和的内存清理，不触发强制GC
     */
    private fun performGentleMemoryCleanup() {
        try {
            Log.d(TAG, "Performing gentle memory cleanup")

            // 只清理图片内存缓存，不清理磁盘缓存 - 使用应用级别的协程作用域，必须在主线程执行
            applicationScope?.launch(Dispatchers.Main) {
                try {
                    com.bumptech.glide.Glide.get(BaseApplication.context).clearMemory()
                    Log.d(TAG, "Gentle Glide memory cache cleared successfully")
                } catch (e: Exception) {
                    Log.e(TAG, "Error clearing Glide memory cache", e)
                }
            }

            // 不调用System.gc()，让系统自然回收
            Log.d(TAG, "Gentle memory cleanup completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error during gentle memory cleanup", e)
        }
    }

    /**
     * 紧急内存清理，用于异常处理器调用
     */
    fun performEmergencyCleanup() {
        try {
            Log.w(TAG, "Performing emergency memory cleanup")

            // 立即清理图片缓存 - 检查是否在主线程
            try {
                if (android.os.Looper.myLooper() == android.os.Looper.getMainLooper()) {
                    // 在主线程，直接清理
                    com.bumptech.glide.Glide.get(BaseApplication.context).clearMemory()
                } else {
                    // 不在主线程，切换到主线程执行
                    android.os.Handler(android.os.Looper.getMainLooper()).post {
                        try {
                            com.bumptech.glide.Glide.get(BaseApplication.context).clearMemory()
                        } catch (e: Exception) {
                            android.util.Log.e(TAG, "Emergency cleanup: Error clearing Glide cache on main thread: $e")
                        }
                    }
                }
            } catch (e: Exception) {
                // 紧急情况下，异常也要静默处理
                android.util.Log.e(TAG, "Emergency cleanup: Error clearing Glide cache: $e")
            }

            // 同步MMKV数据
            try {
                com.sgmw.common.utils.MMKVUtils.sync()
            } catch (e: Exception) {
                android.util.Log.e(TAG, "Emergency cleanup: Error syncing MMKV: $e")
            }

            Log.w(TAG, "Emergency memory cleanup completed")
        } catch (e: Exception) {
            // 紧急清理失败也要静默处理，避免异常传播
            android.util.Log.e(TAG, "Error during emergency memory cleanup: $e")
        }
    }

    /**
     * 内存使用信息数据类
     */
    data class MemoryUsageInfo(
        val usedMemoryMB: Long,
        val maxMemoryMB: Long,
        val usageRatio: Float
    )
}
