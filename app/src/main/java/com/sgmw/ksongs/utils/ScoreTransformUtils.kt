package com.sgmw.ksongs.utils

/**
 * @author: 董俊帅
 * @time: 2025/2/27
 * @desc: K歌打分等级转换工具类
 */
object ScoreTransformUtils {

    /**
     * 五维打分需要自行转换等级
     * 分数等级是按照单句平均分来定级的（<=60 -->C；60~70 -->B；70～80 -->A；80～90-->S；90～95-->SS；100-->SSS）
     */
    fun transformScore(score: Int): String {
        return when {
            score < 60 -> "C"
            score < 70 -> "B"
            score < 80 -> "A"
            score < 90 -> "S"
            score < 95 -> "SS"
            score <= 100 -> "SSS"
            else -> "SSS"
        }
    }

}