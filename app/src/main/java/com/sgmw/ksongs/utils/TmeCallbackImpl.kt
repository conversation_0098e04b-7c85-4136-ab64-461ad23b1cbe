package com.sgmw.ksongs.utils

import com.sgmw.common.utils.Log
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.Result
import com.sgmw.ksongs.viewmodel.MainViewModel
import com.tme.ktv.network.core.TmeCall
import com.tme.ktv.network.core.TmeCallback

// token过期或者错误
const val ERROR_CODE_TOKEN_EXPIRE = 40002
// 账号在其他车机上登录
const val ERROR_CODE_OTHER_LOGIN = 40004

class TmeCallbackImpl<R>(val operation: Operation,
                         val onRequestResult: ((Result<R?>) -> Unit)) : TmeCallback<R>() {

    private val TAG = "TmeCallbackImpl"

    override fun onSuccess(p0: TmeCall<*>?, p1: R) {
        Log.d(TAG, "onSuccess result: ${p1.toString()}")
        val result = Result.Success<R?>(p1, operation)
        onRequestResult(result)
    }

    override fun onFail(p0: TmeCall<*>?, p1: Throwable?) {
        Log.d(TAG, "onFail result: ${p1.toString()}")
        processErrorCodeInternal(p0, p1)
    }

    private fun processErrorCodeInternal(p0: TmeCall<*>?, p1: Throwable?) {
        when (p0?.code) {
            ERROR_CODE_TOKEN_EXPIRE -> {
                Log.e(TAG, "认证失败，可能是token错误或者过期")
                MainViewModel.sendTokenErrorMsg(ERROR_CODE_TOKEN_EXPIRE)
            }
            ERROR_CODE_OTHER_LOGIN -> {
                Log.e(TAG, "token失效，失效原因：互踢")
                MainViewModel.sendTokenErrorMsg(ERROR_CODE_OTHER_LOGIN)
            }
            else -> {
                val failureResult = Result.Failure<R?>(p0?.code, p1, operation)
                onRequestResult(failureResult)
            }
        }

    }

}