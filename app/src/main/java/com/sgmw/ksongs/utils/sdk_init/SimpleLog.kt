package com.sgmw.ksongs.utils.sdk_init

import android.util.Log
import com.tme.ktv.api.IKLog

/**
 * Created by jack<PERSON><PERSON> on 2023/7/22
 */
class SimpleLog : IKLog {

    companion object {
        const val TAG_KTV_RETROFIT = "KTVRetrofit"
        const val USER_NICK = "user_nick"
        const val SEARCH = "search"

        const val IGNORE_TAG = "AudioRecorderReceiver"
    }

    override fun i(tag: String?, msg: String?) {
        Log.i(tag, "$msg")
    }

    override fun d(tag: String?, msg: String?) {
        // 敏感信息不输出 这条信息包含用户昵称 以及用户头像
        if ((TAG_KTV_RETROFIT == tag && msg?.contains(USER_NICK) == true)
            || (TAG_KTV_RETROFIT == tag && msg?.contains(SEARCH) == true)) {
            return
        }
        Log.d(tag, "$msg")
    }

    override fun w(tag: String?, msg: String?) {
        Log.w(tag, "$msg")
    }

    override fun e(tag: String?, msg: String?) {
        Log.e(tag, "$msg")
    }

    override fun flush() {
    }
}