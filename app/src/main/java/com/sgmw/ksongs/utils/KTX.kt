package com.sgmw.ksongs.utils

import android.view.Gravity
import android.view.LayoutInflater
import android.widget.TextView
import android.widget.Toast
import com.sgmw.common.BaseApplication
import com.sgmw.common.utils.Log
import com.sgmw.common.utils.ioLaunch
import com.sgmw.common.utils.mainLaunch
import com.sgmw.ksongs.R
import com.sgmw.ksongs.db.entity.CollectSongInfo
import com.sgmw.ksongs.db.entity.DemandSongInfo
import com.sgmw.ksongs.db.entity.PlayRecordSongInfo
import com.sgmw.ksongs.model.bean.SongInfoBean
import com.sgmw.ksongs.model.repository.CollectRepository
import com.sgmw.ksongs.model.repository.PlayRecordRepository
import com.sgmw.ksongs.ui.playlist.PlayListManager
import com.sgmw.ksongs.ui.songplay.KaraokePlayerManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 接口分页请求时：默认分页大小
 */
const val DEFAULT_PAGE_LIST_SIZE = 20

/**
 * @author: 董俊帅
 * @time: 2025/2/17
 * @desc:
 */

// 扩展函数：为MutableList<SongInfoBean>设置isInDemandList的状态
suspend fun List<SongInfoBean>.updateDemandStatus() {
    val allDemandSongInfo = PlayListManager.getDemandSongInfoList()
    val playingSongInfo = PlayListManager.getPlayingSongInfo()
    // 遍历并修改原列表中的元素
    for (songInfo in this) {
        // 设置isInDemandList状态
        songInfo.isInDemandList = allDemandSongInfo
            .any { it.songInfo.song_id == songInfo.song_id }
        if (playingSongInfo?.songInfo?.song_id == songInfo.song_id) {
            songInfo.isPlaying = true
            songInfo.isPlayingState = KaraokePlayerManager.isPlaying()
        }
    }
}

/**
 * 接口批量下来数据后，需要调用该方法来填充是否已收藏字段
 * 根据数据库收藏歌曲，填充收藏状态
 */
suspend fun List<SongInfoBean>.updateCollectStatus() {
    if (this.isEmpty()) return
    val repository = CollectRepository()
    val collectedSongs = repository.findCollectedSongIds().toSet()
    this.forEach { songInfoBean ->
        songInfoBean.isCollect = collectedSongs.contains(songInfoBean.song_id)
    }
}

/**
 * 收藏/取消收藏歌曲
 * 是否收藏或者取消收藏依赖SongInfoBean。isCollect
 * insertCallBack 返回是否插入成功
 */
fun collectOrCancelSongInfo(songInfoBean: SongInfoBean, insertCallBack: ((Boolean) -> Unit)? = null) {
    ioLaunch {
        if (songInfoBean.isCollect) {
            CollectRepository().delete(songInfoBean.song_id)
            insertCallBack?.invoke(false)
        } else {
            if (!CollectRepository().insertOrUpdate(CollectSongInfo(songInfo = songInfoBean))) {
                withContext(Dispatchers.Main) {
                    showToast(R.string.collect_limit_reached)
                    insertCallBack?.invoke(false)
                }
            } else {
                withContext(Dispatchers.Main) {
                    insertCallBack?.invoke(true)
                }
            }
        }
    }
}

/**
 * 添加播放记录
 */
fun addPlayRecord(songInfoBean: SongInfoBean) {
    ioLaunch {
        songInfoBean.isPlaying = false
        songInfoBean.isPlayingState = false
        PlayRecordRepository().addPlayRecord(PlayRecordSongInfo(songInfo = songInfoBean))
    }
}

/**
 * 歌曲置顶
 */
fun addTopSongInfo(songInfo: DemandSongInfo) {
    ioLaunch {
        songInfo.songInfo.isPlaying = false
        songInfo.songInfo.isPlayingState = false
        PlayListManager.addTopSongInfo(songInfo)
    }
}

/**
 * 切换歌曲在已点列表中的状态
 * 如果歌曲在已点列表中，则删除；如果不在，则添加
 */
fun toggleDemandSongInfo(songInfo: DemandSongInfo, cardName: String = "", callback: ((Boolean) -> Unit)? = null) {
    // 使用ioLaunch，DaemonThreadDispatcher现在支持自动重新初始化
    // 如果调度器已关闭，会自动恢复或使用备选调度器
    ioLaunch {
        performToggleOperation(songInfo, cardName, callback)
    }
}

/**
 * 执行切换操作的具体逻辑
 */
private suspend fun performToggleOperation(songInfo: DemandSongInfo, cardName: String, callback: ((Boolean) -> Unit)?) {
    try {
        Log.d("toggleDemandSongInfo", "toggleDemandSongInfo name: ${songInfo.songInfo.song_name}")
        // 先获取当前状态，避免重复操作
        val currentIsInDemandList = PlayListManager.getDemandSongInfoList()
            .any { it.songInfo.song_id == songInfo.songInfo.song_id }

        // 立即更新本地状态，避免UI闪动
        songInfo.songInfo.isInDemandList = !currentIsInDemandList

        // 执行数据库操作
        PlayListManager.addDemandSongInfo(songInfo, cardName = cardName)
        Log.d("toggleDemandSongInfo", "toggleDemandSongInfo success")
        // 回调返回操作后的状态
        withContext(Dispatchers.Main) {
            callback?.invoke(!currentIsInDemandList)
        }
    } catch (e: Exception) {
        Log.e("toggleDemandSongInfo", "toggleDemandSongInfo operation failed", e)
        // 如果操作失败，恢复原状态
        songInfo.songInfo.isInDemandList = !songInfo.songInfo.isInDemandList
        withContext(Dispatchers.Main) {
            callback?.invoke(songInfo.songInfo.isInDemandList)
        }
        Log.e("toggleDemandSongInfo", "toggleDemandSongInfo error: ${e.message}")
    }
}

fun showToast(resId: Int, duration: Int = Toast.LENGTH_SHORT) {
    mainLaunch {
        val toastView = LayoutInflater.from(BaseApplication.context).inflate(R.layout.layout_toast, null)
        val tvMsg = toastView.findViewById<TextView>(R.id.toast_tv)
        tvMsg.text = BaseApplication.context.getString(resId)

        val toast = Toast(BaseApplication.context)
        toast.setGravity(Gravity.TOP or Gravity.CENTER_HORIZONTAL, 0, 96)
        toast.duration = duration
        toast.view = toastView
        toast.show()
    }
}


