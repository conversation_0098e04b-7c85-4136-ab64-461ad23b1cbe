package com.sgmw.ksongs.utils.sdk_init

import android.app.Activity
import android.content.Context
import android.util.Log
import com.tme.ktv.api.AppRequestProvider
import com.tme.ktv.player.playlist.PendSong
import com.tme.ktv.player.preload.common.PlayPermissionProvider
import com.tme.ktv.widget.KGDialog

/**
 * Created by jackrfwang on 2023/6/25
 */
class SampleAppRequestProvider : AppRequestProvider {
    private val TAG = "KtvPlay-AppRequest"

    /**
     * 向宿主请求的通用接口，需要宿主实现
     * groupId : 请求大类：如播放、登录、支付等，具体见文档
     * eventType : 请求类型，区分具体是什么请求，如"guideUrl"代表获取mic引导链接等
     * status : 请求状态，通用类型的请求参数
     * extra : 其他的请求参数放在 extra中
     *@return 返回true则表示此操作正在处理，当播放页接收到onStart()后，会继续执行。返回false则表明已处理完成。
     */
    override fun request(
        context: Context,
        groupId: String,
        eventType: String,
        status: Any?,
        extra: Map<String, Any>?,
    ): Any {
        Log.i(TAG, "groupId:$groupId, eventType:$eventType, status:$status")
        //播放阻断的场景
        if (groupId == "type_play" && eventType == "play_intercept") {
            if (status == PlayPermissionProvider.InterceptType.LOGIN) {
                //触发登录阻断，需要接入方登录
                showErrorDialog(context, "触发播放阻断，需要登录")
                return true
            } else {
                //触发播放阻断，此时可跳转VIP支付页，支付完成后返回播放页，播放页接收到onStart()后，会继续执行。
                showErrorDialog(context, "触发播放阻断 $status")
            }
            return true
        }
        //需要跳转到接入方的播放页的场景
        if (groupId == "type_play" && eventType == "goto_play_page") {
            Log.d(TAG, "goto play page")
            if (status is PendSong) {
//                JumpUtil.go(context, "ktvsdk://kege.com/ktv/play?song_id=${status.mid}&from=5")
                return true
            }
        }
        return false
    }

    private fun showErrorDialog(context: Context, msg: String?) {
        val content = msg ?: "连接错误，请检查您的网络~"
        val dialog = KGDialog(
            context,
            content,
            "确定",
            null,
            KGDialog.SINGLE_CHECK
        )
        dialog.setClickListener(object : KGDialog.ClickListenerInterface {
            override fun doConfirm() {
                dialog.dismiss()
                exitPage()
            }

            override fun doCancel() {
                dialog.dismiss()
                exitPage()
            }

            private fun exitPage() {
                (context as? Activity)?.finish()
            }

            override fun onKeyBack() {
                dialog.dismiss()
                exitPage()
            }

        })
        dialog.show()
    }
}