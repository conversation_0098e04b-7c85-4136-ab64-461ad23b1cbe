package com.sgmw.ksongs.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.ui.songplay.KaraokePlayerManager

/**
 *系统 Launcher与K歌交互 暂停K歌
 */
class LauncherToKSongsReceiver: BroadcastReceiver() {

    companion object{
        // 暂停
        const val CUSTOM_ACTION = "com.sgmw.ksongs.action"
        const val ACTION_DATA_KEY = "action"
        const val ACTION_PAUSE  = "pause"
    }
    override fun onReceive(context: Context?, intent: Intent?) {
        if (intent?.action != null && intent.action.equals(CUSTOM_ACTION)){
            val action = intent?.getStringExtra(ACTION_DATA_KEY)
            Log.d("Receiver", "Received data: $action")
            if (ACTION_PAUSE == action){
                // 暂停k歌  不退出播放页面
                KaraokePlayerManager.pause()
            }
        }

    }
}