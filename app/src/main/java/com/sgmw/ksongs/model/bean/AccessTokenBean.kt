package com.sgmw.ksongs.model.bean



data class AccessTokenBean(
    var access_token: String,
    val error_code: Int,
    val error_msg: String,
    var expires_in: Int = -1,
    val openid: String,
    val orig_acnt_type: String? = null,
    var refresh_token: String,
    val scope: String,
    val unionid: String? = null,
    var overdue_time: Long = 0L
) {
    override fun toString(): String {
        return "access_token: $access_token error_code: $error_code error_msg: $error_msg expires_in: $expires_in " +
                "openid: $openid " +
                "orig_acnt_type: $orig_acnt_type refresh_token: $refresh_token scope: $scope unionid: $unionid"
    }
}