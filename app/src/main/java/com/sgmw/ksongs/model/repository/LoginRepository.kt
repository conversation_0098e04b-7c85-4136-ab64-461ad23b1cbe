package com.sgmw.ksongs.model.repository

import android.util.Log
import com.sgmw.common.http.RetrofitClient
import com.sgmw.common.mvvm.m.BaseRepository
import com.sgmw.ksongs.BuildConfig
import com.sgmw.ksongs.model.bean.LightQrCodeRequest
import com.sgmw.ksongs.api.LoginApi
import com.sgmw.ksongs.utils.sdk_init.APIHelper
import com.sgmw.ksongs.model.bean.AccessTokenBean
import com.sgmw.ksongs.model.bean.AccessTokenRequest
import com.sgmw.ksongs.model.bean.LightQrCodeBean
import com.sgmw.ksongs.model.bean.LightQrStatBean
import com.sgmw.ksongs.model.bean.LightQrStatRequest
import com.sgmw.ksongs.model.bean.RefreshTokenBean
import com.sgmw.ksongs.model.bean.RefreshTokenRequest
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class LoginRepository : BaseRepository() {

    private val loginApi: LoginApi = RetrofitClient.instance.create(LoginApi::class.java)

    // 封装异步请求
    fun getLightQrCode(
        onRequestResult: (code: Int?, lightQrCode: LightQrCodeBean?) -> Unit  // 接受一个回调
    ) {
        // 创建请求体
        val request = LightQrCodeRequest(
            BuildConfig.KTV_SDK_APP_ID,
            "code",
            "snsapi_login",
            APIHelper.sign(),
            (System.currentTimeMillis() / 1000).toInt()
        )
        // 发起网络请求
        val call: Call<LightQrCodeBean> = loginApi.getLightQrCode(
            request
        )

        // 异步执行请求
        call.enqueue(object : Callback<LightQrCodeBean> {
            override fun onResponse(call: Call<LightQrCodeBean>, response: Response<LightQrCodeBean>) {
                if (response.isSuccessful) {
                    // 请求成功
                    val lightQrCode = response.body()
                    Log.d(TAG, "getLightQrCode success qr_code: ${lightQrCode?.qr_code}")
                    onRequestResult(lightQrCode?.error_code, lightQrCode)
                } else {
                    // 请求失败
                    Log.e(TAG, "getLightQrCode error: ${response.message()}")
                    onRequestResult(null, null)
                }
            }

            override fun onFailure(call: Call<LightQrCodeBean>, t: Throwable) {
                // 网络请求失败
                Log.e(TAG, "getLightQrCode failed: ${t.message}")
                onRequestResult(null, null)
            }
        })
    }

    fun getLightQrStat(
        lightQrCodeBean: LightQrCodeBean,
        onRequestResult: (code: Int?, lightQrStat: LightQrStatBean?) -> Unit
    ): Call<LightQrStatBean>  {

        // 创建请求体
        val request = LightQrStatRequest(
            lightQrCodeBean.qr_code,
            lightQrCodeBean.qr_sig,
            BuildConfig.KTV_SDK_APP_ID,
            APIHelper.sign(),
            (System.currentTimeMillis() / 1000).toInt()
        )

        // 发起网络请求
        val call: Call<LightQrStatBean> = loginApi.getLightQrStat(
            request
        )

        // 异步执行请求
        call.enqueue(object : Callback<LightQrStatBean> {
            override fun onResponse(call: Call<LightQrStatBean>, response: Response<LightQrStatBean>) {
                if (response.isSuccessful) {
                    // 请求成功
                    val lightQrStat = response.body()
                    onRequestResult(lightQrStat?.error_code, lightQrStat)
                } else {
                    // 请求失败
                    Log.e(TAG, "getLightQrCode error: ${response.message()}")
                    onRequestResult(null, null)
                }
            }

            override fun onFailure(call: Call<LightQrStatBean>, t: Throwable) {
                // 网络请求失败
                Log.e(TAG, "getLightQrStat failed: ${t.message}")
                onRequestResult(null, null)
            }
        })

        return call
    }

    fun getAccessToken(
        code: String,
        onRequestResult: (code: Int?, accessTokenBean: AccessTokenBean?) -> Unit
    ) {

        // 创建请求体
        val request = AccessTokenRequest(
            BuildConfig.KTV_SDK_APP_ID,
            BuildConfig.KTV_SDK_TEST_APP_KEY,
            code,
            "authorization_code"
        )

        // 发起网络请求
        val call: Call<AccessTokenBean> = loginApi.getAccessToken(
            request
        )

        // 异步执行请求
        call.enqueue(object : Callback<AccessTokenBean> {
            override fun onResponse(call: Call<AccessTokenBean>, response: Response<AccessTokenBean>) {
                if (response.isSuccessful) {
                    // 请求成功
                    val accessTokenBean = response.body()
                    Log.d(
                        TAG,
                        "getAccessToken success accessTokenBean: ${accessTokenBean.toString()}"
                    )
                    AccessTokenManager.saveAccessTokenBean(accessTokenBean)
                    onRequestResult(accessTokenBean?.error_code, accessTokenBean)
                } else {
                    // 请求失败
                    Log.e(TAG, "getLightQrCode error: ${response.message()}")
                    onRequestResult(null, null)
                }
            }

            override fun onFailure(call: Call<AccessTokenBean>, t: Throwable) {
                // 网络请求失败
                Log.e(TAG, "getAccessToken error throwable: ${t.message}")
                onRequestResult(null, null)
            }
        })
    }

    companion object {
        private const val TAG = "LoginRepository"
    }
}