package com.sgmw.ksongs.model.repository

import com.sgmw.common.mvvm.m.BaseRepository
import com.sgmw.ksongs.api.SongStationApi
import com.sgmw.ksongs.model.bean.CategoryBean
import com.sgmw.ksongs.model.bean.HotTopicBean
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.RankingsBean
import com.sgmw.ksongs.model.bean.Result
import com.sgmw.ksongs.model.bean.SingerBean
import com.sgmw.ksongs.model.bean.UserLikeBean
import com.sgmw.ksongs.ui.hottopic.DEFAULT_HOT_TOPIC_PAGE_SIZE
import com.sgmw.ksongs.ui.hottopic.RECOMMEND_TYPE_HOT_TOPIC
import com.sgmw.ksongs.ui.user.feature.USER_FEATURE_SONG_PAGE_SIZE
import com.sgmw.ksongs.ui.user.feature.UserFeatureSongType
import com.sgmw.ksongs.utils.DEFAULT_PAGE_LIST_SIZE
import com.sgmw.ksongs.utils.PLAY_LIST_TYPE_THEME
import com.sgmw.ksongs.utils.PLAY_LIST_TYPE_TOP
import com.sgmw.ksongs.utils.RANK_MAX_PAGE_SIZE
import com.sgmw.ksongs.utils.RankInfo
import com.sgmw.ksongs.utils.TmeCallbackImpl
import com.tme.ktv.network.KtvRetrofitService
import com.tme.ktv.network.core.TmeCall

class SongStationRepository : BaseRepository() {

    companion object{
        private val TAG = "SongStationRepository"
        const val START_INDEX = 0
        // type 热门伴奏
        const val TYPE_HOT_ACCOMPANIMENT = 1
        // 热门伴奏个数
        const val NUMBER_ACCOMPANIMENT = 20
        // type 热门歌手
        const val TYPE_HOT_SINGER = 2
        // 热门歌手个数
        const val NUMBER_SINGER = 6
        // 热门专题个数
        const val NUMBER_TOPICS = 4
        // type 自建唱单
        const val TYPE_SELF_BUILT_PLAYLIST = 4

    }


    /**
     *  首页获取热门歌手
     */
    fun getHotSinger(
        operation: Operation,
        num: Int,
        type: Int,
        onRequestResult: (Result<SingerBean?>) -> Unit
    ) {
        val call: TmeCall<SingerBean> =
            KtvRetrofitService.get().getService(SongStationApi::class.java).getHotSinger(
                num, type
            )
        call.enqueue(TmeCallbackImpl(operation, onRequestResult))
    }

    /**
     * 获取热门榜歌曲列表
     */
    fun getMultiPlaylist(rankInfo: RankInfo, operation: Operation, nextIndex: Int, pageSize: Int = RANK_MAX_PAGE_SIZE,
        onRequestResult: (Result<RankingsBean?>) -> Unit
    ) {
        val call: TmeCall<RankingsBean> = KtvRetrofitService.get().getService(SongStationApi::class.java)
               .getMultiPlaylist(nextIndex, pageSize, rankInfo.id, PLAY_LIST_TYPE_TOP)
        call.enqueue(TmeCallbackImpl(operation, onRequestResult))
    }


    /**
     * 获取用户个推歌单
     */
    fun getUserFeatureSongList(@UserFeatureSongType userFeatureSongType: Int, operation: Operation, startIndex: Int, pageSize: Int = USER_FEATURE_SONG_PAGE_SIZE,
        onRequestResult: (Result<UserLikeBean?>) -> Unit
    ) {
        val call: TmeCall<UserLikeBean> = KtvRetrofitService.get().getService(SongStationApi::class.java)
            .getUserFeatureSongList(userFeatureSongType, startIndex, pageSize)
        call.enqueue(TmeCallbackImpl(operation, onRequestResult))
    }

    /**
     *  首页获取热门专题
     */
    fun getHotTopics(operation: Operation, onRequestResult: (Result<HotTopicBean?>) -> Unit) {
        val type = RECOMMEND_TYPE_HOT_TOPIC
        val num = DEFAULT_HOT_TOPIC_PAGE_SIZE
        val call: TmeCall<HotTopicBean> =
            KtvRetrofitService.get().getService(SongStationApi::class.java).getRecommendList(type, num)
        call.enqueue(TmeCallbackImpl(operation, onRequestResult))
    }

    /**
     *  获取专题下的歌曲列表
     */
    fun getThemeSongList(operation: Operation, playlistId: String, index: Int, pageSize: Int = DEFAULT_PAGE_LIST_SIZE,
                         onRequestResult: (Result<RankingsBean?>) -> Unit) {
        KtvRetrofitService.get().getService(SongStationApi::class.java).getMultiThemelist(
            index, pageSize, playlistId, PLAY_LIST_TYPE_THEME
        ).enqueue(TmeCallbackImpl(operation, onRequestResult))
    }

    /**
     *  获取分类点歌一级列表
     */
    fun getCategoryList(operation: Operation, sceneId: Int = 0,
                         onRequestResult: (Result<CategoryBean?>) -> Unit) {
        KtvRetrofitService.get().getService(SongStationApi::class.java).getCategoryList(sceneId)
            .enqueue(TmeCallbackImpl(operation, onRequestResult))
    }

}