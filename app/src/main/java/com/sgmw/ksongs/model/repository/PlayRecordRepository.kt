package com.sgmw.ksongs.model.repository

import com.sgmw.common.mvvm.m.BaseRepository
import com.sgmw.ksongs.db.DbManager
import com.sgmw.ksongs.db.entity.PlayRecordSongInfo
import kotlinx.coroutines.flow.Flow

/**
 * 最大播放记录数量
 */
private const val MAX_PLAY_RECORD_COUNT = 100

class PlayRecordRepository : BaseRepository() {

    /**
     * 添加播放记录
     * 根据需求：超过100条记录时，应删除最早的一条记录，然后插入新记录
     */
    suspend fun addPlayRecord(playRecord: PlayRecordSongInfo) {
        val savedPlayRecord = find(playRecord.songInfo.song_id)
        if (savedPlayRecord == null) {
            if (getTotalCount() >= MAX_PLAY_RECORD_COUNT) {
                findLast()?.let { delete(listOf(it)) }
            }
            insert(playRecord)
        } else {
            savedPlayRecord.insertTime = System.currentTimeMillis()
            update(savedPlayRecord)
        }
    }

    private suspend fun insert(collectInfo: PlayRecordSongInfo) {
        DbManager.getPlayRecordDao().insert(collectInfo)
    }

    private suspend fun find(songId: String): PlayRecordSongInfo? {
        return DbManager.getPlayRecordDao().find(songId)
    }

    private suspend fun findLast(): PlayRecordSongInfo? {
        return DbManager.getPlayRecordDao().findLast()
    }

    private suspend fun update(collectInfo: PlayRecordSongInfo) {
        DbManager.getPlayRecordDao().update(collectInfo)
    }

    fun findAll(): Flow<List<PlayRecordSongInfo>> {
        return DbManager.getPlayRecordDao().findAll()
    }

    suspend fun findPlayRecordSongIds(): List<String> {
        return DbManager.getPlayRecordDao().findPlayRecordSongIds()
    }

    suspend fun delete(list: List<PlayRecordSongInfo>) {
        val array: Array<PlayRecordSongInfo> = list.toTypedArray()
        DbManager.getPlayRecordDao().delete(*array)
    }

    private suspend fun getTotalCount(): Int {
        return DbManager.getPlayRecordDao().getTotalCount()
    }

}