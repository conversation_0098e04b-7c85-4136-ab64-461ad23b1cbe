package com.sgmw.ksongs.model.repository

import com.sgmw.common.mvvm.m.BaseRepository
import com.sgmw.common.utils.defaultLaunch
import com.sgmw.common.utils.mainLaunch
import com.sgmw.ksongs.api.PaymentApi
import com.sgmw.ksongs.api.UserApi
import com.sgmw.ksongs.model.bean.LimitConfigBean
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.Result
import com.sgmw.ksongs.model.bean.UserInfoBean
import com.sgmw.ksongs.model.bean.VipInfoBean
import com.sgmw.ksongs.utils.TmeCallbackImpl
import com.tme.ktv.api.KtvSdk
import com.tme.ktv.demo.bean.VipProductBean
import com.tme.ktv.network.KtvRetrofitService
import com.tme.ktv.network.core.TmeCall

class MineRepository : BaseRepository() {

    private val TAG = "MineRepository"

    fun getUserInfo(iconSize:Int, needAccountType:Int, operation: Operation, onRequestResult:(Result<UserInfoBean?>) -> Unit){
        val call: TmeCall<UserInfoBean> =
            KtvRetrofitService.get().getService(UserApi::class.java).getUserInfo(iconSize,needAccountType)
        call.enqueue(TmeCallbackImpl(operation, onRequestResult))
    }

    fun getVipInfo(operation: Operation, onRequestResult:(Result<VipInfoBean?>) -> Unit){
        val call:TmeCall<VipInfoBean> = KtvRetrofitService.get().getService(PaymentApi::class.java).getVipInfo()
        call.enqueue(TmeCallbackImpl(operation, onRequestResult))
    }

    fun getVipProduct(operation: Operation, onRequestResult:(Result<VipProductBean?>) -> Unit){
        val call:TmeCall<VipProductBean> = KtvRetrofitService.get().getService(PaymentApi::class.java).getVipProduct(
            KtvSdk.getVersionName(),1)
        call.enqueue(TmeCallbackImpl(operation, onRequestResult))
    }

    /**
     * 获取播放次数限制
     */
    fun getLimitConfig(operation: Operation, onRequestResult: (Result<LimitConfigBean?>) -> Unit) {
        defaultLaunch {
            AccessTokenManager.getAccessTokenBean()?.let {
                val call: TmeCall<LimitConfigBean> =
                    KtvRetrofitService.get().getService(UserApi::class.java)
                        .getLimitConfig(it.openid, KtvSdk.getSdkImei())
                call.enqueue(TmeCallbackImpl(operation, onRequestResult))
            }
        }
    }
}