package com.sgmw.ksongs.model.repository

import android.util.Log
import com.blankj.utilcode.util.GsonUtils
import com.sgmw.common.http.RetrofitClient
import com.sgmw.common.utils.MMKVUtils
import com.sgmw.ksongs.BuildConfig
import com.sgmw.ksongs.api.LoginApi
import com.sgmw.ksongs.model.bean.AccessTokenBean
import com.sgmw.ksongs.model.bean.RefreshTokenBean
import com.sgmw.ksongs.model.bean.RefreshTokenRequest
import com.sgmw.ksongs.ui.songplay.KaraokePlayerManager
import com.sgmw.ksongs.utils.sdk_init.APIHelper
import com.tme.ktv.login.api.LoginEvent
import com.tme.ktv.login.api.ThirdPartyLoginService
import kotlinx.coroutines.suspendCancellableCoroutine
import retrofit2.Callback
import retrofit2.Response
import kotlin.coroutines.resume

/**
 * @author: 董俊帅
 * @time: 2025/1/17
 * @desc:
 */

object AccessTokenManager {

    private const val TAG = "AccessTokenManager"

    private var accessTokenBean: AccessTokenBean? = null

    init {
        if (MMKVUtils.contains("access_token") == true) {
            val tokenJson = MMKVUtils.getString("access_token", "")
            tokenJson?.let {
                // 解析 JSON 数据
                accessTokenBean = GsonUtils.fromJson(tokenJson, AccessTokenBean::class.java)
            }
        }
    }

    /**
     * 是否登录
     * 只要accessTokenBean不为null，就认为是登录了
     * 在退出登录，或者登录被踢，则清除 access_token
     */
    fun isLogin(): Boolean {
        return accessTokenBean != null
    }

    /**
     * 退出登录
     */
    fun logout() {
        Log.d(TAG, "logout")
        KaraokePlayerManager.stop()
        accessTokenBean?.let {
            ThirdPartyLoginService.notifyLoginStateChanged(LoginEvent.Logout(it.openid ?: ""))
        }
        accessTokenBean = null
        MMKVUtils.put("access_token", "")
    }

    // 保存 access_token 到 MMKV
    fun saveAccessTokenBean(accessTokenBean: AccessTokenBean?) {
        Log.d(TAG, "saveAccessTokenBean expires_in: ${accessTokenBean?.expires_in?.times(1) ?: 0}")
        accessTokenBean?.overdue_time = System.currentTimeMillis() / 1000 + (accessTokenBean?.expires_in?.times(1) ?: 0)
        AccessTokenManager.accessTokenBean = accessTokenBean
        val tokenJson = GsonUtils.toJson(accessTokenBean)
        MMKVUtils.put("access_token", tokenJson)
    }

    // 获取 access_token
    suspend fun getAccessToken(): String {
        if (accessTokenBean == null) {
            val tokenJson = MMKVUtils.getString("access_token", "")
            tokenJson?.let {
                // 解析 JSON 数据
                accessTokenBean = GsonUtils.fromJson(tokenJson, AccessTokenBean::class.java)
            }
        }
        // 判断是否过期，如果过期则刷新
        if (isTokenExpired()) {
            refreshToken()  // 刷新 token
        }
        val access_token = accessTokenBean?.access_token ?: ""
        Log.d(TAG, "getAccessToken access_token: $access_token")
        return access_token
    }

    // 获取 access_token
    suspend fun getAccessTokenBean(): AccessTokenBean? {
        if (accessTokenBean == null) {
            val tokenJson = MMKVUtils.getString("access_token", "")
            tokenJson?.let {
                // 解析 JSON 数据
                accessTokenBean = GsonUtils.fromJson(tokenJson, AccessTokenBean::class.java)
            }
        }
        // 判断是否过期，如果过期则刷新
        if (isTokenExpired()) {
            refreshToken()  // 刷新 token
        }
        return accessTokenBean
    }

    fun refreshTokenBean(refreshTokenBean: RefreshTokenBean) {
        accessTokenBean?.let {
            it.access_token = refreshTokenBean.access_token
            it.expires_in = refreshTokenBean.expires_in.toInt()
        }
        saveAccessTokenBean(accessTokenBean)
    }

    /**
     * 判断 access_token 是否过期
     * 过期标准，若距离过期时间小于1小时，即3600秒就刷新token
     */
    private fun isTokenExpired(): Boolean {
        val currentTime = System.currentTimeMillis() / 1000  // 当前时间戳，单位秒
        val overdueTime = accessTokenBean?.overdue_time ?: return true
        Log.d(TAG, "isTokenExpired currentTime: $currentTime overdueTime: $overdueTime")
        // 剩余过期时间 = 过期时间 - 当前时间戳
        val restExpirationTime = overdueTime - currentTime
        Log.d(TAG, "isTokenExpired restExpirationTime: $restExpirationTime")
        val isTokenExpired = restExpirationTime < 60 * 60
        Log.d(TAG, "isTokenExpired isTokenExpired: $isTokenExpired")
        return isTokenExpired
    }

    // 刷新 access_token
    suspend fun refreshToken() {
        // 发起网络请求，调用 refreshToken 接口
        val refreshedToken = fetchRefreshedToken()
        // 更新 token
        accessTokenBean = refreshedToken
        // 保存刷新后的 token 到 MMKV
        saveAccessTokenBean(refreshedToken)
    }

    // 模拟发起网络请求刷新 access_token
    private suspend fun fetchRefreshedToken(): AccessTokenBean {
        // 使用 suspendCoroutine 将回调转换为协程
        return suspendCancellableCoroutine { continuation ->
            // 创建请求体
            val request = RefreshTokenRequest(
                BuildConfig.KTV_SDK_APP_ID,
                accessTokenBean?.openid ?: "",
                accessTokenBean?.refresh_token ?: "",
                APIHelper.sign(),
                (System.currentTimeMillis() / 1000)
            )
            Log.d(TAG, "fetchRefreshedToken request: $request")
            // 发起网络请求
            val call: retrofit2.Call<RefreshTokenBean> =
                RetrofitClient.instance.create(LoginApi::class.java).refreshToken(
                    BuildConfig.KTV_SDK_APP_ID,
                    (System.currentTimeMillis() / 1000),
                    request
                )

            // 异步执行请求
            call.enqueue(object : Callback<RefreshTokenBean> {
                override fun onResponse(
                    call: retrofit2.Call<RefreshTokenBean>,
                    response: Response<RefreshTokenBean>
                ) {
                    if (response.isSuccessful) {
                        // 请求成功
                        val refreshTokenBean = response.body()
                        Log.d(
                            TAG,
                            "fetchRefreshedToken success refreshTokenBean: ${refreshTokenBean.toString()}"
                        )
                        // 请求成功，返回数据并恢复协程
                        if (refreshTokenBean != null) {
                            Log.d(TAG, "fetchRefreshedToken refreshTokenBean error_code: ${refreshTokenBean.error_code}")
                            if (refreshTokenBean.error_code == "0") {
                                if (accessTokenBean != null) {
                                    accessTokenBean?.access_token = refreshTokenBean.access_token
                                    accessTokenBean?.expires_in = refreshTokenBean.expires_in.toInt()
                                    Log.d(TAG, "fetchRefreshedToken success expires_in: ${accessTokenBean?.expires_in}")
                                    accessTokenBean?.overdue_time = System.currentTimeMillis() / 1000 + (accessTokenBean?.expires_in?.times(1) ?: 0)
                                    Log.d(TAG, "fetchRefreshedToken success overdue_time: ${accessTokenBean?.overdue_time}")
                                    continuation.resume(accessTokenBean!!)
                                }
                            } else {
                                val result =
                                    accessTokenBean ?: AccessTokenBean("", 0, "", -1, "", "", "", "", "")
                                continuation.resume(result)
                            }
                        } else {
                            continuation.resume(AccessTokenBean("", 0, "", -1, "", "", "", "", ""))
                        }
                    } else {
                        // 请求失败
                        Log.e(TAG, "fetchRefreshedToken error response.isFail")
                        // 请求失败，抛出异常并恢复协程
                        continuation.resume(AccessTokenBean("", 0, "", -1, "", "", "", "", ""))
                    }
                }

                override fun onFailure(call: retrofit2.Call<RefreshTokenBean>, t: Throwable) {
                    Log.e(TAG, "fetchRefreshedToken error throwable: ${t.message}")
                    // 请求失败，抛出异常并恢复协程
                    continuation.resume(AccessTokenBean("", 0, "", -1, "", "", "", "", ""))
                }
            })
        }
    }

}
