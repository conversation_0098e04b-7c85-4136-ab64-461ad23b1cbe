package com.sgmw.ksongs.model.bean

import java.io.Serializable
/**
 * 接口返回数据封装后，发送到页面上去
 */
sealed class Result<T>(val value: T?, var operation: Operation) : Serializable{


    class Success<T>(value: T?, operation: Operation) : Result<T>(value, operation)

    class Failure<T>(val resultCode: Int?, val throwable: Throwable?, operation: Operation) : Result<T>(null, operation)


    val isSuccess: <PERSON>ole<PERSON> get() = this !is Failure

    val isFailure: <PERSON>olean get() = this is Failure

}

inline fun <T> Result<T>.onFailure(action: (resultCode: Int?, operation: Operation) -> Unit): Result<T> {
    if (this is Result.Failure) {
        action(resultCode, operation)
    }
    return this
}

inline fun <T> Result<T>.onSuccess(action: (value: T?, operation: Operation) -> Unit): Result<T> {
    if (isSuccess) {
        action(value, operation)
    }
    return this
}


/**
 * 操作类型，
 * 新进入页面， 下拉刷新， 加载跟多操作, 及同步是否收藏/是否已添加至歌单列表状态
 */
sealed class Operation {

    object NewData : Operation()

    object Refresh : Operation()

    object LoadMore : Operation()

    object UpdateStatus : Operation()
}
