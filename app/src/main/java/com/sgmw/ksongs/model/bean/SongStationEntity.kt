package com.sgmw.ksongs.model.bean

import com.chad.library.adapter.base.entity.MultiItemEntity

/**
 * 卡片数据bean
 */
class CardItem(override val title:String) :SongStationItem(){
     var url :String = ""
}

/**
 * 歌手数据bean
 */
class HotSingerItem(override val title:String) :SongStationItem(){
    var singerList: MutableList<SingerBean.Singer>? = null
}

/**
 * 排行榜数据bean
 */
class HotRankingsItem(override val title:String) :SongStationItem(){
    var hotSongList: MutableList<SongInfoBean>? = null
    var hotWeeklyList: MutableList<SongInfoBean>? = null
}

/**
 * 猜你喜欢数据bean
 */
class HotLikeItem(override val title:String) :SongStationItem(){
    var songList: MutableList<SongInfoBean>? = null
}

/**
 * 品味时代金曲数据bean
 */
class HotAgeItem(override val title:String) :SongStationItem()

/**
 * 热门专题数据bean
 */
class HotTopicsItem(override val title:String) :SongStationItem(){
    var songList: MutableList<HotTopicBean.Theme>? = null
}

open class SongStationItem {
    open val title :String = ""
}

class BaseMultiQuickItem(override val itemType: Int, val data: SongStationItem) : MultiItemEntity {
    companion object {
        // 点歌台卡片
        const val STATION_CARD = 0
        // 热门歌手
        const val HOT_SINGER = 1
        // 排行榜
        const val HOT_RANKINGS = 2
        // 猜你喜欢
        const val HOT_LIKE = 3
        // 品味时代金曲
        const val HOT_AGES = 4
        // 热门专题
        const val HOT_TOPICS = 5
    }
}