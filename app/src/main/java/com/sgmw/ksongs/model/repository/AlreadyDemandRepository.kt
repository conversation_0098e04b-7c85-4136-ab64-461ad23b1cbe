package com.sgmw.ksongs.model.repository

import com.sgmw.ksongs.db.entity.DemandSongInfo
import com.sgmw.ksongs.ui.playlist.PlayListManager

/**
 * @author: 董俊帅
 * @time: 2025/1/22
 * @desc:
 */
class AlreadyDemandRepository {

    suspend fun getDemandSongInfoList(): MutableList<DemandSongInfo> {
        return PlayListManager.getDemandSongInfoListWithOutPlaying()
    }

    suspend fun getPlayingSongInfo(): DemandSongInfo? {
        return PlayListManager.getPlayingSongInfo()
    }

    suspend fun deleteDemandSongInfo(songId: String) {
        PlayListManager.deleteDemandSongInfoById(songId)
    }

    suspend fun deleteAllDemandSongInfo() {
        PlayListManager.deleteAllDemandSongInfo()
    }

    suspend fun topDemandSongInfo(songInfo: DemandSongInfo) {
        PlayListManager.addTopSongInfo(songInfo)
    }

    suspend fun shuffleDemandList(){
        PlayListManager.shuffleDemandList()
    }

}