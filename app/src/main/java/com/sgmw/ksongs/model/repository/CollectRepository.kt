package com.sgmw.ksongs.model.repository

import androidx.lifecycle.LiveData
import com.sgmw.common.mvvm.m.BaseRepository
import com.sgmw.ksongs.db.DbManager
import com.sgmw.ksongs.db.entity.CollectSongInfo

// 最大收藏数量100
private const val MAX_COLLECT_COUNT: Int = 100

class CollectRepository : BaseRepository() {

    /**
     * 收藏歌曲：true:收藏成功， false: 收藏失败：数量超限
     */
    suspend fun insertOrUpdate(collectInfo: CollectSongInfo): Boolean {
        val savedCollectInfo = find(collectInfo.songInfo.song_id)
        if (savedCollectInfo == null) {
            if (getTotalCount() >= MAX_COLLECT_COUNT) {
                return false
            } else {
                insert(collectInfo)
            }
        } else {
            savedCollectInfo.insertTime = System.currentTimeMillis()
            update(savedCollectInfo)
        }
        return true
    }

    private suspend fun insert(collectInfo: CollectSongInfo) {
        DbManager.getCollectDao().insert(collectInfo)
    }

    suspend fun find(songId: String): CollectSongInfo? {
        return DbManager.getCollectDao().find(songId)
    }

    private suspend fun update(collectInfo: CollectSongInfo) {
        DbManager.getCollectDao().update(collectInfo)
    }

    fun findAll(): LiveData<List<CollectSongInfo>> {
        return DbManager.getCollectDao().findAll()
    }

    suspend fun findCollectedSongIds(): List<String> {
        return DbManager.getCollectDao().findCollectedSongIds()
    }

    suspend fun delete(list: List<CollectSongInfo>) {
        DbManager.getCollectDao().delete(list)
    }

    suspend fun delete(songId: String) {
        DbManager.getCollectDao().delete(songId)
    }

    private suspend fun getTotalCount(): Int {
        return DbManager.getCollectDao().getTotalCount()
    }

    fun getCount(): LiveData<Int> {
        return DbManager.getCollectDao().getCount()
    }

}