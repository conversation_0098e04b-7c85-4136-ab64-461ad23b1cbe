package com.sgmw.ksongs.model.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
class CategoryBean(
    val class_list: List<CategoryItemBean>
): Parcelable {

}

@Parcelize
class CategoryItemBean(
    val class_id: Int = 0,
    val class_name: String = "",
    val themes: ArrayList<CategoryThemeBean> = arrayListOf()
) : Parcelable {

}

@Parcelize
class CategoryThemeBean(
    val theme_id: Int = 0,
    val theme_name: String = "",
    val theme_pic: String = "",
    val theme_desc: String = "",
)  : Parcelable {

}