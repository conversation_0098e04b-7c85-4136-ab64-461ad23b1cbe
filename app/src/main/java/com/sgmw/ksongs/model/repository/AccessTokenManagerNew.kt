//package com.sgmw.ksongs.model.repository
//
//import android.util.Log
//import com.blankj.utilcode.util.GsonUtils
//import com.sgmw.common.utils.MMKVUtils
//import com.sgmw.ksongs.BuildConfig
//import com.sgmw.ksongs.api.LoginApi
//import com.sgmw.ksongs.model.bean.AccessTokenBean
//import com.sgmw.ksongs.model.bean.RefreshTokenBean
//import com.sgmw.ksongs.utils.sdk_init.APIHelper
//import com.tme.ktv.network.KtvRetrofitService
//import com.tme.ktv.network.core.TmeCall
//import com.tme.ktv.network.core.TmeCallback
//import kotlinx.coroutines.runBlocking
//import kotlinx.coroutines.suspendCancellableCoroutine
//import kotlin.coroutines.resume
//
///**
// * @author: 董俊帅
// * @time: 2025/1/18
// * @desc:
// */
//object AccessTokenManagerNew {
//
//    private const val MMKV_KEY_ACCESS_TOKEN = "access_token"
//    private const val MMKV_KEY_EXPIRES_IN = "expires_in"
//    private const val MMKV_KEY_REFRESH_TOKEN = "refresh_token"
//
//    // 4. 获取AccessToken（同步方法）
//    fun getAccessToken(): String? {
//        val accessToken = MMKVUtils.getString(MMKV_KEY_ACCESS_TOKEN, "")
//        val expiresIn = MMKVUtils.getInt(MMKV_KEY_EXPIRES_IN, -1)
//
//        // 如果access_token存在且未过期，直接返回
//        if (accessToken != null && System.currentTimeMillis() < expiresIn * 1000L) {
//            return accessToken
//        }
//
//        // 如果过期了，刷新access_token
//        val refreshedToken = refreshAccessToken()
//        return refreshedToken?.access_token
//    }
//
//    // 5. 获取AccessTokenBean（同步方法）
//    fun getAccessTokenBean(): AccessTokenBean? {
//        val accessToken = MMKVUtils.getString(MMKV_KEY_ACCESS_TOKEN, "")
//        return if (accessToken != null) {
//            GsonUtils.fromJson(accessToken, AccessTokenBean::class.java)
//        } else {
//            null
//        }
//    }
//
//    // 6. 是否登录
//    fun isLoggedIn(): Boolean {
//        val accessToken = MMKVUtils.getString(MMKV_KEY_ACCESS_TOKEN, "")
//        val expiresIn = MMKVUtils.getInt(MMKV_KEY_EXPIRES_IN, -1)
//
//        // 判断access_token是否存在且未过期
//        if (accessToken.isNullOrEmpty() || System.currentTimeMillis() > expiresIn * 1000L) {
//            // 如果access_token为空或过期，尝试刷新
//            val refreshedToken = refreshAccessToken()
//            return refreshedToken != null // 刷新失败则未登录
//        }
//        // 如果未过期，说明已经登录
//        return true
//    }
//
//    // 7. 刷新AccessToken
//    private fun refreshAccessToken(): RefreshTokenBean? {
//        val refreshToken = MMKVUtils.getString(MMKV_KEY_REFRESH_TOKEN, "") ?: return null
//
//        // 使用同步调用方式调用 Retrofit API
//        val refreshTokenBean = runBlocking {
//            fetchRefreshedToken()
//        }
//
//        if (refreshTokenBean.error_code == 0) {
//            // 更新缓存
//            MMKVUtils.putString(MMKV_KEY_ACCESS_TOKEN, refreshTokenBean.access_token)
//            MMKVUtils.putInt(MMKV_KEY_EXPIRES_IN, System.currentTimeMillis() + (refreshTokenBean.expires_in * 1000))
//            return refreshTokenBean
//        } else {
//            // 刷新失败
//            return null
//        }
//    }
//
//    // 8. 第一次获取AccessToken后保存到MMKV
//    fun initializeAccessToken() {
//        // 使用同步方式调用Retrofit接口
//        val response = runBlocking {
//            apiService.getAccessToken()
//        }
//
//        if (response.error_code.isNullOrEmpty()) {
//            mmkv.encode(MMKV_KEY_ACCESS_TOKEN, response.access_token)
//            mmkv.encode(MMKV_KEY_EXPIRES_IN, System.currentTimeMillis() + (response.expires_in * 1000))
//            mmkv.encode(MMKV_KEY_REFRESH_TOKEN, response.refresh_token)
//        }
//    }
//
//    // 模拟发起网络请求刷新 access_token
//    private suspend fun fetchRefreshedToken(): AccessTokenBean {
//        // 使用 suspendCoroutine 将回调转换为协程
//        return suspendCancellableCoroutine { continuation ->
//            // 发起网络请求
//            val call: TmeCall<RefreshTokenBean> =
//                KtvRetrofitService.get().getService(LoginApi::class.java).refreshToken(
//                    BuildConfig.KTV_SDK_APP_ID,
//                    accessTokenBean?.openid ?: "",
//                    accessTokenBean?.refresh_token ?: "",
//                    APIHelper.sign(),
//                    (System.currentTimeMillis() / 1000).toInt().toString()
//                )
//            call.enqueue(object : TmeCallback<RefreshTokenBean>() {
//                override fun onFail(call: TmeCall<*>?, throwable: Throwable) {
//                    Log.e(TAG, "fetchRefreshedToken error throwable: ${throwable.message}")
//                    // 请求失败，抛出异常并恢复协程
//                    continuation.resume(AccessTokenBean("", 0, "", -1, "", "", "", "", ""))
//                }
//
//                override fun onSuccess(call: TmeCall<*>?, refreshTokenBean: RefreshTokenBean?) {
//                    Log.d(
//                        TAG,
//                        "fetchRefreshedToken success refreshTokenBean: ${refreshTokenBean.toString()}"
//                    )
//                    // 请求成功，返回数据并恢复协程
//                    refreshTokenBean?.let {
//                        accessTokenBean?.refresh_token = refreshTokenBean.access_token
//                        accessTokenBean?.expires_in = refreshTokenBean.expires_in.toInt()
//                    }
//                    val result =
//                        accessTokenBean ?: AccessTokenBean("", 0, "", -1, "", "", "", "", "")
//                    continuation.resume(result)
//
//                }
//            })
//        }
//    }
//
//}