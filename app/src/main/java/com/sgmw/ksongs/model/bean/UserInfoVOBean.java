package com.sgmw.ksongs.model.bean;

/**
 * <AUTHOR>
 */
public class UserInfoVOBean {
    /**
     * nickname : llbuCyy7pn
     * mobile : 18172545799
     * photo :
     * userIdStr : MjE5NjAzOA==
     * creationDate : 1625043989000
     * sex : 0
     * register : 0
     */

    private String nickname;
    private String mobile;
    private String photo;
    private String userIdStr;
    private long creationDate;
    private int sex;
    private int register;

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public String getUserIdStr() {
        return userIdStr;
    }

    public void setUserIdStr(String userIdStr) {
        this.userIdStr = userIdStr;
    }

    public long getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(long creationDate) {
        this.creationDate = creationDate;
    }

    public int getSex() {
        return sex;
    }

    public void setSex(int sex) {
        this.sex = sex;
    }

    public int getRegister() {
        return register;
    }

    public void setRegister(int register) {
        this.register = register;
    }

    public String toString(){
        return "{\"nickname\":\""+nickname
                +"\",\"mobile\":\""+mobile
                +"\",\"photo\":\""+photo
                +"\",\"userIdStr\":\""+userIdStr
                +"\",\"creationDate\":"+creationDate
                +",\"sex\":"+0
                +",\"register\":"+register
                +"}";
    }
}
