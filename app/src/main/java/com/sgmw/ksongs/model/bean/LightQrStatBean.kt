package com.sgmw.ksongs.model.bean

import com.google.gson.annotations.SerializedName

data class LightQrStatBean(
    /**
     * 二维码状态
     * 11 待扫码
     * 12 已扫码，待确认
     * 13 已扫码，已确认 (只有此时会返回授权码，且此状态只触发一次)
     * 14 扫码完成登录
     */
    @SerializedName("stat")
    val stat: Int,
    /**
     * 授权码
     */
    @SerializedName("data")
    val data: String,
    /**
     * 扫码来源：
     * 0 未知
     * 1 全民K歌
     * 2 微信
     * 3 QQ
     */
    @SerializedName("scan_source")
    val scan_source: Int? = null,
    @SerializedName("error_code")
    val error_code: Int? = null,
    @SerializedName("error_msg")
    val error_msg: String? = null,
) {
    fun isWaitScan(): Boolean {
        return stat == 11
    }
}
