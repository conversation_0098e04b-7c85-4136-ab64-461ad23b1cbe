package com.sgmw.ksongs.model.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class HotTopicBean(
    val themes: MutableList<Theme>
) : Parcelable {

    @Parcelize
    data class Theme(
//        val songs: ArrayList<SongInfoBean>?,
        val theme_desc: String,
        val theme_id: Int,
        val theme_name: String,
        val theme_pic: String
    ) : Parcelable

}
