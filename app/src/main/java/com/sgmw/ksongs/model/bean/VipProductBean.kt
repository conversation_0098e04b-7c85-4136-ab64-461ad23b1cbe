package com.tme.ktv.demo.bean


data class VipProductBean(
    val account_id:String,
    val account_type:Int,
    val block_background_img:String,
    val block_main_tile:String,
    val block_sub_title:String,
    val cont_group_id:String,
    val curr_time:Int,
    val firstopen_group_id:String,
    val goods_list:MutableList<GoodsInfo>?,
    val vip_ad_list:MutableList<VipAdItem>?
){
    data class GoodsInfo(
        val bar_style:BarDisplay,
        val continuous_month_type:Int,
        val goods_desc:String,
        val goods_type:Int,
        val group_id:String,
        val is_firstopen:Boolean,
        val merger_qrcode:MergerQrCode,
        val pay_url:String,
        val price:String,
        val product_id:String,
        val redirect_uri:String,
        val sale_price:String,
        val sale_product_id:String,
    )
    data class BarDisplay(
        val continuous_month_type:Int,
        val goods_desc:String,
        val goods_type:Int,
        val group_id:String,
        val is_firstopen:Boolean
    )
    data class MergerQrCode(
        val code:String,
        val cookie_sig:String,
        val expires_in:Int,
        val sig:String
    )

    data class VipAdItem(
        val ad_id:Int,
        val focus_img:String,
        val type:Int,
        val unfocus_img:String
    )
}
