package com.sgmw.ksongs.model.repository

import com.sgmw.common.mvvm.m.BaseRepository
import com.sgmw.ksongs.api.BaseApi
import com.sgmw.ksongs.api.OperApi
import com.sgmw.ksongs.model.bean.RankingsBean
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.Result
import com.sgmw.ksongs.model.bean.SearchHotWordsBean
import com.sgmw.ksongs.model.bean.SearchResultBean
import com.sgmw.ksongs.model.bean.SongsBySingerBean
import com.sgmw.ksongs.utils.TmeCallbackImpl
import com.tme.ktv.network.KtvRetrofitService
import com.tme.ktv.network.core.TmeCall

class SearchRepository : BaseRepository() {

    private val TAG = SearchRepository::class.java.simpleName
    private val HOT_SEARCH_ID = "119"
    private val HOT_PLAY_LIST_TYPE = "toplist"

    companion object {
        const val DEFAULT_PAGE_NUM = 30
    }

    //歌曲搜索方式: 1-拼音首字母搜索 2-中英文搜索。
    enum class Action(var value: Int) {
        PINYIN(1),
        LETTER(2)
    }

    //搜索内容：1-歌曲(0x1) 2-歌手(0x10) 4-MV(0x100). 如要搜索歌曲+歌手，则content_flag=0x1|0x10=0x11=3
    enum class ContentFlag(var value: Int) {
        CONTENT_FLAG_SONG(1),
        CONTENT_FLAG_SINGER(2),
        CONTENT_FLAG_SINGER_SONG(3),
    }

    //按地区过滤歌手搜索结果; 0 -港台，1-内地，2 -日韩，3-欧美，4-其他，100-全部
    enum class FilterSingerArea(var value: Int) {
        FILTER_SINGER_AREA_GT(0),
        FILTER_SINGER_AREA_ND(1),
        FILTER_SINGER_AREA_RH(2),
        FILTER_SINGER_AREA_OM(3),
        FILTER_SINGER_AREA_OTHER(4),
        FILTER_SINGER_AREA_ALL(100)
    }

    //按类型过滤歌手搜索结果; 0-男，1-女，2-组合 ,100-为全部
    enum class FilterSingerType(var value: Int) {
        FILTER_SINGER_TYPE_M(0),
        FILTER_SINGER_TYPE_W(1),
        FILTER_SINGER_TYPE_G(2),
        FILTER_SINGER_TYPE_ALL(100)
    }

    fun getHotWords(operation: Operation, onRequestResult: (Result<SearchHotWordsBean?>) -> Unit) {
        val call: TmeCall<SearchHotWordsBean> =
            KtvRetrofitService.get().getService(OperApi::class.java).getHotWords()
        call.enqueue(TmeCallbackImpl(operation, onRequestResult))
    }

    fun getHotSearch(
        index: Int,
        num: Int,
        operation: Operation,
        onRequestResult: (hotRankingsBean: Result<RankingsBean?>) -> Unit
    ) {
        val call: TmeCall<RankingsBean> =
            KtvRetrofitService.get().getService(OperApi::class.java).getMultiPlaylist(index, num, HOT_SEARCH_ID, HOT_PLAY_LIST_TYPE)
        call.enqueue(TmeCallbackImpl(operation, onRequestResult))
    }

    fun getSearchResult(
        action: Int,
        content_flag: Int,
        filter_singer_area: Int,
        filter_singer_type: Int,
        start_page: Int,
        page_num: Int,
        word: String,
        operation: Operation,
        onRequestResult: (searchResultBean: Result<SearchResultBean?>) -> Unit
    ) {
        val call: TmeCall<SearchResultBean> =
            KtvRetrofitService.get().getService(BaseApi::class.java)
                .getSearchResult(
                    action,
                    content_flag,
                    filter_singer_area,
                    filter_singer_type,
                    start_page,
                    page_num,
                    word
                )
        call.enqueue(TmeCallbackImpl(operation, onRequestResult))
    }

    fun getSongsBySinger(
        index: Int,
        page_num: Int,
        query: String,
        singer_id: String,
        start_page: Int,
        operation: Operation,
        onRequestResult: (songsBySingerBean: Result<SongsBySingerBean?>) -> Unit
    ) {
        val call: TmeCall<SongsBySingerBean> =
            KtvRetrofitService.get().getService(BaseApi::class.java)
                .getSongsBySinger(index, page_num, query, singer_id, start_page)

        call.enqueue(TmeCallbackImpl(operation, onRequestResult))
    }

}