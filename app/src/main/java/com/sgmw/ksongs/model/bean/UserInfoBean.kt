package com.sgmw.ksongs.model.bean

data class UserInfoBean(
    val account_type: Int,
    val kid: String,
    val user_avatar: String,
    val user_id: String,
    val user_nick: String
) {
    override fun toString(): String {
        return "UserInfoBean(" +
                "account_type=$account_type, " +
                "kid='$kid', " +
                "user_id='$user_id' " +
                ")"
    }
}