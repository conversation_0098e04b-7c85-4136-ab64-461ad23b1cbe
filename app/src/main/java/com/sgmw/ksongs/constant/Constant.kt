package com.sgmw.ksongs.constant

/**
 * @author: 董俊帅
 * @time: 2025/2/18
 * @desc: 常量
 */
object Constant {
    /**
     * 传递SongInfo的Intent KEY
     */
    const val KEY_SONG_INFO = "song_info"
    const val KEY_PLAY_PAGE_STATE = "play_page_state"
    const val KEY_CHANNEL_TYPE = "channel_type"
    const val KEY_ACTION_TYPE = "action_type"
    const val KEY_NEED_REPLAY = "need_replay"
    const val KEY_NEED_SHOW_PLAYING = "need_show_playing"

    /**
     * MV歌词模式
     */
    const val LYRIC_KTV_MODEL = 1

    /**
     * 纯歌词模式
     */
    const val LYRIC_LINES_MODEL = 2

    /**
     * 默认升降调
     */
    const val DEFAULT_TONE_PITCH_SHIFT = 0

    /**
     * 默认伴奏音量
     */
    const val DEFAULT_ACCOMPANY_VOLUME = 15

    /**
     * 默认麦克风音量
     */
    const val DEFAULT_MIC_VOLUME = 20

    /**
     * 亮度最大值
     */
    const val MAX_BRIGHTNESS = 255

    /**
     * 伴奏声音最大值
     */
    const val MAX_VOLUME = 40



    /**
     * 档位
     * $0=Initialize(初始化)
     * $6=error( 故障)
     * $a=Park gear(P档)
     * $b=Sport Gear(S档)
     * $c=Drive Gear(D档)
     * $d=Neutral Gear(空档)
     * $e=Reverse Gear(倒档)
     */
    const val GEAR_INITIALIZE: Int = '0'.code
    const val GEAR_PARK: Int = 'a'.code
    const val GEAR_SPORT: Int = 'b'.code
    const val GEAR_DRIVE: Int = 'c'.code
    const val GEAR_NEUTRAL: Int = 'd'.code
    const val GEAR_REVERSE: Int = 'e'.code
    const val GEAR_ERROR: Int = '6'.code

    const val MAX_TAB_COUNT =  6
    // 播放mv
    const val ACTION_TYPE_PLAY = 0
    // 下一首mv
    const val ACTION_TYPE_PLAY_NEXT = 1


}