package com.sgmw.ksongs.constant

/**
 * @author: 董俊帅
 * @time: 2025/1/18
 * @desc:
 */
object MMKVConstant {

    /** 是否同意隐私协议 */
    const val IS_AGREE_PRIVACY = "is_agree_privacy"

    const val SEARCH_HISTORY = "search_history"

    /** 是否开启伴唱 */
    const val VOCALS_OPEN_STATUE = "vocals_open_statue"

    /** 是否开启MV */
    const val MV_OPEN_STATUE = "mv_open_statue"

    /** 是否开启打分 */
    const val SCORE_OPEN_STATUE = "score_open_statue"

    /** 是否打开音准器 */
    const val PITCHER_OPEN_STATUE = "pitcher_open_statue"

    /** 播放页面亮度 */
    const val PLAY_SCREEN_BRIGHTNESS = "play_screen_brightness"

    /** 伴奏音量 */
    const val ACCOMPANY_VOLUME = "accompany_volume"

    /** 麦克风音量 */
    const val MIC_VOLUME = "mic_volume"

    /** 播放升降调 */
    const val TONE_PITCH_SHIFT = "tone_pitch_shift"

    /** 当前账号是否是VIP */
    const val IS_VIP = "is_vip"

}