<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:sharedUserId="android.uid.system">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.WAKE_LOCK"/>

    <uses-permission android:name="android.car.permission.CAR_POWER" />
    <uses-permission android:name="android.car.permission.CAR_POWERTRAIN" />
    <uses-permission android:name="android.car.permission.CAR_CONTROL_AUDIO_SETTINGS" />
    <uses-permission android:name="android.car.permission.CONTROL_CAR_SEATS" />
    <uses-permission android:name="android.car.permission.CAR_VENDOR_EXTENSION" />

    <uses-permission android:name="android.car.permission.CAR_CONTROL_AUDIO_VOLUME" />
    <uses-permission android:name="android.car.permisssion.CAR_DYNAMICS_STATE" />
    <uses-permission android:name="android.car.permisssion.CAR_DRIVING_STATE"/>
    <uses-permission android:name="android.car.permisssion.CONTROL_CAR_EXTERIOR_LIGHTS" />
    <uses-permission android:name="android.car.permission.CAR_EXTERIOR_LIGHTS" />
    <uses-permission android:name="android.car.permission.CAR_INFO" />
    <uses-permission android:name="android.car.permission.CAR_TIRES" />
    <uses-permission android:name="android.car.permission.CAR_ENERGY" />
    <uses-permission android:name="android.car.permission.BIND_CAR_INPUT_SERVICE" />

<!--    网络状态相关权限-->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission-group.MICROPHONE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>

    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.BROADCAST_STICKY" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />

    <application
        android:name=".application.MyApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:persistent="true"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:theme="@style/Theme.SGMWKSongs"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:hardwareAccelerated="true"
            android:configChanges="orientation|screenSize|keyboardHidden|layoutDirection|uiMode|smallestScreenSize|screenLayout|density"
            android:label="@string/app_name">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <receiver android:name=".phonestatus.PhoneStatusReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.bluetooth.headsetclient.profile.action.AG_CALL_CHANGED"/>
                <action android:name="android.bluetooth.adapter.action.CONNECTION_STATE_CHANGED" />
                <action android:name="android.bluetooth.adapter.action.STATE_CHANGED" />
            </intent-filter>
        </receiver>


        <meta-data
            android:name="design_width_in_dp"
            android:value="1920"/>
        <meta-data
            android:name="design_height_in_dp"
            android:value="1080"/>
    </application>

</manifest>