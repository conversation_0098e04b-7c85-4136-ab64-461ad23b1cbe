apply plugin: 'org.jetbrains.kotlin.android'
apply from: rootProject.file('customize_gradles/app.build.gradle')

// 是否使用leakcanary 检测内存泄漏
boolean isUseLeakCanary = false
import com.autoai.car.buildsrc.Libs
import com.autoai.car.buildsrc.Versions
def DD_APK_BUILD_NUMBER
def DD_APK_VERSION
def DD_APK_NAME
def DD_APK_VERSION_CODE
def propFile = file('../build.number')
if (propFile.canRead()) {
    def props = new Properties()
    props.load(new FileInputStream(propFile))
    if (props != null && props.containsKey('DD_APK_VERSION')
            && props.containsKey('DD_APK_BUILD_NUMBER')) {
        DD_APK_BUILD_NUMBER="${props['DD_APK_BUILD_NUMBER']}"
        DD_APK_VERSION="${props['DD_APK_VERSION']}"
        DD_APK_NAME="${props['DD_APK_NAME']}"
        DD_APK_VERSION_CODE = "${props['DD_APK_VERSION_CODE']}"
    }
    println("DD_APK_BUILD_NUMBER:${DD_APK_BUILD_NUMBER}")
    println("DD_APK_VERSION:${DD_APK_VERSION}")
    println("DD_APK_NAME:${DD_APK_NAME}")
    println("DD_APK_VERSION_CODE:${DD_APK_VERSION_CODE}")
}


android {
    namespace Versions.APPLICATION_ID
    compileSdk Versions.COMPILE_SDK

    defaultConfig {
        applicationId Versions.APPLICATION_ID
        minSdk Versions.MIN_SDK
        targetSdk Versions.TARGET_SDK
        versionCode "${DD_APK_VERSION_CODE}".toInteger()
        versionName "${DD_APK_VERSION}.${DD_APK_BUILD_NUMBER}"

        ndk {
            abiFilters 'armeabi', 'armeabi-v7a', 'x86'
        }

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        buildConfigField("String", "KTV_SDK_APP_ID", "\"100117\"")
        buildConfigField("String", "KTV_SDK_APP_KEY", "\"3600824fd368f3029783cd40480e54f8\"")
        buildConfigField("String", "KTV_SDK_TEST_APP_KEY", "\"3600824fd368f3029783cd40480e54f8\"")
        buildConfigField("Boolean", "IS_PHONE", "false")  //是否模拟器调试
    }

    applicationVariants.all { variant ->
        def assembleTask = project.tasks.getByName("assemble${variant.name.capitalize()}")
        println(assembleTask.getName())
        assembleTask.doLast { ->
            println("doLast")
            def flavor = variant.name.replace("Release", "").replace("Debug", "")
            println(flavor)
            def srcPath = "${project.buildDir}${File.separator}outputs${File.separator}apk${File.separator}${flavor}${File.separator}${variant.buildType.name}"
            println(srcPath)

            copy {
                from(srcPath) {
                    include '*.apk'
                }
                into "${project.rootDir}${File.separator}out"
                println("${project.rootDir}${File.separator}out")
                rename {
                    "${DD_APK_NAME}-${variant.buildType.name}.apk"
                }
            }
        }

        variant.outputs.all { output ->
            //def outputFile = output.outputFile
            //if (outputFile != null && outputFile.name.endsWith('.apk')) {
            def fileName = "${DD_APK_NAME}-${variant.buildType.name}.apk"
            println("fileName:${output.outputFile}")
            outputFileName = fileName
            //}
        }

        assembleTask.doLast { ->

            //export mapping
            def mappingPath="${project.buildDir}${File.separator}outputs${File.separator}mapping${File.separator}${variant.name}"
            if (file(mappingPath).exists()){
                copy {
                    from(mappingPath) {
                        include '*'
                    }
                    into "${project.rootDir}${File.separator}out${File.separator}mapping${File.separator}${variant.name}"
                }
            }
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    packagingOptions {
        exclude 'META-INF/com.android.tools/proguard/coroutines.pro'

        //mmkv、unify库冲突问题解决
        pickFirst 'lib/arm64-v8a/libc++_shared.so'
        pickFirst 'lib/x86/libc++_shared.so'
        pickFirst 'lib/armeabi/libc++_shared.so'
        pickFirst 'lib/armeabi-v7a/libc++_shared.so'
    }

    signingConfigs {
        debug {
            storeFile file('../sign/platform.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
        release {
            storeFile file('../sign/platform.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
    }
    apply plugin: 'com.sensorsdata.analytics.android'

}

//为了让加入的aar可以更快生效，非必须
configurations.all {
    // check for updates every build
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}

repositories {
    flatDir {
        dirs 'libs'
    }
}

dependencies {
    implementation project(':common')

//    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.6.1'
//    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1'
    //页面导航框架
    implementation Libs.Navigation.NavigationFragment
    implementation Libs.Navigation.NavigationUiKtx
    implementation Libs.AndroidCar
    implementation Libs.PermissionClient
    // 数据库依赖
    implementation Libs.Room.roomKtx
    implementation Libs.Room.roomRuntime
    kapt Libs.Room.roomCompiler

    implementation Libs.FlexBox
    implementation Libs.SmartRefreshLayout.base
    implementation Libs.SmartRefreshLayout.classicsHeader
    implementation Libs.SmartRefreshLayout.classicFooter
    implementation Libs.SmartRefreshLayout.horizontal

    implementation Libs.VrSdk
    if(isUseLeakCanary){
        debugImplementation Libs.leakcanary
    }
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'

    implementation fileTree(dir: 'libs', include: ['*.aar'])

    compileOnly Libs.sw_framework
}