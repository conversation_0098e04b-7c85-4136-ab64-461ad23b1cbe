/**
 * 读取本地properties配置文件中定义的属性
 * @params key 属性名称
 * @params defaultValue 属性默认值
 * @params annotation 注释说明
 * @params fileName properties配置文件名称
 * @params fileName writeInto第一次去读，如果配置文件中没有此属性，是否按照默认值写入
 * */
static String getLocalProperty(Project project, String key, def defaultValue, def annotation = '#', String fileName = 'local.properties', boolean writeInto = true) {
    def desFile = project.rootProject.file(fileName)
    boolean createAndWrite = false
    def value
    if (desFile.exists()) {
        InputStream inputStream = desFile.newDataInputStream()
        Properties properties = new Properties()
        properties.load(inputStream)
        value = properties.getProperty(key)
        inputStream.close()
        //value 不为空 说明文件中有此参数
        if (value) {
            defaultValue = value
        } else {
            //文件中没有此参数时写入此参数,默认值为false
            properties.setProperty(key, "${defaultValue}")
            createAndWrite = true
        }
    } else {
        createAndWrite = true
    }
    if (createAndWrite && writeInto) {
        def content = "$key=$defaultValue"
        //true不覆盖已有内容
        def fos = new FileOutputStream(fileName, true)
        // 写入一个换行
        fos.write("\r\n".getBytes())
        //写入
        fos.write("#$annotation".getBytes())
        // 写入一个换行
        fos.write("\r\n".getBytes())
        //写入
        fos.write(content.getBytes())
        fos.flush()
        fos.close()
    }
    project.getLogger().log(LogLevel.LIFECYCLE, "$fileName --> $key : $defaultValue")
    "$defaultValue"
}

ext.getLocalProperty = this.&getLocalProperty
