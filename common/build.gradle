import com.autoai.car.buildsrc.Libs

apply plugin: 'org.jetbrains.kotlin.android'

apply from: rootProject.file('customize_gradles/module.build.gradle')
android {
    namespace 'com.sgmw.common'
    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    api Libs.androidAutoSize
    api project(':skin:autoinflater')
    api project(':skin:skincore:adapters:skinandroidx')
    api project(':skin:skincore:adapters:skinglideadapter')
    api project(':skin:skincore:skinframework')
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}