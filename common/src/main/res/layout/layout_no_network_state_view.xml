<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">


    <ImageView
        android:id="@+id/ivError"
        android:src="@mipmap/icon_no_net"
        android:layout_width="360dp"
        android:layout_height="160dp"
        android:layout_marginTop="160dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <TextView
        android:id="@+id/tvNoNetWorkHint"
        app:layout_constraintStart_toStartOf="@+id/ivError"
        app:layout_constraintEnd_toEndOf="@+id/ivError"
        app:layout_constraintTop_toBottomOf="@+id/ivError"
        android:layout_marginHorizontal="16dp"
        android:textSize="28sp"
        android:layout_marginTop="6dp"
        android:text="@string/no_network_hint"
        android:textColor="@color/color_262E33_FFFFFF"
        android:gravity="center_horizontal"
        android:layout_width="0dp"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/tvErrorRetry"
        app:layout_constraintStart_toStartOf="@+id/ivError"
        app:layout_constraintEnd_toEndOf="@+id/ivError"
        app:layout_constraintTop_toBottomOf="@+id/tvNoNetWorkHint"
        android:background="@drawable/selector_btn_bg_rect_20_1c7dff"
        android:textSize="26sp"
        android:gravity="center"
        android:textColor="@drawable/selector_btn_color_ffffff"
        android:text="@string/retry"
        android:layout_marginTop="46dp"
        android:includeFontPadding="false"
        android:layout_width="180dp"
        android:layout_height="72dp"/>


</androidx.constraintlayout.widget.ConstraintLayout>