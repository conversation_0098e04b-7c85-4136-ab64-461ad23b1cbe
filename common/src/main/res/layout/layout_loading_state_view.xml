<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">


    <ImageView
        android:id="@+id/ivLoading"
        tools:src="@mipmap/loading_00"
        android:layout_width="156dp"
        android:layout_height="156dp"
        android:layout_marginTop="357dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <TextView
        android:id="@+id/tvLoadingHint"
        app:layout_constraintStart_toStartOf="@+id/ivLoading"
        app:layout_constraintEnd_toEndOf="@+id/ivLoading"
        app:layout_constraintTop_toBottomOf="@+id/ivLoading"
        android:layout_marginHorizontal="16dp"
        android:textSize="28sp"
        android:layout_marginTop="6dp"
        android:text="@string/loading_hint"
        android:textColor="@color/color_262E33_FFFFFF"
        android:gravity="center_horizontal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>


</androidx.constraintlayout.widget.ConstraintLayout>