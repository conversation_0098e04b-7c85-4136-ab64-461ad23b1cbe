<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/ivEmpty"
        android:src="@mipmap/icon_empty"
        android:layout_width="360dp"
        android:layout_height="160dp"
        android:layout_marginTop="160dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <TextView
        android:id="@+id/tvEmptyDataHint"
        app:layout_constraintStart_toStartOf="@+id/ivEmpty"
        app:layout_constraintEnd_toEndOf="@+id/ivEmpty"
        app:layout_constraintTop_toBottomOf="@+id/ivEmpty"
        android:layout_marginHorizontal="16dp"
        android:textSize="28sp"
        android:layout_marginTop="6dp"
        android:text="@string/no_data"
        android:textColor="@color/color_262E33_FFFFFF"
        android:gravity="center_horizontal"
        android:layout_width="0dp"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/tvEmpty"
        app:layout_constraintStart_toStartOf="@+id/ivEmpty"
        app:layout_constraintEnd_toEndOf="@+id/ivEmpty"
        app:layout_constraintTop_toBottomOf="@+id/tvEmptyDataHint"
        android:background="@drawable/selector_btn_bg_rect_20_80d9e0ea_1afff"
        android:drawableLeft="@mipmap/icon_search_no_data"
        android:drawablePadding="8dp"
        android:paddingLeft="16dp"
        android:textSize="26sp"
        android:includeFontPadding="false"
        android:gravity="center_vertical"
        android:textColor="@drawable/selector_btn_color_66262e33_66ffffff"
        android:text="@string/go_song"
        android:layout_marginTop="46dp"
        android:layout_width="180dp"
        android:layout_height="56dp"/>


</androidx.constraintlayout.widget.ConstraintLayout>