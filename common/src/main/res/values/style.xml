<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="ResourceName,SpUsage">

<!--    &lt;!&ndash;自定义dialog背景全透明无边框theme &ndash;&gt;-->
<!--    <style name="common_dialog" parent="android:style/Theme.Dialog">-->
<!--        &lt;!&ndash;        &lt;!&ndash;背景颜色及和透明程度&ndash;&gt;&ndash;&gt;-->
<!--        &lt;!&ndash;        <item name="android:windowBackground">@android:color/transparent</item>&ndash;&gt;-->
<!--        &lt;!&ndash;是否去除标题 &ndash;&gt;-->
<!--        <item name="android:windowNoTitle">true</item>-->
<!--        &lt;!&ndash;是否去除边框&ndash;&gt;-->
<!--        <item name="android:windowFrame">@null</item>-->
<!--        &lt;!&ndash;是否浮现在activity之上&ndash;&gt;-->
<!--        <item name="android:windowIsFloating">true</item>-->
<!--        &lt;!&ndash;是否暗化背景&ndash;&gt;-->
<!--        &lt;!&ndash;        <item name="android:backgroundDimEnabled">true</item>&ndash;&gt;-->
<!--        &lt;!&ndash; 是否半透明 &ndash;&gt;-->
<!--        &lt;!&ndash;        <item name="android:windowIsTranslucent">true</item>&ndash;&gt;-->
<!--        &lt;!&ndash; 打开模糊效果 &ndash;&gt;-->
<!--        <item name="android:windowBlurBehindEnabled" tools:ignore="NewApi">true</item>-->
<!--        &lt;!&ndash; 设置模糊半径 &ndash;&gt;-->
<!--        <item name="android:windowBlurBehindRadius" tools:ignore="NewApi">@dimen/common_blur_radius</item>-->
<!--        &lt;!&ndash; Dialog进入及退出动画 &ndash;&gt;-->
<!--        <item name="android:windowAnimationStyle">@style/ActionSheetDialogAnimation</item>-->


<!--        <item name="android:background">#00000000</item>-->
<!--        <item name="android:fitsSystemWindows">false</item>-->
<!--        <item name="android:windowIsTranslucent">false</item>-->
<!--        <item name="android:windowBackground">@null</item>-->
<!--        <item name="android:backgroundDimEnabled">false</item>-->
<!--    </style>-->

        <style name="common_dialog" parent="android:style/Theme.Dialog">
            <item name="android:background">#00000000</item>
            <item name="android:windowNoTitle">true</item>
            <item name="android:windowIsFloating">true</item>
            <item name="android:fitsSystemWindows">false</item>
            <item name="android:windowIsTranslucent">false</item>
            <item name="android:windowBackground">@null</item>
            <item name="android:backgroundDimEnabled">false</item>
            <!-- Dialog进入及退出动画 -->
            <item name="android:windowAnimationStyle">@null</item>
        </style>

    <!-- ActionSheet进出动画 -->
    <style name="ActionSheetDialogAnimation" parent="@android:style/Animation.Dialog" tools:ignore="ResourceName">
        <item name="android:windowEnterAnimation">@anim/actionsheet_dialog_in</item>
        <item name="android:windowExitAnimation">@anim/actionsheet_dialog_out</item>
    </style>






    <!-- 自定义dialog主题，不带模糊效果-->
    <style name="common_dialog_no_blur" parent="android:style/Theme.Dialog">
        <!--背景颜色及和透明程度-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!--是否去除标题 -->
        <item name="android:windowNoTitle">true</item>
        <!--是否去除边框-->
        <item name="android:windowFrame">@null</item>
        <!--是否浮现在activity之上-->
        <item name="android:windowIsFloating">true</item>
        <!--是否暗化背景-->
        <item name="android:backgroundDimEnabled">false</item>
        <!-- 是否半透明 -->
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <!-- 自定义dialog主题，不带模糊效果，不暗化背景-->
    <style name="common_dialog_no_blur_no_dim" parent="android:style/Theme.Dialog">
        <!--背景颜色及和透明程度-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!--是否去除标题 -->
        <item name="android:windowNoTitle">true</item>
        <!--是否去除边框-->
        <item name="android:windowFrame">@null</item>
        <!--是否浮现在activity之上-->
        <item name="android:windowIsFloating">true</item>
        <!--是否模糊-->
        <item name="android:backgroundDimEnabled">false</item>
        <!-- 是否半透明 -->
        <item name="android:windowIsTranslucent">true</item>
    </style>


</resources>