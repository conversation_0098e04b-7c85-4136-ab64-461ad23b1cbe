<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="ResourceName">

    <declare-styleable name="StateLayout">
        <attr name="loadingResId" format="reference" />
        <attr name="emptyResId" format="reference" />
        <attr name="errorResId" format="reference" />
        <attr name="noNetWorkResId" format="reference" />
        <attr name="defaultShowLoading" format="boolean" />
        <attr name="loadingIvTopMargin" format="dimension" />
        <attr name="emptyIvTopMargin" format="dimension" />
        <attr name="errorIvTopMargin" format="dimension" />
        <attr name="emptyDataHintTxt" format="reference" />
        <attr name="showEmptyBtn" format="boolean" />

    </declare-styleable>

    <declare-styleable name="CustomRecyclerView">
        <attr name="enableBottomFadeEffect" format="boolean" />
    </declare-styleable>


</resources>