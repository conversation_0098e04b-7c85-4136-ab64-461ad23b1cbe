package com.sgmw.common

import android.annotation.SuppressLint
import android.content.Context
import androidx.multidex.MultiDexApplication

/**
 * @author: 董俊帅
 * @time: 2025/3/3
 * @desc: BaseApplication
 */
open class BaseApplication : MultiDexApplication() {

    companion object {
        const val TAG = "BaseApplication"

        // 私有静态变量，避免外部直接访问和修改
        private var appInstance: BaseApplication? = null

        /**
         * 获取Application实例
         * @return BaseApplication实例，如果未初始化则抛出异常
         */
        val application: BaseApplication
            get() = appInstance ?: throw IllegalStateException("BaseApplication not initialized")

        /**
         * 获取Application Context，确保返回的是ApplicationContext而不是Activity Context
         * @return ApplicationContext，安全的全局Context
         */
        val context: Context
            get() = application.applicationContext
    }

    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(base)
        // 安全地初始化Application实例，避免Context泄漏
        appInstance = this
    }


}