package com.sgmw.common.ktx
import androidx.annotation.StringDef
import java.text.SimpleDateFormat
import java.util.*

/**
 * 时间格式化规则 yyyy-MM-dd HH:mm:ss
 */
const val PATTERN_YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss"

/**
 * 时间格式化规则 yyyyMMddHHmmss
 */
const val PATTERN_YYYYMMDDHHMMSS = "yyyyMMddHHmmss"

/**
 * 时间格式化规则 MMDD
 */
const val PATTERN_MMDD = "MMdd"

/**
 * 时间格式化规则 YYYYMMDD
 */
const val PATTERN_YYYYMMDD = "YYYYMMdd"

@Retention(AnnotationRetention.SOURCE)
@StringDef(value = [PATTERN_YYYY_MM_DD_HH_MM_SS, PATTERN_YYYYMMDDHHMMSS, PATTERN_MMDD, PATTERN_YYYYMMDD])
internal annotation class Pattern

/**
 * 依照格式规则，将date结果的时间字符串
 *
 * @param pattern 规则
 * @return 转换结果
 */
fun Date.date2second(@Pattern pattern: String): String =
    SimpleDateFormat(pattern, Locale.getDefault()).format(this)

/**
 * 依照格式规则，将date结果的时间字符串
 *
 * @param pattern 规则
 * @return 转换结果
 */
fun Long.time2Format(@Pattern pattern: String): String =
    SimpleDateFormat(pattern, Locale.getDefault()).format(Date(this))