package com.sgmw.common.ktx

import android.view.View
import android.widget.TextView
import androidx.core.content.ContextCompat
import android.content.Context
import android.widget.ImageView
import androidx.annotation.DimenRes
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.sgmw.common.R

/**
 * @author: 董俊帅
 * @time: 2025/3/1
 * @desc: View的一些扩展方法
 */

// OnClickListener 接口
fun View.setOnSingleClickListener(
    interval: Long = 500,  // 新增参数：可自定义间隔（默认 500ms）
    listener: View.OnClickListener
) {
    setOnClickListener(SingleClickListener(interval, listener))
}

// Lambda 表达式 方式
fun View.setOnSingleClickListener(
    interval: Long = 500,  // 新增参数：可自定义间隔（默认 500ms）
    listener: (View) -> Unit
) {
    setOnClickListener(SingleClickListener(interval, View.OnClickListener { listener(it) }))
}

// 内部实现类（带间隔参数）
private class SingleClickListener(
    private val interval: Long,         // 动态接收间隔时间
    private val originListener: View.OnClickListener
) : View.OnClickListener {

    private var lastClickTime = 0L

    override fun onClick(v: View) {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastClickTime >= interval) {  // 使用动态间隔
            lastClickTime = currentTime
            originListener.onClick(v)
        }
    }
}

/**
 * 为TextView动态设置Drawable ---Start || Left
 */
fun TextView.setDrawableStart(resId: Int) {
    val drawable = ContextCompat.getDrawable(context, resId)
    drawable?.setBounds(0, 0, drawable.intrinsicWidth, drawable.intrinsicHeight)
    setCompoundDrawables(drawable, null, null, null)
}

/**
 * 为TextView动态设置Drawable ---Top
 */
fun TextView.setDrawableTop(resId: Int) {
    val drawable = ContextCompat.getDrawable(context, resId)
    drawable?.setBounds(0, 0, drawable.intrinsicWidth, drawable.intrinsicHeight)
    setCompoundDrawables(null, drawable, null, null)
}

/**
 * 为TextView动态设置Drawable ---End || Right
 */
fun TextView.setDrawableEnd(resId: Int) {
    val drawable = ContextCompat.getDrawable(context, resId)
    drawable?.setBounds(0, 0, drawable.intrinsicWidth, drawable.intrinsicHeight)
    setCompoundDrawables(null, null, drawable, null)
}

/**
 * 为TextView动态设置Drawable ---Bottom
 */
fun TextView.setDrawableBottom(resId: Int) {
    val drawable = ContextCompat.getDrawable(context, resId)
    drawable?.setBounds(0, 0, drawable.intrinsicWidth, drawable.intrinsicHeight)
    setCompoundDrawables(null, null, null, drawable)
}

fun ImageView.loadCornerImage(
    url: String,
    @DimenRes radius: Int = R.dimen.dp_10 // 假设你已经在dimens.xml中定义了默认值
) {
    // 从dimens资源中获取px值
    val cornerRadiusPx = context.resources.getDimensionPixelSize(radius)

    val requestOptions = RequestOptions()
        .transform(RoundedCorners(cornerRadiusPx))

    Glide.with(context)
        .load(url)
        .apply(requestOptions)
        .into(this)
}


