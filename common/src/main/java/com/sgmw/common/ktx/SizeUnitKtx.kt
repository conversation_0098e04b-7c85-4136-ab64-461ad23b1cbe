package com.sgmw.common.ktx

import android.content.Context
import androidx.fragment.app.Fragment

/**
 * @Author: Qu<PERSON><PERSON><PERSON>huo
 * @Time: 2020/9/17
 * @Class: SizeUnitKtx
 * @Remark: 尺寸单位换算相关扩展属性
 */

/**
 * dp 转 px
 */
fun Context.dp2px(dpValue: Float): Int {
    val scale = resources.displayMetrics.density
    return (dpValue * scale + 0.5f).toInt()
}

/**
 * px 转 dp
 */
fun Context.px2dp(pxValue: Float): Int {
    val scale = resources.displayMetrics.density
    return (pxValue / scale + 0.5f).toInt()
}

/**
 * sp 转 px
 */
fun Context.sp2px(spValue: Float): Int {
    val scale = resources.displayMetrics.scaledDensity
    return (spValue * scale + 0.5f).toInt()
}

/**
 * px 转 sp
 */
fun Context.px2sp(pxValue: Float): Int {
    val scale = resources.displayMetrics.scaledDensity
    return (pxValue / scale + 0.5f).toInt()
}

/**
 * dp 转 px
 */
fun Fragment.dp2px(dpValue: Float): Int {
    val scale = resources.displayMetrics.density
    return (dpValue * scale + 0.5f).toInt()
}

/**
 * px 转 dp
 */
fun Fragment.px2dp(pxValue: Float): Int {
    val scale = resources.displayMetrics.density
    return (pxValue / scale + 0.5f).toInt()
}

/**
 * sp 转 px
 */
fun Fragment.sp2px(spValue: Float): Int {
    val scale = resources.displayMetrics.scaledDensity
    return (spValue * scale + 0.5f).toInt()
}

/**
 * px 转 sp
 */
fun Fragment.px2sp(pxValue: Float): Int {
    val scale = resources.displayMetrics.scaledDensity
    return (pxValue / scale + 0.5f).toInt()
}

// 伴奏音量设置，将0~39的音量值映射到0~1的范围
fun Int.convertVolumeToSdkRange(): Float {
    // 限制输入范围在0到39之间
    val clampedVolume = this.coerceIn(0, 39)
    // 将0~39的音量值映射到0~1的范围
    return clampedVolume / 39f
}

// 麦克风音量设置，将1~39的音量值映射到0~1的范围
fun Int.convertMicVolumeToSdkRange(): Float {
    // 限制输入范围在1到39之间
    val clampedVolume = this.coerceIn(1, 39)
    // 将0~39的音量值映射到0~1的范围
    return clampedVolume / 39f
}

fun Float.convertSdkRangeToVolume(): Int {
    // 将0~1的范围映射到0~39的音量值
    return (this * 39).toInt()
}