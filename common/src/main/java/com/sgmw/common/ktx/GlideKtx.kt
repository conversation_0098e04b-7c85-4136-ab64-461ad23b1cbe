package com.sgmw.common.ktx

import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.widget.ImageView
import androidx.annotation.DrawableRes

import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.DecodeFormat
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.Transformation
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.load.resource.bitmap.DownsampleStrategy
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target
import com.sgmw.common.BaseApplication
import com.sgmw.common.config.MemoryOptimizationConfig
import com.sgmw.common.utils.Log
import com.sgmw.common.utils.RoundCorner
import com.sgmw.common.utils.mainLaunch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File


private fun handleImageLoadPerformance(src: Any?, width: Int, height: Int) {
//    if (com.autoai.car.frame.base.BuildConfig.PERFORMANCE_TEST_MODE) {
//        // 记录视图绘制完成的时间
//        val srcString = when (src) {
//            is String, is Int, is File -> src.toString()
//            is Drawable -> "Drawable"
//            is Bitmap -> "Bitmap"
//            null -> "null"
//            else -> "Unknown type"
//        }
//        TimeMeasureUtil.logTime(TimeMeasurer.TestCase.PAGE_IMAGE_LOAD, "image", false)
//        TimeMeasureUtil.endTimeWithTrace(TimeMeasurer.TestCase.GLIDE_IMAGE_LOAD, srcString)
//
//        Log.d("TimeMeasure", "Image loaded, size: ${width}x${height}")
//        if (width > 700 || height > 512) {
//            Log.e("TimeMeasure", "Image loaded, path: ${width}x${height} path: $srcString")
//        }
//    }
}

/**
 * 加载路径地址
 *
 * @param iv 加载的图片视图
 */
fun String.loadImg(iv: ImageView) {
    Glide.with(iv.context)
        .load(this)
        .apply(RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL))
        .into(iv)
}

/**
 * 加载本地资源
 *
 * @param iv 加载的图片视图
 */
fun Int.loadImg(iv: ImageView) {
    Glide.with(iv.context)
        .load(this)
        .apply(RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL))
        .into(iv)
}

/**
 * 加载本地资源
 *
 * @param path 加载图片路径
 */
fun ImageView.loadImg(path: Any?,
                      onImageSuccess: (() -> Unit)? = null, // 加载成功回调
                      onImageError: (() -> Unit)? = null,
                      roundCorner: Float = 0f) {

    val requestOptions = RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL).transform(
        MultiTransformation(
            CenterCrop(),
            RoundCorner(context, roundCorner, roundCorner, roundCorner, roundCorner)

        )
    )

    val request = Glide.with(this.context)
        .load(path)
        .apply(requestOptions)
    val onSuccess: (Int, Int) -> Unit = { width, height ->
        handleImageLoadPerformance(path ?: "", width, height)
        if(onImageSuccess != null) onImageSuccess()
    }
    //失败再试一次，解决class java.io.IOException: File unsuitable for memory mapping,本地读缓存引起的失败
    val onFailure: () -> Unit = {
        mainLaunch {
            request.listener(GlideLoadListener(onSuccess) { if (onImageError != null) onImageError() })
                .skipMemoryCache(true).diskCacheStrategy(DiskCacheStrategy.NONE)
                .into(this)
        }
    }
    request.listener(GlideLoadListener(onSuccess,onFailure))
            .into(this)
}

fun ImageView.loadRoundImage(
    url: Any, leftTop: Float = 0f,
    rightTop: Float = 0f,
    rightBottom: Float = 0f,
    leftBottom: Float = 0f
) {
//    val request = Glide.with(context).load(url).apply(
//        RequestOptions().transform(
//            MultiTransformation(
//                CenterCrop(),
//                RoundCorner(context, leftTop, rightTop, rightBottom, leftBottom)
//            )
//        )
//    )

    val request = Glide.with(context).load(url)
    val onSuccess: (Int, Int) -> Unit = { width, height ->
        handleImageLoadPerformance(url, width, height)
    }
    //失败再试一次，解决class java.io.IOException: File unsuitable for memory mapping,本地读缓存引起的失败
    val onFailure: () -> Unit = {
        mainLaunch {
            request.listener(null)
                .skipMemoryCache(true).diskCacheStrategy(DiskCacheStrategy.NONE)
                .into(this)
        }
    }
    request.listener(GlideLoadListener(onSuccess,onFailure))
            .into(this)
}

/**
 * 加载圆角图片
 * @param src : url:String
 *              resourceId:Int
 *              drawableImage:Drawable
 *
 * @param roundCorner 四个角的弧度半径
 */
fun ImageView.loadRoundImage(
    src: Any, roundCorner: Float = 0f, @DrawableRes  placeHolderResId: Int = 0
) {
    try {
        //    TimeMeasureUtil.startTimeWithTrace(TimeMeasurer.TestCase.GLIDE_IMAGE_LOAD, src.toString())
        val requestOptions = RequestOptions()
            .format(DecodeFormat.PREFER_RGB_565) // 使用RGB_565格式减少内存占用
            .downsample(DownsampleStrategy.AT_MOST) // 降采样策略
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .transform(
                MultiTransformation(
                    CenterCrop(),
                    RoundCorner(context, roundCorner, roundCorner, roundCorner, roundCorner)
                )
            )

        if(placeHolderResId != 0){
            requestOptions.placeholder(placeHolderResId)  // 如果placeholder有有效的resId, 则设置placeholder
        }

        val request = Glide.with(context)
            .load(src)
            .apply(requestOptions)

        val onSuccess: (Int, Int) -> Unit = { width, height ->
            handleImageLoadPerformance(src, width, height)
        }

        //失败再试一次，解决class java.io.IOException: File unsuitable for memory mapping,本地读缓存引起的失败
        val onFailure: () -> Unit = {
            mainLaunch {
                request.listener(null)
                    .skipMemoryCache(true).diskCacheStrategy(DiskCacheStrategy.NONE)
                    .into(this)
            }
        }

        request.listener(GlideLoadListener(onSuccess,onFailure))
            .into(this)

    } catch (e: OutOfMemoryError) {
        android.util.Log.e("GlideKtx", "OutOfMemoryError loading round image: $src", e)
        // 执行紧急内存清理
        System.gc()
        try {
            // 使用最小配置重新加载
            val fallbackOptions = RequestOptions()
                .diskCacheStrategy(DiskCacheStrategy.NONE)
                .skipMemoryCache(true)
                .format(DecodeFormat.PREFER_RGB_565)
                .override(MemoryOptimizationConfig.GlideConfig.OOM_RECOVERY_SIZE,
                         MemoryOptimizationConfig.GlideConfig.OOM_RECOVERY_SIZE)
                .transform(
                    MultiTransformation(
                        CenterCrop(),
                        RoundCorner(context, roundCorner, roundCorner, roundCorner, roundCorner)
                    )
                )

            if(placeHolderResId != 0){
                fallbackOptions.placeholder(placeHolderResId)
            }

            Glide.with(context)
                .load(src)
                .apply(fallbackOptions)
                .into(this)
        } catch (e2: Exception) {
            android.util.Log.e("GlideKtx", "Failed to load round image after OOM recovery: $src", e2)
            // 最后的备用方案：显示占位图
            if (placeHolderResId != 0) {
                setImageResource(placeHolderResId)
            }
        }
    } catch (e: Exception) {
        android.util.Log.e("GlideKtx", "Error loading round image: $src", e)
        if (placeHolderResId != 0) {
            setImageResource(placeHolderResId)
        }
    }
}

/**
 * 加载图片
 * @param src 图片地址
 * @param roundCorner 圆角
 * @param placeHolderResId 默认占位图
 * @param transform 白天黑夜
 */
fun ImageView.loadRoundImage(
    src: Any, roundCorner: Float = 0f, @DrawableRes  placeHolderResId: Int = 0, transform: Transformation<Bitmap>
) {

//    TimeMeasureUtil.startTimeWithTrace(TimeMeasurer.TestCase.GLIDE_IMAGE_LOAD, src.toString())
    val requestOptions = RequestOptions().transform(
        MultiTransformation(
            CenterCrop(),
            RoundCorner(context, roundCorner, roundCorner, roundCorner, roundCorner),
            transform
        )
    )

    if(placeHolderResId != 0){
        requestOptions.placeholder(placeHolderResId)  // 如果placeholder有有效的resId, 则设置placeholder
    }

    val request = Glide.with(context)
        .load(src)
        .apply(requestOptions)

    val onSuccess: (Int, Int) -> Unit = { width, height ->
        handleImageLoadPerformance(src, width, height)
    }

    //失败再试一次，解决class java.io.IOException: File unsuitable for memory mapping,本地读缓存引起的失败
    val onFailure: () -> Unit = {
        mainLaunch {
            request.listener(null)
                .skipMemoryCache(true).diskCacheStrategy(DiskCacheStrategy.NONE)
                .into(this)
        }
    }

    request.listener(GlideLoadListener(onSuccess,onFailure))
        .into(this)

}

fun ImageView.loadRoundImageWithDefault(
    src: Any?,
    roundCorner: Float = 0f,
    @DrawableRes  placeHolderResId: Int = 0,
    onImageSuccess: () -> Unit, // 加载成功回调
    onImageError: () -> Unit//加载失败回调，对应使用了默认图片的场景
) {
    if (placeHolderResId == 0 && src == null) {
        return
    }
    val requestOptions = RequestOptions().transform(
        MultiTransformation(
            CenterCrop(),
            RoundCorner(context, roundCorner, roundCorner, roundCorner, roundCorner)

        )
    )
    val request = Glide.with(context)
        .load(src ?: placeHolderResId)
        .apply(requestOptions)
        .placeholder(placeHolderResId)
    val onSuccess: (Int, Int) -> Unit = { width, height ->
        handleImageLoadPerformance(src, width, height)
        if(onImageSuccess != null) onImageSuccess()
    }
    //失败再试一次，解决class java.io.IOException: File unsuitable for memory mapping,本地读缓存引起的失败
    val onFailure: () -> Unit = {
        mainLaunch {
            request.listener(GlideLoadListener(onSuccess) { onImageError() })
                .skipMemoryCache(true).diskCacheStrategy(DiskCacheStrategy.NONE)
                .into(this)
        }
    }
    request.listener(GlideLoadListener(onSuccess) { onFailure() })
            .into(this)
}

fun printFailCallback( p0: GlideException?,
                   p1: Any?,
                   p2: Target<Drawable>,
                   p3: Boolean){
    val printMsg = StringBuilder()
    printMsg.append("onLoadFailed,")
    p0.let {
        printMsg.append(it.toString())
            .append(",")
    }
    if(p1 is String){
        printMsg.append("src:")
            .append(p1)
            .append(",")
    }
    printMsg.append("time:")
        .append(System.currentTimeMillis())
        .append(",")

    Log.i("glideCallback",printMsg.toString())
}

fun printSucCallback(p0: Drawable,
                     p1: Any,
                     p2: Target<Drawable>?,
                     p3: DataSource,
                     p4: Boolean){
    val printMsg = StringBuilder()
    printMsg.append("onResourceReady,")
    if(p1 is String){
        printMsg.append("src:")
            .append(p1)
            .append(",")
    }
    printMsg.append("source:")
        .append(p3)
        .append(",")
        .append("time:")
        .append(System.currentTimeMillis())
}

fun ImageView.loadRoundImageWithCallback(
    src: Any, roundCorner: Float = 0f, @DrawableRes  placeHolderResId: Int = 0
) {

    val requestOptions = RequestOptions().transform(
        MultiTransformation(
            CenterCrop(),
            RoundCorner(context, roundCorner, roundCorner, roundCorner, roundCorner)

        )
    )

    if(placeHolderResId != 0){
        requestOptions.placeholder(placeHolderResId)  // 如果placeholder有有效的resId, 则设置placeholder
    }

    if(src is String){
        Log.i("glideCallback","imgSrc:" + src + ",time:" + System.currentTimeMillis())
    }
    val request = Glide.with(context)
        .load(src)
        .timeout(10000)
        .apply(requestOptions)

    val onSuccess: (Int, Int) -> Unit = { width, height ->
        handleImageLoadPerformance(src, width, height)
    }
    //失败再试一次，解决class java.io.IOException: File unsuitable for memory mapping,本地读缓存引起的失败
    val onFailure: () -> Unit = {
        mainLaunch {
            request.listener(null)
                .skipMemoryCache(true).diskCacheStrategy(DiskCacheStrategy.NONE)
                .into(this)
        }
    }
    request.listener(GlideLoadListener(onSuccess) { onFailure() })
            .into(this)
}


fun ImageView.loadRoundImage(
    src: Any, roundCorner: Float = 0f, transform: Transformation<Bitmap>
) {
    val request = Glide.with(context).load(src).apply(
        RequestOptions().transform(
            MultiTransformation(
                CenterCrop(),
                RoundCorner(context, roundCorner, roundCorner, roundCorner, roundCorner),
                transform
            )
        )
    )
    val onSuccess: (Int, Int) -> Unit = { width, height ->
        handleImageLoadPerformance(src, width, height)
    }
    //失败再试一次，解决class java.io.IOException: File unsuitable for memory mapping,本地读缓存引起的失败
    val onFailure: () -> Unit = {
        mainLaunch {
            request.listener(null)
                .skipMemoryCache(true).diskCacheStrategy(DiskCacheStrategy.NONE)
                .into(this)
        }
    }
    request.listener(GlideLoadListener(onSuccess) { onFailure() })
            .into(this)
}

fun ImageView.loadRoundImage(
    src: Any, placeHolderResId: Int = 0
) {
    val request = Glide.with(context).load(src).apply(
        RequestOptions().transform(
            MultiTransformation(CircleCrop())
        ).placeholder(placeHolderResId)
    )
    val onSuccess: (Int, Int) -> Unit = { width, height ->
        handleImageLoadPerformance(src, width, height)
    }
    //失败再试一次，解决class java.io.IOException: File unsuitable for memory mapping,本地读缓存引起的失败
    val onFailure: () -> Unit = {
        mainLaunch {
            request.listener(null)
                .skipMemoryCache(true).diskCacheStrategy(DiskCacheStrategy.NONE)
                .into(this)
        }
    }
    request.listener(GlideLoadListener(onSuccess) { onFailure() })
            .into(this)
}

fun ImageView.loadRoundImageWithCallback(
    src: Any, placeHolderResId: Int = 0
) {
    if(src is String){
        Log.i("glideCallback","imgSrc:" + src + ",time:" + System.currentTimeMillis())
    }
    val request = Glide.with(context).load(src)
        .timeout(10000)
        .apply(
        RequestOptions().transform(
            MultiTransformation(CircleCrop())
        ).placeholder(placeHolderResId)
    )
    val onSuccess: (Int, Int) -> Unit = { width, height ->
        handleImageLoadPerformance(src, width, height)
    }
    //失败再试一次，解决class java.io.IOException: File unsuitable for memory mapping,本地读缓存引起的失败
    val onFailure: () -> Unit = {
        mainLaunch {
            request.listener(null)
                .skipMemoryCache(true).diskCacheStrategy(DiskCacheStrategy.NONE)
                .into(this)
        }
    }
    request.listener(GlideLoadListener(onSuccess) { onFailure() })
            .into(this)
}

/**
 * 加载本地资源
 *
 * @param path 加载图片路径
 * @param placeHolderResId 占位图路径
 */
fun ImageView.loadImgWithPlaceHolder(path: Any?, @DrawableRes placeHolderResId: Int) {
    val request = Glide.with(this.context)
        .load(path)
        .placeholder(placeHolderResId)
        .apply(RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL))

    val onSuccess: (Int, Int) -> Unit = { width, height ->
        handleImageLoadPerformance(path, width, height)
    }
    //失败再试一次，解决class java.io.IOException: File unsuitable for memory mapping,本地读缓存引起的失败
    val onFailure: () -> Unit = {
        mainLaunch {
            request.listener(null)
                .skipMemoryCache(true).diskCacheStrategy(DiskCacheStrategy.NONE)
                .into(this)
        }

    }
    request.listener(GlideLoadListener(onSuccess) { onFailure() })
            .into(this)
}

/**
 * 加载本地资源
 *
 * @param path 加载图片路径
 * @param errorResId 异常图片
 */
fun ImageView.loadImgWithError(path: Any?, @DrawableRes errorResId: Int) {
    Glide.with(this.context)
        .load(path)
        .error(errorResId)
        .apply(RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL))
        .into(this)
}

fun getFolderSize(folder: File?): Float {
    var size: Long = 0
    if (folder != null && folder.isDirectory) {
        val files = folder.listFiles()
        if (files != null) {
            for (file in files) {
                size += if (file.isFile) file.length() else getFolderSize(file).toLong()
            }
        }
    }
    return size.toFloat() / (1024 * 1024) // 返回 MB 值
}

fun getGlideCacheInfo(context: Context): String {
    val cacheDir = Glide.getPhotoCacheDir(context)
    val cacheSize = getFolderSize(cacheDir)
    return "%.2fMB".format(cacheSize) // 返回带有单位的字符串形式
}

fun clearGlideCache(context: Context) {
    GlobalScope.launch(Dispatchers.IO) {
        Glide.get(context).clearDiskCache()
        withContext(Dispatchers.Main) {
            Glide.get(context).clearMemory()
        }
    }
}

fun ImageView.loadImageOrGifByExtension(context: Context, url: String, imageView: ImageView) {
    val extension = url.substringAfterLast(".", "")
    val isGif = extension.equals("gif", true)
    if (isGif) {
        Glide.with(context).asGif()
            .load(url)
            .apply(
                RequestOptions()
                    .diskCacheStrategy(DiskCacheStrategy.ALL)

            )
            .into(imageView)
    } else {
        Glide.with(context)
            .load(url)
            .apply(
                RequestOptions()
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
            )
            .into(imageView)
    }
}
