package com.sgmw.common.mvvm.m



/**
 * Base repository
 *
 * @constructor Create empty Base repository
 */
open class BaseRepository {

    companion object {
        private const val TAG = "BaseRepository"
        private const val NOT_NETWORK_MSG = "Not Network"
    }
//    /**
//     * 发起请求封装
//     * 该方法将flow的执行切换至IO线程
//     *
//     * @param requestBlock 请求的整体逻辑
//     * @return Flow<T> @BuilderInference block: suspend FlowCollector<T>.() -> Unit
//     */
//    protected fun <T> request(requestBlock: suspend FlowCollector<T>.() -> Unit): Flow<T> {
//        return flow(block = requestBlock).flowOn(Dispatchers.IO).catch {
//            Log.d(TAG, "request: err=${it.message}")
//        }
//    }
//
//    /**
//     * 发起请求封装
//     * 该方法将flow的执行切换至IO线程
//     *
//     * @param requestBlock 请求的整体逻辑
//     * @return Flow<T> @BuilderInference block: suspend FlowCollector<T>.() -> Unit
//     */
//    protected fun <T> request(requestBlock: suspend FlowCollector<T>.() -> Unit,isNeedOutCatch : Boolean): Flow<T> {
//        if(isNeedOutCatch){
//            return flow(block = requestBlock).flowOn(Dispatchers.IO)
//        }else{
//            return request(requestBlock)
//        }
//    }
//
//    /**
//     * 确保网络正常再发送网路请求
//     * @param requestBlock 请求代码块
//     * @param errorResponse 无网或请求失败代码块
//     */
//    protected fun <T> performNetworkRequest(
//        requestBlock: suspend FlowCollector<T>.() -> Unit,
//        errorResponse: suspend FlowCollector<T>.(msg: String) -> Unit
//    ): Flow<T> {
//        return flow {
//            if (!NetworkStateClient.isConnected()) {
//                Log.w(TAG, "performNetworkRequest: err=$NOT_NETWORK_MSG")
//                errorResponse(NOT_NETWORK_MSG)
//            } else {
//                requestBlock()
//            }
//        }.catch { error ->
//            Log.i(TAG, "performNetworkRequest: err=${error.message}")
//            errorResponse(error.message ?: "")
//        }.flowOn(Dispatchers.IO)
//    }

}