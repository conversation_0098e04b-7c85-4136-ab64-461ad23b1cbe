package com.sgmw.common.mvvm.v
import android.app.ActivityOptions
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.PersistableBundle
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import androidx.viewbinding.ViewBinding
import com.sgmw.common.mvvm.vm.BaseViewModel
import com.sgmw.common.utils.BindingReflex
import com.sgmw.common.utils.EventBusUtils
import com.sgmw.common.utils.Log
import com.sgmw.common.utils.RegisterEventBus
import com.sgmw.common.utils.network.AutoRegisterNetListener
import com.sgmw.common.utils.network.NetworkStateChangeListener
import com.sgmw.common.utils.network.NetworkTypeEnum
import java.lang.reflect.ParameterizedType


/**
 * Base frame activity
 *
 * @param VB
 * @param VM
 * @constructor Create empty Base frame activity
 */
abstract class BaseFrameActivity<VB : ViewBinding, VM : BaseViewModel> : AppCompatActivity(),
    FrameView<VB>, NetworkStateChangeListener {

     val mBinding: VB by lazy(mode = LazyThreadSafetyMode.NONE) {
        Log.i(TAG, "reflexViewBinding  before")
        BindingReflex.reflexViewBinding(javaClass, layoutInflater)
    }

    private val TAG = BaseFrameActivity::class.java.simpleName

    //    protected val mBinding: VB = BindingReflex.reflexViewBinding(javaClass, layoutInflater)
//    protected lateinit var mBinding: VB

    protected  var mViewModel: VM? = null

    // 界面是否在前台，用于前台显示相关提示。
    var isInForeground = false

    /**
     * 是否有 [RegisterEventBus] 注解，避免重复调用 [Class.isAnnotation]
     */
    private var mHaveRegisterEventBus = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.i(TAG, "onCreate 1")
        setContentView(mBinding.root)
        Log.i(TAG, "onCreate  2")
        // 根据子类是否有 RegisterEventBus 注解決定是否进行注册 EventBus
        if (javaClass.isAnnotationPresent(RegisterEventBus::class.java)) {
            mHaveRegisterEventBus = true
            EventBusUtils.register(this)
        }
        mViewModel = createViewModel()
        Log.i(TAG, "onCreate  3")
        setStatusBar()
        Log.i(TAG, "onCreate  4")
        mBinding.initView()
        Log.i(TAG, "onCreate  5")
        initNetworkListener()
        initObserve()
        initRequestData()
        Log.i(TAG, "onCreate  6")
    }

    /**
     * 创建viewModel
     */
    private fun createViewModel(): VM {
        val vm:Class<VM> = (javaClass.genericSuperclass as ParameterizedType).actualTypeArguments[1] as Class<VM>
        return ViewModelProvider(this).get(vm)
    }
    override fun onRestoreInstanceState(
        savedInstanceState: Bundle?,
        persistentState: PersistableBundle?
    ) {
        super.onRestoreInstanceState(savedInstanceState, persistentState)
        Log.i(TAG, "onRestoreInstanceState  6")
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)

        outState.putInt("ActivityState", 1)
        Log.i(TAG, "onSaveInstanceState  6")
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        Log.i(TAG, "onRestoreInstanceState  6")
    }

    override fun onNewIntent(intent: Intent?) {
        Log.i(TAG, "onNewIntent")
        super.onNewIntent(intent)
    }

    override fun onPause() {
        Log.i(TAG, "onPause")
        isInForeground = false
        super.onPause()
    }

    override fun onResume() {
        Log.i(TAG, "onResume")
        isInForeground = true
        super.onResume()
    }

    override fun onStop() {
        Log.i(TAG, "onStop")
        super.onStop()
    }


    /**
     * 初始化网络状态监听
     * @return Unit
     */
    private fun initNetworkListener() {
        lifecycle.addObserver(AutoRegisterNetListener(this))
    }

    /**
     * 设置状态栏
     * 子类需要自定义时重写该方法即可
     * @return Unit
     */
    open fun setStatusBar() {}

    /**
     * 网络类型更改回调
     * @param type Int 网络类型
     * @return Unit
     */
    override fun networkTypeChange(type: NetworkTypeEnum) {}

    /**
     * 网络连接状态更改回调
     * @param isConnected Boolean 是否已连接
     * @return Unit
     */
    override fun networkConnectChange(isConnected: Boolean) {
    }

    override fun onDestroy() {
        Log.i(TAG, "onDestroy  1")
        // 根据子类是否有 RegisterEventBus 注解决定是否进行注册 EventBus
        if (mHaveRegisterEventBus) {
            EventBusUtils.unRegister(this)
        }
        super.onDestroy()
    }


}