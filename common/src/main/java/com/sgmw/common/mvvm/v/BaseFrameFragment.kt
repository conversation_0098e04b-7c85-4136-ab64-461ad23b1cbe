package com.sgmw.common.mvvm.v

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.viewbinding.ViewBinding
import com.autoai.baseline.support.skincore.SkinManager
import com.sgmw.common.dialog.IDialogDismissInterface
import com.sgmw.common.mvvm.vm.BaseViewModel
import com.sgmw.common.utils.BindingReflex
import com.sgmw.common.utils.EventBusUtils
import com.sgmw.common.utils.Log
import com.sgmw.common.utils.RegisterEventBus
import com.sgmw.common.utils.StateLayoutEnum
import com.sgmw.common.utils.network.AutoRegisterNetListener
import com.sgmw.common.utils.network.NetworkStateChangeListener
import com.sgmw.common.utils.network.NetworkTypeEnum
import com.sgmw.common.widget.StateLayout
import java.lang.reflect.ParameterizedType


/**
 * Fragment基类
 *
 * <AUTHOR> Yunshuo
 * @since 8/27/20
 */
abstract class BaseFrameFragment<VB : ViewBinding, VM : BaseViewModel> : Fragment(),
    FrameView<VB>,
    NetworkStateChangeListener,
    IDialogDismissInterface {
    private val TAG = BaseFrameFragment::class.java.simpleName
    private var _binding: VB? = null
     val mBinding get() = _binding
    protected val mBindingOrNull get() = _binding
    protected var mViewModel: VM? = null

    private var isViewCreated = false
    protected var isViewModelCreated: Boolean = false
    private var mStateLayout : StateLayout? = null

    /**
     * 获取StateLayoutView
     */
    open fun getStateLayoutView(): StateLayout?{
        return  mStateLayout
    }

    open fun needViewState() = true

    /**
     * 换肤时否主动刷新一下布局
     */
    open fun needSkinApply() = false

    override fun onResume() {
        super.onResume()
        Log.d(TAG,"onResume" + this::class.java.simpleName)
    }

    override fun onPause() {
        super.onPause()
        Log.d(TAG,"onPause" + this::class.java.simpleName)
    }

    override fun onStop() {
        super.onStop()
        Log.d(TAG,"onStop" + this::class.java.simpleName)
    }

    override fun onStart() {
        if (needSkinApply()){
            mBinding?.let {
                SkinManager.getInstance().applyView(it.root)
            }
        }
        super.onStart()
        Log.d(TAG,"onStart" + this::class.java.simpleName)

    }
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        Log.i(TAG, "onCreateView reflexViewBinding  before " + this::class.java.simpleName )
        _binding = BindingReflex.reflexViewBinding(javaClass, layoutInflater)
        Log.i(TAG, "onCreateView reflexViewBinding  after" + this::class.java.simpleName )
        if (savedInstanceState != null) {
            isViewCreated = savedInstanceState.getBoolean("isViewCreated", false)
        }
        if (needViewState()){
            mStateLayout =  replaceView()
            if (getLoadViewRoot() == _binding?.root){
                return mStateLayout
            }
        }
        return _binding?.root
    }

    /**
     * 重置多状态布局
     */
    fun reSetStateLayout(){
        if (needViewState()){
            mStateLayout =  replaceView()
        }
    }

    /**
     * 对指定view 包裹StateLayout
     */
    private fun replaceView() :StateLayout?{
        getLoadViewRoot()?.let {
            val oldContent = it
            if (oldContent.parent == null) {
                Log.d(TAG, "replaceView oldContent.parent  ${oldContent.parent}")
                val loadLayout = StateLayout(oldContent.context)
                setStateLayoutParameters(loadLayout)
                loadLayout.setupSuccessLayout(oldContent)
                return loadLayout
            }
            if (oldContent.parent is StateLayout){
                val parent = oldContent.parent as StateLayout
                setStateLayoutParameters(parent)
                return oldContent.parent as StateLayout
            }
            Log.d(TAG, "replaceView2 oldContent.parent  ${oldContent.parent}")
            val contentParent = oldContent.parent as ViewGroup
            var childIndex = 0
            val childCount = contentParent?.childCount ?: 0
            for (i in 0 until childCount) {
                if (contentParent.getChildAt(i) === oldContent) {
                    childIndex = i
                    break
                }
            }
            Log.d(TAG, "replaceView childIndex = ${childIndex}")
            contentParent?.removeView(oldContent)
            val oldLayoutParams = oldContent.layoutParams
            val loadLayout = StateLayout(oldContent.context)
            setStateLayoutParameters(loadLayout)
            loadLayout.setupSuccessLayout(oldContent)
            contentParent?.addView(loadLayout, childIndex, oldLayoutParams)
            return loadLayout
        }
        return null
    }

    /**
     * 设置stateLayout的参数 自定义相关参数
     */
    open fun setStateLayoutParameters(stateLayout:StateLayout){

    }

    open fun getLoadViewRoot(): View? {
        return null
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        lifecycle.addObserver(AutoRegisterNetListener(this))
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Log.i(TAG, "onCreateView onViewCreated  1" + this::class.java.simpleName )

        var fragmentName = this::class.java.simpleName
        isViewCreated = true
        mViewModel = createViewModel()
        isViewModelCreated = true
        if (javaClass.isAnnotationPresent(RegisterEventBus::class.java)) {
            EventBusUtils.register(this)
        }

        Log.i(TAG, "onCreateView onViewCreated 2"+ this::class.java.simpleName )
        _binding?.initView()
        Log.i(TAG, "onCreateView onViewCreated  3" + this::class.java.simpleName )
        initObserve()
        Log.i(TAG, "onCreateView onViewCreated  4"+ this::class.java.simpleName )
        initRequestData()
        Log.i(TAG, "onCreateView onViewCreated  5"+ this::class.java.simpleName )
    }

    /**
     * 创建viewModel
     */
    private fun createViewModel(): VM {
        val vm:Class<VM> = (javaClass.genericSuperclass as ParameterizedType).actualTypeArguments[1] as Class<VM>

        if (useActivityForViewModelLifecycle()) {
            return ViewModelProvider(requireActivity()).get(vm)
        }
        return ViewModelProvider(this).get(vm)
    }

    /**
     * 是否使用 Activity 作为 ViewModel 的生命周期，默认为 false
     */
    open fun useActivityForViewModelLifecycle(): Boolean = false

    override fun onDestroyView() {
        super.onDestroyView()
        Log.i(TAG, "onCreateView onDestroyView  1"+ this::class.java.simpleName )
        // eventbus的解除注册应和注册时的生命周期对应，否则导致各种问题
        if (javaClass.isAnnotationPresent(RegisterEventBus::class.java)) {
            EventBusUtils.unRegister(this)
        }
        _binding = null
        Log.i(TAG, "onCreateView onDestroyView  2"+ this::class.java.simpleName )
        isViewCreated = false
    }

    override fun onDestroy() {
        Log.i(TAG, "onCreateView onDestroy "+ this::class.java.simpleName )
        super.onDestroy()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        Log.i(TAG, "onCreateView onSaveInstanceState "+ this::class.java.simpleName )
        outState.putBoolean("isViewCreated", isViewCreated)
    }

    /**
     * 网络类型更改回调
     * @param type Int 网络类型
     * @return Unit
     */
    override fun networkTypeChange(type: NetworkTypeEnum) {}

    /**
     * 网络连接状态更改回调
     * @param isConnected Boolean 是否已连接
     * @return Unit
     */
    override fun networkConnectChange(isConnected: Boolean) {
//        toast(if (isConnected) "网络已连接" else "网络已断开")
    }


    // 判断页面是否有销毁，有一些页面回调任务需要判断（VideoDetailFragment）
    fun isBindingDestroy(): Boolean {
        return _binding == null
    }

    fun getViewModel(): BaseViewModel? {
        return mViewModel
    }

    fun isActivityDestroy(): Boolean {
        return activity == null || activity?.isDestroyed == true
    }

    private fun getParentTAG(): String = this::class.java.simpleName

    /**
     * 关闭Dialog（不会检测当前Activity是否销毁，一般用在onDestroy中关闭dialog）
     */
    override fun dismissDialog(dialog: Dialog?) {
        if (dialog?.isShowing == true) {
            Log.i(getParentTAG(), "Base# dismissDialog: dialog=$dialog")
            dialog.dismiss()
        }
    }

    /**
     * 安全的关闭Dialog（关闭前会检测当前Activity是否已经销毁）
     */
    override fun dismissDialogSafely(dialog: Dialog?) {
        if (isActivityDestroy()) {
            Log.i(getParentTAG(), "Base# dismissDialog: activity is destroy, dialog=$dialog")
            return
        }
        dismissDialog(dialog)
    }

    override fun dismissDialogFragment(dialog: DialogFragment?) {
        if (dialog?.isAdded == true) {
            Log.i(getParentTAG(), "Base# dismissDialogFragment: dialog=$dialog")
            dialog.dismiss()
        }
    }

    override fun dismissDialogFragmentSafely(dialog: DialogFragment?) {
        if (isActivityDestroy()) {
            Log.i(getParentTAG(), "Base# dismissDialogFragmentSafely: activity is destroy, fragment=${dialog}")
            return
        }
        dismissDialogFragment(dialog)
    }

    open fun onDisplayChangeAction(isDSeat  : Boolean) {

    }

    override fun initObserve() {
        mViewModel?.stateViewLD?.observe(viewLifecycleOwner, Observer {
            Log.d(TAG, "initObserve: state = $it")
            when (it) {
                StateLayoutEnum.LOADING -> {
                    showLoading()
                }

                StateLayoutEnum.NO_DATA -> {
                    showEmpty()
                }

                StateLayoutEnum.ERROR -> {
                    showError()
                }

                StateLayoutEnum.SUCCESS -> {
                    showSuccess()
                }

                StateLayoutEnum.NO_NETWORK -> {
                    showNoNetwork()
                }

                else -> {
                    Log.e(TAG, "initObserve: state  is error")
                }
            }
        })

    }

    open fun showNoNetwork() {
        getStateLayoutView()?.let {
            it.showError()
            it.setErrorRetryClickListener {
                errorReload()
            }
       }
    }

    /**
     * 展示成功的页面
     */
    open fun showSuccess() {
        Log.d(TAG, "showSuccess == ")
        getStateLayoutView()?.showContent()
    }

    /**
     * 展示错误 网络错误/网络异常页面
     */
    open fun showError() {
        getStateLayoutView()?.let {
            it.showError()
            it.setErrorRetryClickListener {
                errorReload()
            }
        }
    }

    /**
     * 展示空页面
     */
    open fun showEmpty() {
        getStateLayoutView()?.let {
            it.showEmpty()
            it.setEmptyClickListener {
                emptyReload()
            }
        }
    }

    /**
     * 空页面中重新加载
     */
    open fun emptyReload(){
        initRequestData()
    }

    /**
     * 错误页面中点击重新加载
     */
    open fun errorReload() {
        initRequestData()
    }

    /**
     * 展示loading
     */
    open fun showLoading() {
        Log.d(TAG,"showLoading == ")
        getStateLayoutView()?.showLoading()
    }
}
