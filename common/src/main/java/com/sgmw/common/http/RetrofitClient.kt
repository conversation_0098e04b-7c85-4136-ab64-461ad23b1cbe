package com.sgmw.common.http

import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.Executors
import java.util.concurrent.ThreadFactory
import java.util.concurrent.atomic.AtomicInteger

/**
 * @author: 董俊帅
 * @time: 2025/1/20
 * @desc:
 */

object RetrofitClient {

    private const val BASE_URL = "https://api.kg.qq.com/test/"  // 替换成你的API根地址

    // 创建守护线程的线程池，避免阻塞应用关闭
    private val daemonThreadFactory = object : ThreadFactory {
        private val counter = AtomicInteger(0)
        override fun newThread(r: Runnable): Thread {
            val thread = Thread(r, "OkHttp-Daemon-${counter.incrementAndGet()}")
            // 关键：设置为守护线程
            thread.isDaemon = true
            return thread
        }
    }

    // 设置 OkHttpClient - 使用守护线程避免阻塞应用关闭
    private fun getOkHttpClient(): OkHttpClient {
        val loggingInterceptor = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }

        return OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            // 配置连接池使用守护线程
            .dispatcher(okhttp3.Dispatcher(Executors.newFixedThreadPool(5, daemonThreadFactory)))
            .build()
    }

    // 创建 Retrofit 实例
    val instance: Retrofit by lazy {
        Retrofit.Builder()
            .baseUrl(BASE_URL)
            .client(getOkHttpClient())
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }
}
