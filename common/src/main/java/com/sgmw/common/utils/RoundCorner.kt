package com.sgmw.common.utils

import android.content.Context
import android.graphics.*
import android.os.Build
import androidx.annotation.NonNull
import com.bumptech.glide.load.Key
import com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation
import java.security.MessageDigest

/**
 * description: glide 圆角
 * <AUTHOR>
 * @date 2020/12/29 12:58 PM
 * @version V1.0
 * Copyright (C), 2020, NavInfo 四维图新
 */
class RoundCorner constructor(context: Context, leftTop: Float = 0f,
                              rightTop: Float = 0f,
                              rightBottom: Float = 0f,
                              leftBottom: Float = 0f) : BitmapTransformation() {

    private val ID: String = "com.autoai.agent.common.utils.RoundCorner"

    private val leftTop = dip2px(context, leftTop).toFloat()
    private val rightTop = dip2px(context, rightTop).toFloat()
    private val leftBottom = dip2px(context, leftBottom).toFloat()
    private val rightBottom = dip2px(context, rightBottom).toFloat()

    private val ID_BYTES: ByteArray


    fun dip2px(var0: Context, var1: Float): Int {
        val var2 = var0.resources.displayMetrics.density
        return (var1 * var2 + 0.5f).toInt()
    }

    init {
        ID_BYTES = ID.toByteArray(Key.CHARSET)
    }

    override fun transform(
            pool: BitmapPool, inBitmap: Bitmap, outWidth: Int, outHeight: Int): Bitmap {
        // Alpha is required for this transformation.
        val safeConfig = getAlphaSafeConfig(inBitmap)
        val toTransform = getAlphaSafeBitmap(pool, inBitmap)
        val result = pool[toTransform.width, toTransform.height, safeConfig]

        result.setHasAlpha(true)

        val shader = BitmapShader(toTransform, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP)
        val paint = Paint()
        paint.isAntiAlias = true
        paint.shader = shader
        val rect = RectF(0F, 0F, result.width.toFloat(), result.height.toFloat())

        val radii = floatArrayOf(leftTop, leftTop, rightTop, rightTop, rightBottom, rightBottom, leftBottom, leftBottom)
        val path = Path()
        path.addRoundRect(rect, radii, Path.Direction.CW)
        val canvas = Canvas(result)
        canvas.drawPath(path, paint)

        canvas.setBitmap(null)

        if (toTransform != inBitmap) {
            pool.put(toTransform)
        }
        return result
    }


    override fun equals(other: Any?): Boolean {
        if (other is RoundCorner) {
            return leftTop == other.leftTop && rightTop == other.rightTop && leftBottom == other.leftBottom &&
                    rightBottom == other.rightBottom
        }
        return false
    }

    override fun hashCode(): Int {
        return ID.hashCode() + leftTop.hashCode() + rightTop.hashCode() + leftBottom.hashCode() + rightBottom.hashCode()
    }

    override fun updateDiskCacheKey(messageDigest: MessageDigest) {
        messageDigest.update(ID_BYTES)
    }

    private fun getAlphaSafeConfig(@NonNull inBitmap: Bitmap): Bitmap.Config {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // Avoid short circuiting the sdk check.
            if (Bitmap.Config.RGBA_F16 == inBitmap.config) { // NOPMD
                return Bitmap.Config.RGBA_F16
            }
        }
        return Bitmap.Config.ARGB_8888
    }

    private fun getAlphaSafeBitmap(
            @NonNull pool: BitmapPool, @NonNull maybeAlphaSafe: Bitmap): Bitmap {
        val safeConfig = getAlphaSafeConfig(maybeAlphaSafe)
        if (safeConfig == maybeAlphaSafe.config) {
            return maybeAlphaSafe
        }
        val argbBitmap = pool[maybeAlphaSafe.width, maybeAlphaSafe.height, safeConfig]
        Canvas(argbBitmap).drawBitmap(maybeAlphaSafe, 0f, 0f, null /*paint*/)

        // We now own this Bitmap. It's our responsibility to replace it in the pool outside this method
        // when we're finished with it.
        return argbBitmap
    }
}


