package com.sgmw.common.utils

import android.content.Context
import android.content.Intent
import android.util.Log
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.tabs.TabLayout

/**
 * @author: 董俊帅
 * @time: 2025/7/28
 * @desc: 可见可说设置ContentDescription工具类
 */
object ContentDescriptionUtils {

    private const val TAG = "ContentDescriptionUtils"


    const val SLIDE_EDGE_BROADCAST: String = "com.sgmw.view.scrollview.action"

    const val SLIDE_TOP: Int = 1
    const val SLIDE_BOTTOM: Int = 2
    const val SCROLL_STATUS: String = "scrollstatus"

    fun setTabContentDescription(tab: TabLayout.Tab, isSelected: Boolean) {
        try {
            val objView = tab.view
            // class com.google.android.material.tabs.TabLayout$TabView
            val clzTabView: Class<*> = objView.javaClass

            val fieldTextView = clzTabView.getDeclaredField("textView")
            fieldTextView.isAccessible = true
            val objTextView = fieldTextView[objView] as TextView
            if (isSelected) {
                objTextView.contentDescription = "@viewSelectStatus(value=true)"
            } else {
                val contextDesc = (tab.text.toString() + ";"
                        + tab.text + "页面" + ";"
                        + tab.text + "界面" + ";"
                        + "打开" + tab.text + ";"
                        + "打开" + tab.text + "页面" + ";"
                        + "打开" + tab.text + "界面")
                objTextView.contentDescription = contextDesc
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun sendSlidTopBroadCast(ctx: Context) {
        Log.i(TAG, "scrollstatus:到顶了")
        val intent: Intent = Intent(SLIDE_EDGE_BROADCAST)
        intent.putExtra(SCROLL_STATUS, SLIDE_TOP)
        ctx.sendBroadcast(intent)
    }

    fun sendSlidBottomBroadCast(ctx: Context) {
        Log.i(TAG, "scrollstatus:到底了")
        val intent: Intent = Intent(SLIDE_EDGE_BROADCAST)
        intent.putExtra(SCROLL_STATUS, SLIDE_BOTTOM)
        ctx.sendBroadcast(intent)
    }

    fun isSlideToBottom(recyclerView: RecyclerView?, dy: Int): Boolean {
        if (recyclerView == null) return false
        if (!recyclerView.canScrollVertically(1) && dy != 0) return true
        return false
    }

    fun isSlideToTop(recyclerView: RecyclerView?, dy: Int): Boolean {
        if (recyclerView == null) return false
        if (!recyclerView.canScrollVertically(-1) && dy != 0) return true
        return false
    }


    fun isSlideToBottom(recyclerView: RecyclerView?): Boolean {
        if (recyclerView == null) return false
        if (!recyclerView.canScrollVertically(1)) return true
        return false
    }

    fun isSlideToTop(recyclerView: RecyclerView?): Boolean {
        if (recyclerView == null) return false
        if (!recyclerView.canScrollVertically(-1)) return true
        return false
    }

    fun isSlideToLeft(recyclerView: RecyclerView?): Boolean {
        if (recyclerView == null) return false
        if (!recyclerView.canScrollHorizontally(-1)) return true
        return false
    }

    fun isSlideToRight(recyclerView: RecyclerView?): Boolean {
        if (recyclerView == null) return false
        if (!recyclerView.canScrollHorizontally(1)) return true
        return false
    }

}