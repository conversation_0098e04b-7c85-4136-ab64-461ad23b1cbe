package com.sgmw.common.utils

import android.content.Context
import android.graphics.drawable.Drawable
import android.widget.ImageView
import com.bumptech.glide.Glide
import com.bumptech.glide.Priority
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.DecodeFormat
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.DownsampleStrategy
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.load.resource.gif.GifDrawable
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target
import com.sgmw.common.utils.GlideRoundTransform
import com.sgmw.common.utils.Log
import com.sgmw.common.utils.safeClose
import java.io.BufferedInputStream
import java.io.BufferedOutputStream
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream


object GlideUtil {

    /**
     * OOM恢复处理方法
     * 当发生OutOfMemoryError时，使用最小配置重新加载图片
     */
    private fun handleOOMRecovery(
        context: Context,
        url: String,
        imageView: ImageView,
        transform: com.bumptech.glide.load.Transformation<android.graphics.Bitmap>?,
        fallbackResId: Int
    ) {
        // 执行紧急内存清理
        System.gc()
        try {
            val fallbackOptions = RequestOptions()
                .diskCacheStrategy(DiskCacheStrategy.NONE)
                .skipMemoryCache(true)
                .format(DecodeFormat.PREFER_RGB_565)
                .override(200, 200) // 强制缩小尺寸到200x200
                .centerCrop()

            // 如果有变换，应用变换
            transform?.let { fallbackOptions.transform(it) }

            Glide.with(context)
                .load(url)
                .apply(fallbackOptions)
                .into(imageView)
        } catch (e2: Exception) {
            android.util.Log.e("GlideUtil", "Failed to load image after OOM recovery: $url", e2)
            // 最后的备用方案：显示占位图
            if (fallbackResId != 0) {
                imageView.setImageResource(fallbackResId)
            }
        }
    }

    fun loadImage(context: Context, url: String, imageView: ImageView) {
        try {
            Glide.with(context)
                .load(url)
                .apply(RequestOptions()
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .format(DecodeFormat.PREFER_RGB_565) // 使用RGB_565格式减少内存占用
                    .downsample(DownsampleStrategy.AT_MOST) // 降采样策略
                )
                .into(imageView)
        } catch (e: OutOfMemoryError) {
            android.util.Log.e("GlideUtil", "OutOfMemoryError loading image: $url", e)
            handleOOMRecovery(context, url, imageView, null, 0)
        } catch (e: Exception) {
            android.util.Log.e("GlideUtil", "Error loading image: $url", e)
        }
    }

    fun loadRoundCornerImage(context: Context, url: String, imageView: ImageView, ra: Int, errorResId: Int) {
        val options: RequestOptions = RequestOptions()
            .centerCrop()
            .placeholder(errorResId) //预加载图片
            .error(errorResId) //加载失败图片
            .priority(Priority.HIGH) //优先级
            .diskCacheStrategy(DiskCacheStrategy.NONE) //缓存
            .transform(GlideRoundTransform(ra)) //圆角

        Glide.with(context)
            .load(url)
            .apply(options)
            .into(imageView)
    }

    fun loadRoundCornerImage2(context: Context, url: String, imageView: ImageView, ra: Int) {
        Glide.with(context).clear(imageView)
        val options: RequestOptions = RequestOptions()
            .centerCrop()
            //.placeholder(R.mipmap.ic_launcher_round) //预加载图片
            //.error(R.drawable.ic_launcher_foreground) //加载失败图片
            .priority(Priority.HIGH) //优先级
            .diskCacheStrategy(DiskCacheStrategy.NONE) //缓存
            .transform(RoundedCorners(ra)) //圆角

        Glide.with(context)
            .load(url)
            .apply(options)
            .into(imageView)
    }
    fun loadRoundCornerImage(context: Context, url: String, imageView: ImageView, radius: Int, placeholder: Int, errorResId: Int) {
        try {
            val options: RequestOptions = RequestOptions()
                .centerCrop()
                .placeholder(placeholder) //预加载图片
                .error(errorResId) //加载失败图片
                .priority(Priority.HIGH) //优先级
                .diskCacheStrategy(DiskCacheStrategy.ALL) //缓存
                .format(DecodeFormat.PREFER_RGB_565) // 使用RGB_565格式减少内存占用
                .downsample(DownsampleStrategy.AT_MOST) // 降采样策略
                .transform(RoundedCorners(radius)) //圆角

            Glide.with(context)
                .load(url)
                .apply(options)
                .into(imageView)
        } catch (e: OutOfMemoryError) {
            android.util.Log.e("GlideUtil", "OutOfMemoryError loading round corner image: $url", e)
            handleOOMRecovery(context, url, imageView, RoundedCorners(radius), errorResId)
        } catch (e: Exception) {
            android.util.Log.e("GlideUtil", "Error loading round corner image: $url", e)
            if (errorResId != 0) {
                imageView.setImageResource(errorResId)
            }
        }
    }

    fun loadRoundCornerImage2(context: Context, url: String, imageView: ImageView, radius: Int, placeholder: Int, errorResId: Int) {
        try {
            val options: RequestOptions = RequestOptions()
                .centerCrop()
                .placeholder(placeholder) //预加载图片
                .error(errorResId) //加载失败图片
                .priority(Priority.HIGH) //优先级
                .diskCacheStrategy(DiskCacheStrategy.ALL) //缓存
                .format(DecodeFormat.PREFER_RGB_565) // 使用RGB_565格式减少内存占用
                .downsample(DownsampleStrategy.AT_MOST) // 降采样策略
                .transform(RoundedCorners(radius)) //圆角

            Glide.with(context)
                .load(url)
                .apply(options)
                .into(imageView)
        } catch (e: OutOfMemoryError) {
            android.util.Log.e("GlideUtil", "OutOfMemoryError loading round corner image2: $url", e)
            handleOOMRecovery(context, url, imageView, RoundedCorners(radius), errorResId)
        } catch (e: Exception) {
            android.util.Log.e("GlideUtil", "Error loading round corner image2: $url", e)
            if (errorResId != 0) {
                imageView.setImageResource(errorResId)
            }
        }
    }
    fun loadImageWithOptions(
        context: Context,
        url: String,
        imageView: ImageView,
        options: RequestOptions
    ) {
        Glide.with(context)
            .load(url)
            .apply(options)
            .into(imageView)
    }

    fun loadImageWithPlaceholder(
        context: Context,
        url: String,
        imageView: ImageView,
        placeholder: Int
    ) {
        Glide.with(context)
            .load(url)
            .apply(
                RequestOptions()
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .placeholder(placeholder)
            )
            .into(imageView)
    }

    fun loadImageWithPlaceholderAndError(
        context: Context,
        url: String,
        imageView: ImageView,
        placeholder: Int,
        error: Int
    ) {
        Glide.with(context)
            .load(url)
            .apply(
                RequestOptions()
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .placeholder(placeholder)
                    .error(error)
            )
            .into(imageView)
    }

    fun loadImageOrGifByExtension(context: Context, url: String, imageView: ImageView) {
        val extension = url.substringAfterLast(".", "")
        val isGif = extension.equals("gif", true)
        if (isGif) {
            Glide.with(context).asGif()
                .load(url)
                .apply(
                    RequestOptions()
                        .diskCacheStrategy(DiskCacheStrategy.ALL)

                )
                .into(imageView)
        } else {
            Glide.with(context)
                .load(url)
                .apply(
                    RequestOptions()
                        .diskCacheStrategy(DiskCacheStrategy.ALL)
                )
                .into(imageView)
        }
    }

    fun loadImageOrGifByExtensionWithCallBack(
        context: Context,
        url: String,
        imageView: ImageView,
        callback: (d1: GifDrawable?, d2: Drawable?) -> Unit
    ) {
        val extension = url.substringAfterLast(".", "")
        val isGif = extension.equals("gif", true)
        if (isGif) {
            Glide.with(context).asGif()
                .load(url)
                .apply(
                    RequestOptions()
                        .diskCacheStrategy(DiskCacheStrategy.ALL)

                )
                .listener(object : RequestListener<GifDrawable> {


                    override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<GifDrawable>,
                        isFirstResource: Boolean
                    ): Boolean {
                        return false
                    }

                    override fun onResourceReady(
                        resource: GifDrawable,
                        model: Any,
                        target: Target<GifDrawable>?,
                        dataSource: DataSource,
                        isFirstResource: Boolean
                    ): Boolean {
                        return false
                    }

                })
                .into(imageView)
        } else {
            Glide.with(context)
                .load(url)
                .apply(
                    RequestOptions()
                        .diskCacheStrategy(DiskCacheStrategy.ALL)
                )
                .listener(object : RequestListener<Drawable> {


                    override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<Drawable>,
                        isFirstResource: Boolean
                    ): Boolean {
                        return false
                    }

                    override fun onResourceReady(
                        resource: Drawable,
                        model: Any,
                        target: Target<Drawable>?,
                        dataSource: DataSource,
                        isFirstResource: Boolean
                    ): Boolean {
                        return false
                    }

                })
                .into(imageView)
        }
    }

    //    ThemeDownloadAIDL.instance?.getThemeImageResourcePath()
    fun saveImageFileToPath(context: Context, url: String, path: String, fileName: String) {
        Log.i("Glide", "save image")
        val extension = url.substringAfterLast(".", "")
        Glide.with(context).asFile()
            .load(url)
//                .apply(
//                    RequestOptions()
//                        .diskCacheStrategy(DiskCacheStrategy.ALL)
//                )
            .listener(object : RequestListener<File> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<File>,
                    isFirstResource: Boolean
                ): Boolean {
                    Log.e("Glide", "onLoadFailed")
                    return false
                }

                override fun onResourceReady(
                    resource: File,
                    model: Any,
                    target: Target<File>?,
                    dataSource: DataSource,
                    isFirstResource: Boolean
                ): Boolean {
                    try {
                        val dir = File(path)
                        if (!dir.exists()) {
                            dir.mkdir()
                        }
                        val name = "$fileName.$extension"
                        val file = File(dir, name)

                        if (file.exists()) {
                            file.delete()
                        }
                        file.createNewFile()
                        copyFile(resource, file)
                        Log.i("Glide", "save image to path with finish")
                    } catch (e: Exception) {
                        Log.e("Glide", "save image to path with error $e")
                    }
                    return false
                }

            })
            .preload()
    }


    private fun copyFile(sourceFile: File?, targetFile: File) {
        var inBuff: BufferedInputStream? = null
        var outBuff: BufferedOutputStream? = null
        try {
            inBuff = BufferedInputStream(FileInputStream(sourceFile))
            outBuff = BufferedOutputStream(FileOutputStream(targetFile))
            val b = ByteArray(1024 * 5)
            var len: Int
            while (inBuff.read(b).also { len = it } != -1) {
                outBuff.write(b, 0, len)
            }
            outBuff.flush()
        } catch (e: Exception) {
            Log.i("copyFile e: $e")
        } finally {
            inBuff?.safeClose()
            outBuff?.safeClose()
        }
    }
}