package com.sgmw.common.utils;

import java.io.File;
import java.io.RandomAccessFile;


/**
 * 多资源模块日志管理
 *
 * <AUTHOR>
 */
public class Log {
    private static final String TAG = "KSongs";
    private static final String LOG_NAME = Log.class.getSimpleName() + ".java";
    private static File file;
    private static RandomAccessFile raf;
    private  static boolean sIsLoggable = true;
    private static boolean sIsLogWrite;

    private static final Object lock = new Object();

    // 移除全局异常处理器设置，避免递归调用导致的native crash
    // 异常处理统一由SafeUncaughtExceptionHandler管理

    private Log() {
    }


    public static void setIsLoggable(boolean isLoggable){
        sIsLoggable = isLoggable;
    }

    public boolean isLoggable(){
        return sIsLoggable;
    }
    public void setIsLogWrite(boolean isLogWrite){
        sIsLogWrite = isLogWrite;
    }

    public static boolean isLogWrite(){
        return sIsLogWrite;
    }

    /**
     * 将字符串写入到文本文件中
     */
    private static void writeTxtToFile(String msg) {
        //生成文件夹之后，再生成文件，不然会出错
        // 每次写入时，都换行写
        if (!isLogWrite()){return;}
//
//        ThreadUtil.execute(() -> {
//            synchronized (lock) {
//                if (file == null || raf == null) {
//                    String date = new SimpleDateFormat("yyyyMMdd", Locale.getDefault()).format(Calendar.getInstance().getTime());
//                    String processName = null;
//                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
//                        processName = Application.getProcessName().replaceAll("/:", "_");
//                    }
//                    //每次启动使用新的 日志index命名，避免单个文件内容过多
//                    String key = "Log" + date + processName;
//                    int index = SpUtil.getLogIndex(key);
//                    index = index + 1;
//                    SpUtil.putLogInt(key, index);
//                    //保持日志数据不过多，避免占用过多车机存储空间
//                    String logPath = GlobalUtil.getContext().getExternalFilesDir("") + "/Log";
//                    File logPathFile = new File(logPath);
//                    if (logPathFile.exists()) {
//                        if (logPathFile.isDirectory()) {
//                            String[] files = logPathFile.list();
//                            if (files != null && files.length > 20) {
//                                for (String name : files) {
//                                    if (name.startsWith("AccountLog(" + processName + ")")) {
//                                        File file1 = new File(logPath + "/" + name);
//                                        file1.delete();
//                                    }
//                                }
//                            }
//                        }
//                    }
//                    //
//                    String strFilePath = logPath + "/AccountLog(" + processName + ")(" + date + ")_" + index + ".txt";
//                    try {
//                        file = new File(strFilePath);
//                        if (!file.exists()) {
//                            android.util.Log.d(TAG, "Create the file:" + strFilePath);
//                            file.getParentFile().mkdirs();
//                            file.createNewFile();
//                        }
//                        raf = new RandomAccessFile(file, "rwd");
//                    } catch (Exception e) {
//                        android.util.Log.e(TAG, "Error on write File:" + e);
//                    }
//                }
//
//
//                String strContent = msg + "\r\n";
//                try {
//                    raf.seek(file.length());
//                    raf.write(strContent.getBytes());
//                } catch (IOException e) {
//                    android.util.Log.e(TAG, "writeTxtToFile", e);
//                }
//            }
//        });
    }

    /**
     * 获取当前日志打印位置（安全版本）
     */
    private static String addStackTrace(String msg) {
        try {
            StackTraceElement[] elements = Thread.currentThread().getStackTrace();
            if (elements == null || elements.length == 0) {
                return msg != null ? msg : "";
            }

            for (int index = 5; index >= 0; index--) {
                if (elements.length <= index) {
                    continue;
                }
                StackTraceElement element = elements[index];
                if (element != null && LOG_NAME.equals(element.getFileName())) {
                    if (index + 1 < elements.length) {
                        StackTraceElement nextElement = elements[index + 1];
                        if (nextElement != null) {
                            return "(" + nextElement.getFileName() + ":" + nextElement.getLineNumber() + ")" + (msg != null ? msg : "");
                        }
                    }
                }
            }
            return msg != null ? msg : "";
        } catch (Exception e) {
            // 如果获取堆栈信息失败，直接返回原始消息
            return msg != null ? msg : "";
        }
    }

    public static void v(String msg) {
        v(TAG, msg,null);
    }


    public static void v(String msg, Throwable t) {
       v(TAG, msg,t);
    }

    public static void v(String tag, String msg) {
        v(tag, msg,null);
    }
    public static void v(String tag, String msg,Throwable t) {
        msg = addStackTrace(msg);
        android.util.Log.v(tag, msg,t);
        writeTxtToFile(tag + ": " + msg + "" + getStackTraceString(t));
    }

    public static void d(String msg) {
       d(TAG, msg,null);
    }

    public static void d(String msg, Throwable t) {
        d(TAG, msg,t);
    }

    public static void d(String tag, String msg) {
        d(tag, msg,null);
    }

    public static void d(String tag, String msg,Throwable t) {
        msg = addStackTrace(msg);
        android.util.Log.d(tag, msg,t);
        writeTxtToFile(tag + ": " + msg + "" + getStackTraceString(t));
    }
    public static void i(String msg) {
        i(TAG, msg,null);
    }

    public static void i(String msg, Throwable t) {
        i(TAG, msg,t);
    }
    public static void i(String tag, String msg) {
        i(tag, msg,null);
    }
    public static void i(String tag, String msg,Throwable t) {
        msg = addStackTrace(msg);
        android.util.Log.i(tag, msg,t);
        writeTxtToFile(tag + ": " + msg + "" + getStackTraceString(t));
    }
    public static void w(String msg) {
      w(TAG, msg,null);
    }

    public static void w(String msg, Throwable t) {
        w(TAG, msg,t);
    }

    public static void w(String tag, String msg) {
        w(tag, msg,null);
    }
    public static void w(String tag, String msg,Throwable t) {
        try {
            msg = addStackTrace(msg);
            android.util.Log.w(tag != null ? tag : TAG, msg, t);
            writeTxtToFile(tag + ": " + msg + "" + getStackTraceString(t));
        } catch (Exception e) {
            // 安全防护：避免日志记录本身导致异常
            try {
                android.util.Log.w("LogSafe", "Log.w方法内部异常: " + e.getMessage());
            } catch (Exception ignored) {
                // 静默处理
            }
        }
    }
    public static void e(String msg) {
      e(TAG, msg,null);
    }

    public static void e(String msg, Throwable t) {
        e(TAG, msg,t);
    }

    public static void e(String tag,String msg) {
        e(tag, msg,null);
    }
    public static void e(String tag, String msg,Throwable t) {
        try {
            // 安全地添加堆栈跟踪信息
            if (msg != null) {
                msg = addStackTrace(msg);
            } else {
                msg = "null";
            }

            // 安全地记录到系统日志
            android.util.Log.e(tag != null ? tag : TAG, msg, t);

            // 安全地写入文件（如果启用）
            if (isLogWrite()) {
                String stackTrace = "";
                if (t != null) {
                    try {
                        stackTrace = getStackTraceString(t);
                    } catch (Exception e) {
                        stackTrace = "[获取堆栈信息失败]";
                    }
                }
                writeTxtToFile(tag + ": " + msg + "" + stackTrace);
            }
        } catch (Exception e) {
            // 最后的安全防线：使用最基础的日志记录，避免递归调用
            try {
                android.util.Log.e("LogSafe", "Log.e方法内部异常: " + e.getMessage());
            } catch (Exception ignored) {
                // 如果连这个都失败了，就静默处理，避免无限递归
            }
        }
    }

    public static String getStackTraceString(Throwable tr) {
        try {
            if (tr == null) {
                return "";
            }
            return android.util.Log.getStackTraceString(tr);
        } catch (Exception e) {
            // 如果获取堆栈字符串失败，返回简单的异常信息
            try {
                return tr.getClass().getSimpleName() + ": " + tr.getMessage();
            } catch (Exception ex) {
                return "[获取异常信息失败]";
            }
        }
    }

    /**
     * 打印堆栈信息的方法
     */
    public static void printStackTrace(String msg) {
        String log = getStackTraceString(new Throwable(msg));
        log = log.replace("java.lang.Throwable:", "");
        android.util.Log.v(TAG, log);
        writeTxtToFile(TAG + ": " + log);
    }
}
