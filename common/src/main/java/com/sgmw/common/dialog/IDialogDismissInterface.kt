package com.sgmw.common.dialog
import android.app.Dialog
import androidx.fragment.app.DialogFragment


/**
 * Created by Sam on 2024/4/15.
 */
interface IDialogDismissInterface {

    /**
     * 关闭Dialog（不会检测当前Activity是否销毁，一般用在onDestroy中关闭dialog）
     */
    fun dismissDialog(dialog: Dialog?)

    /**
     * 安全的关闭Dialog（关闭前会检测当前Activity是否已经销毁）
     */
    fun dismissDialogSafely(dialog: Dialog?)

    /**
     * 关闭DialogFragment（不会检测当前Activity是否销毁，一般用在onDestroy中关闭dialog）
     */
    fun dismissDialogFragment(dialog: DialogFragment?)

    /**
     * 安全的关闭DialogFragment（关闭前会检测当前Activity是否已经销毁）
     */
    fun dismissDialogFragmentSafely(dialog: DialogFragment?)
}