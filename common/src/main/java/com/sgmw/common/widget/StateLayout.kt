package com.sgmw.common.widget

import android.content.Context
import android.graphics.drawable.AnimationDrawable
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.IdRes
import androidx.constraintlayout.widget.ConstraintLayout
import com.sgmw.common.R
import com.sgmw.common.ktx.setOnSingleClickListener

import com.sgmw.common.utils.Log
import com.sgmw.common.utils.StateLayoutEnum
import java.lang.ref.WeakReference


/**
 * 多状态布局
 */
class StateLayout @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) : FrameLayout(context, attrs, defStyleAttr){

    private val tag = "StateLayout"

    private val NO_MARGIN = -1F
    private val NO_HINT = ""

    private var loadingLayoutId = 0
    private var emptyLayoutId = 0
    private var errorLayoutId = 0
    private var defaultShowLoading = true

    private var contentView: View? = null
    private var emptyView: View? = null
    private var loadingView: View? = null
    private var errorView: View? = null
    private var currentState: StateLayoutEnum? = null

    private var loadingIvTopMargin = NO_MARGIN
    private var emptyIvTopMargin = NO_MARGIN
    private var errorIvTopMargin = NO_MARGIN
    private var emptyDataHintTxt = NO_HINT
    private var showEmptyBtn = true

    /**
     * 设置LoadingLayoutId
     */
    fun setLoadingLayoutId(@IdRes layoutId:Int):StateLayout{
        loadingLayoutId = layoutId
        return  this
    }

    /**
     * 设置空布局layoutId
     */
    fun setEmptyLayoutId(@IdRes emptyLayoutId:Int):StateLayout{
        this.emptyLayoutId = emptyLayoutId
        return this
    }

    /**
     * 设置ErrorLayoutId
     */
    fun setErrorLayoutId(@IdRes errorLayoutId:Int):StateLayout{
        this.errorLayoutId = errorLayoutId
        return  this
    }

    /**
     * 设置LoadingIv marginTop值
     */
    fun setLoadingIvTopMargin(loadingIvTopMargin:Float):StateLayout{
        this.loadingIvTopMargin = loadingIvTopMargin
        return this
    }

    /**
     * 设置Empty 布局 marginTop值
     */
    fun setEmptyIvTopMargin(emptyIvTopMargin:Float):StateLayout{
        this.emptyIvTopMargin = emptyIvTopMargin
        return this
    }

    /**
     * 设置 error布局 marginTop值
     */
    fun setErrorIvTopMargin(errorIvTopMargin:Float):StateLayout{
        this.errorIvTopMargin = errorIvTopMargin
        return this
    }

    /**
     * 设置空布局文案
     */
    fun setEmptyDataHintTxt(emptyDataHintTxt:String):StateLayout{
        this.emptyDataHintTxt = emptyDataHintTxt
        return this
    }

    /**
     * 设置是否展示 empty Button布局
     */
    fun setShowEmptyBtn(showEmptyBtn:Boolean):StateLayout{
        this.showEmptyBtn = showEmptyBtn
        return this
    }

    fun setParameters(@IdRes layoutId:Int,@IdRes emptyLayoutId:Int,@IdRes errorLayoutId:Int,loadingIvTopMargin:Float,
                      emptyIvTopMargin:Float,errorIvTopMargin:Float,emptyDataHintTxt:String,showEmptyBtn:Boolean):StateLayout{
        loadingLayoutId = layoutId
        this.emptyLayoutId = emptyLayoutId
        this.errorLayoutId = errorLayoutId
        this.loadingIvTopMargin = loadingIvTopMargin
        this.emptyIvTopMargin = emptyIvTopMargin
        this.errorIvTopMargin = errorIvTopMargin
        this.emptyDataHintTxt = emptyDataHintTxt
        this.showEmptyBtn = showEmptyBtn
        return this
    }
    init {
        val ta = context.obtainStyledAttributes(attrs, R.styleable.StateLayout)
        loadingLayoutId = ta.getResourceId(R.styleable.StateLayout_loadingResId, R.layout.layout_loading_state_view)
        emptyLayoutId = ta.getResourceId(R.styleable.StateLayout_emptyResId, R.layout.layout_empty_state_view)
        errorLayoutId = ta.getResourceId(R.styleable.StateLayout_errorResId, R.layout.layout_error_state_view)
        defaultShowLoading = ta.getBoolean(R.styleable.StateLayout_defaultShowLoading, true)

        loadingIvTopMargin = ta.getDimension(R.styleable.StateLayout_loadingIvTopMargin, NO_MARGIN)
        emptyIvTopMargin = ta.getDimension(R.styleable.StateLayout_emptyIvTopMargin, NO_MARGIN)
        errorIvTopMargin = ta.getDimension(R.styleable.StateLayout_errorIvTopMargin, NO_MARGIN)

        emptyDataHintTxt = ta.getString(R.styleable.StateLayout_emptyDataHintTxt) ?: NO_HINT
        showEmptyBtn = ta.getBoolean(R.styleable.StateLayout_showEmptyBtn, true)


        ta.recycle()
    }

    override fun onFinishInflate() {
        super.onFinishInflate()
        contentView = getChildAt(0)
        if (defaultShowLoading) {
            showLoading()
        }
    }

    /**
     * 设置SuccessLayout布局，即原来的view
     */
    fun setupSuccessLayout(tagView :View){
        contentView = tagView
        tagView.visibility = INVISIBLE
        addView(
            tagView, ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
        )
    }

    private var errorClickInfo: Pair<Int, WeakReference<OnClickListener>>? = null
    private var emptyClickInfo: Pair<Int, WeakReference<View.OnClickListener>>? = null

    fun setEmptyClickListener(clickResId: Int = R.id.tvEmpty, clickListener: View.OnClickListener?): StateLayout {
        emptyClickInfo = Pair(clickResId, WeakReference(clickListener))
        setViewClickListener(emptyView, emptyClickInfo?.first, emptyClickInfo?.second?.get())
        return this
    }

    fun setErrorRetryClickListener(clickResId: Int = R.id.tvErrorRetry, clickListener: View.OnClickListener?): StateLayout {
        errorClickInfo = Pair(clickResId, WeakReference(clickListener))
        setViewClickListener(errorView, errorClickInfo?.first, errorClickInfo?.second?.get())
        return this
    }

    private fun setViewClickListener(parentView: View?, clickResId: Int?, clickListener: View.OnClickListener?): StateLayout {
        clickResId?.let {
            clickListener?.let {
                parentView?.findViewById<View>(clickResId)?.setOnSingleClickListener(500, clickListener)
            }
        }
        return this
    }

    fun showView(state: StateLayoutEnum) {
        when(state) {
            StateLayoutEnum.SUCCESS -> {
                showContent()
            }
            StateLayoutEnum.LOADING -> {
                showLoading()
            }
            StateLayoutEnum.ERROR -> {
                showError()
            }
            StateLayoutEnum.NO_DATA -> {
                showEmpty()
            }
            else -> {
                Log.d(tag, "error state...")
            }
        }
    }

    fun showContent() {
        if (currentState == StateLayoutEnum.SUCCESS) return
        currentState = StateLayoutEnum.SUCCESS
        hideAllView()
        contentView?.visibility = View.VISIBLE
    }

    fun showLoading(@IdRes loadingId:Int = R.id.ivLoading) {
        if (currentState == StateLayoutEnum.LOADING) return
        currentState = StateLayoutEnum.LOADING
        if (loadingView == null) {
            loadingView = inflate(context, loadingLayoutId, null)
            updateViewTopMargin(loadingView, loadingId, loadingIvTopMargin)
            addView(loadingView)
            loadingView?.findViewById<ImageView>(R.id.ivLoading)?.setImageResource(R.drawable.kw_animation_fragment_loading)
        }
        hideAllView()
        loadingView?.visibility = View.VISIBLE
        (loadingView?.findViewById<ImageView>(R.id.ivLoading)?.drawable as? AnimationDrawable)?.start()
    }

    private fun updateViewTopMargin(parentView: View?, childViewResId: Int, topMargin: Float) {
        if (topMargin != NO_MARGIN) {
            parentView?.findViewById<View>(childViewResId)?.let { view ->
                (view.layoutParams as? ViewGroup.MarginLayoutParams)?.let {
                    it.topMargin = topMargin.toInt()
                    view.layoutParams = it
                }
            }
        }
    }

    fun showEmpty(@IdRes ivEmptyId:Int = R.id.ivEmpty,@IdRes tvEmptyId:Int = R.id.tvEmpty) {
        if (currentState == StateLayoutEnum.NO_DATA) return
        currentState = StateLayoutEnum.NO_DATA
        if (emptyView == null) {
            emptyView = inflate(context, emptyLayoutId, null)
            addView(emptyView)
            setViewClickListener(emptyView, emptyClickInfo?.first, emptyClickInfo?.second?.get())
            updateViewTopMargin(emptyView, ivEmptyId, emptyIvTopMargin)
            emptyView?.findViewById<View>(tvEmptyId)?.visibility = if (showEmptyBtn) { View.VISIBLE } else { View.GONE }
            if (emptyDataHintTxt != NO_HINT) {
                emptyView?.findViewById<TextView>(R.id.tvEmptyDataHint)?.text = emptyDataHintTxt
            }
        }
        hideAllView()
        emptyView?.visibility = View.VISIBLE
    }

    fun showError(@IdRes errorId:Int = R.id.ivError) {
        if (currentState == StateLayoutEnum.ERROR) return
        currentState = StateLayoutEnum.ERROR
        if (errorView == null) {
            errorView = inflate(context, errorLayoutId, null)
            addView(errorView)
            setViewClickListener(errorView, errorClickInfo?.first, errorClickInfo?.second?.get())
            updateViewTopMargin(errorView, errorId, errorIvTopMargin)
        }
        hideAllView()
        errorView?.visibility = View.VISIBLE
    }


    private fun hideAllView() {
        contentView?.visibility = View.GONE
        loadingView?.visibility = View.GONE
        emptyView?.visibility = View.GONE
        errorView?.visibility = View.GONE
        stopLoadingAnim()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        stopLoadingAnim()
    }

    private fun stopLoadingAnim() {
        (loadingView?.findViewById<ImageView>(R.id.ivLoading)?.drawable as? AnimationDrawable)?.let {
            if (it.isRunning) {
                it.stop()
            }
        }
    }

}