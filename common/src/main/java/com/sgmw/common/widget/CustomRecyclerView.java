package com.sgmw.common.widget;

import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.interpolator.view.animation.FastOutSlowInInterpolator;
import androidx.recyclerview.widget.RecyclerView;

import com.sgmw.common.R;
import com.sgmw.common.utils.ContentDescriptionUtils;


/**
 * 自定义recyclerView 添加滑动动效
 */
public class CustomRecyclerView extends RecyclerView {
    private static final String TAG = "CustomRecyclerView";

    // 记录回弹偏移量
    private float mBounceOffset = 0;
    // 标记是否处于拖拽（拉伸）状态
    private boolean mIsDragging = false;
    // 回弹动画时长（毫秒）
    private static final int BOUNCE_DURATION = 450;

    /**
     * 是否启动回弹动画
     */
    private boolean enableBoundAnimation = true;
    private boolean canReBound = true;

    /**
     * 是否支持底部渐隐效果
     */
    private boolean enableBottomFadeEffect = false;

    /**
     * 是否还有更多数据需要加载
     * 当为 true 时，向上滑动到底部不会触发回弹，以免干扰 SmartRefreshLayout 的加载更多功能
     */
    private boolean hasMoreData = false;

    private int slideTopCount = 0;
    private int slideBottomCount = 0;
    private boolean isFirstScroll = true;

    public CustomRecyclerView(@NonNull Context context) {
        this(context, null);
    }

    public CustomRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CustomRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initAttrs(context, attrs);
        setFadingEdge();
    }

    /**
     * 初始化XML属性
     */
    private void initAttrs(Context context, AttributeSet attrs) {
        if (attrs != null) {
            TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.CustomRecyclerView);
            enableBottomFadeEffect = typedArray.getBoolean(R.styleable.CustomRecyclerView_enableBottomFadeEffect, false);
            typedArray.recycle();
        }
    }

    private int startX, startY;

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                startX = (int) ev.getX();
                startY = (int) ev.getY();
                getParent().requestDisallowInterceptTouchEvent(true);//告诉viewgroup不要去拦截我
                break;
            case MotionEvent.ACTION_MOVE:
                int endX = (int) ev.getX();
                int endY = (int) ev.getY();
                int disX = Math.abs(endX - startX);
                int disY = Math.abs(endY - startY);
                if (disX > disY) {
                    getParent().requestDisallowInterceptTouchEvent(false);
                } else {
                    getParent().requestDisallowInterceptTouchEvent(true);//下拉的时候是false
                }
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                getParent().requestDisallowInterceptTouchEvent(true);
                break;
        }
        return super.dispatchTouchEvent(ev);
    }


    // 记录手指上一次的 Y 坐标
    private float mLastY;


    public void setEnableBoundAnimation(boolean isEnable) {
        enableBoundAnimation = isEnable;
    }

    /**
     * 设置是否支持底部渐隐效果
     *
     * @param enable true表示启用底部渐隐效果，false表示禁用
     */
    public void setEnableBottomFadeEffect(boolean enable) {
        this.enableBottomFadeEffect = enable;
        setFadingEdge();
    }

    /**
     * 获取是否支持底部渐隐效果
     *
     * @return true表示启用底部渐隐效果，false表示禁用
     */
    public boolean isEnableBottomFadeEffect() {
        return enableBottomFadeEffect;
    }

    /**
     * 设置是否还有更多数据需要加载
     *
     * @param hasMoreData true表示还有更多数据，此时向上滑动到底部不会触发回弹动画，
     *                    以免干扰 SmartRefreshLayout 的加载更多功能；
     *                    false表示没有更多数据，可以正常显示回弹动画
     */
    public void setHasMoreData(boolean hasMoreData) {
        Log.d(TAG, "setHasMoreData: hasMoreData = " + hasMoreData);
        this.hasMoreData = hasMoreData;
    }

    /**
     * 获取是否还有更多数据需要加载
     *
     * @return true表示还有更多数据，false表示没有更多数据
     */
    public boolean hasMoreData() {
        return hasMoreData;
    }

    @Override
    public boolean onTouchEvent(MotionEvent e) {
        switch (e.getActionMasked()) {
            case MotionEvent.ACTION_MOVE:
                float nowY = e.getY();
                float deltaY = nowY - mLastY;
                mLastY = nowY;

                // 1. 如果已经在回弹中，无论方向都继续往 0 推
                if (mIsDragging) {
                    float nextOffset = mBounceOffset + deltaY / 2f;
                    // 判断是否已经反向回到或超过 0
                    if ((mBounceOffset > 0 && nextOffset <= 0) ||
                            (mBounceOffset < 0 && nextOffset >= 0)) {
                        // 够了，直接回归正常状态
                        mBounceOffset = 0;
                        mIsDragging = false;
                    } else {
                        // 继续累加阻尼偏移
                        mBounceOffset = nextOffset;
                    }
                    invalidate();
                    return true;
                }

                // 2. 未在回弹时，按原逻辑判断进/出界触发回弹
                boolean isPullDown = !canScrollVertically(-1) && deltaY > 0;
                boolean isPullUp = !canScrollVertically(1) && deltaY < 0;

                // 当还有更多数据需要加载时，不要在向上滑动到底部时消费触摸事件
                // 这样可以让 SmartRefreshLayout 正常处理加载更多的逻辑
                if (isPullUp && hasMoreData) {
                    // 有更多数据且向上滑动到底部时，不消费事件，让父布局处理
                    break;
                }

                // 内容不足一屏时，不允许向上回弹
                int contentHeight = computeVerticalScrollRange();
                int viewHeight = getHeight();
                canReBound = (contentHeight > viewHeight || !isPullUp) && enableBoundAnimation;
                if (canReBound && (isPullDown || isPullUp)) {
                    mIsDragging = true;
                    mBounceOffset += deltaY / 2f;
                    invalidate();
                    return true;
                }
                break;

            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                if (mIsDragging) {
                    // 松手后做一个平滑回弹动画
                    ValueAnimator animator = ValueAnimator.ofFloat(mBounceOffset, 0);
                    animator.setDuration(BOUNCE_DURATION);
                    animator.setInterpolator(new FastOutSlowInInterpolator());
                    animator.addUpdateListener(animation -> {
                        mBounceOffset = (float) animation.getAnimatedValue();
                        invalidate();
                    });
                    animator.start();
                    mIsDragging = false;
                    return true;
                }
                break;
        }
        return super.onTouchEvent(e);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent e) {
        //这里处理down事件，避免被子view先拦截
        if (e.getActionMasked() == MotionEvent.ACTION_DOWN) {
            // 无论子 View 消不消费，先记录起点
            mLastY = e.getY();
            mIsDragging = false;
            // 让 RecyclerView 继续处理后续事件
        }
        // 如果想对子 View 也保持原有滚动／点击行为，就调用 super
        return super.onInterceptTouchEvent(e);
    }

    @Override
    protected void dispatchDraw(android.graphics.Canvas canvas) {
        if (!canReBound) {
            super.dispatchDraw(canvas);
            return;
        }
        int saveCount = canvas.save();
        // 应用上下平移效果，但在绘制前裁剪到原始 RecyclerView 边界
        canvas.translate(0, mBounceOffset);
        canvas.clipRect(0, 0, getWidth(), getHeight());
        super.dispatchDraw(canvas);
        canvas.restoreToCount(saveCount);
    }


    private void setFadingEdge() {
        setOverScrollMode(View.OVER_SCROLL_NEVER);
        setVerticalFadingEdgeEnabled(enableBottomFadeEffect);
//        setFadingEdgeLength(getResources().getDimensionPixelSize(R.dimen.dp_80));
        setVerticalScrollBarEnabled(true);
        setScrollbarFadingEnabled(true);
    }

    @Override
    public void onScrollStateChanged(int state) {
        super.onScrollStateChanged(state);
//        Log.e(TAG, "------>onScrollStateChanged:state:" + state);

        if (state == RecyclerView.SCROLL_STATE_IDLE) {
            if (ContentDescriptionUtils.INSTANCE.isSlideToTop(this)) {
                slideTopCount++;
                if (slideTopCount > 1 || isFirstScroll) {
                    ContentDescriptionUtils.INSTANCE.sendSlidTopBroadCast(getContext());
                }
            } else if (ContentDescriptionUtils.INSTANCE.isSlideToBottom(this)) {
                slideBottomCount++;
                if (slideBottomCount > 1) {
                    ContentDescriptionUtils.INSTANCE.sendSlidBottomBroadCast(getContext());
                }
            }
        }
    }

    @Override
    protected void onScrollChanged(int l, int t, int oldl, int oldt) {
        super.onScrollChanged(l, t, oldl, oldt);
//        Log.e(TAG, "------>onScrollChanged:l:" + l + "--t:" + t + "--oldl:" + oldl + "--oldt:" + oldt);
        if (t != oldt) {
            slideTopCount = 0;
            slideBottomCount = 0;
            isFirstScroll = false;
        }
    }
}
