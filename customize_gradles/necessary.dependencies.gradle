import com.autoai.car.buildsrc.Versions
import com.autoai.car.buildsrc.Libs

apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'org.jetbrains.kotlin.plugin.parcelize'
apply plugin: 'io.github.wurensen.android-aspectjx'
android {
    compileSdk Versions.COMPILE_SDK
    defaultConfig {
        minSdk Versions.MIN_SDK
        targetSdk Versions.TARGET_SDK
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }


    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
        freeCompilerArgs = ["-Xsam-conversions=class"]
    }
//    dataBinding {
//        enabled = true
//    }
    viewBinding {
        enabled = true
    }
    lintOptions {
        abortOnError false
    }
    // Robolectric 测试框架需要
    testOptions {
        unitTests {
//            unitTests.returnDefaultValues = true
            includeAndroidResources = true
//            returnDefaultValues = true
//            testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        }
    }


}
aspectjx {
    // 排除一些第三方库的包名（Gson、 LeakCanary 和 AOP 有冲突）
    // 否则就会起冲突：ClassNotFoundException: Didn't find class on path: DexPathList
    exclude 'androidx'
    exclude 'android.arch'
    exclude 'android.support'
//    exclude 'kotlin'
    exclude 'com.google'
    exclude 'versions.9'
    exclude 'com.squareup'
    exclude 'leakcanary'
    exclude 'com.taobao'
    exclude 'com.ut'
    exclude 'kotlinx'
    // 移除kotlin相关，编译错误和提升速度
//    exclude 'kotlin'
//    exclude 'kotlin.jvm', 'kotlin.internal'
//    exclude 'kotlinx.coroutines.internal', 'kotlinx.coroutines.android'
    exclude 'com.tencent'
    exclude 'tv.danmaku.ijk'
}



dependencies {
    // kotlin
    implementation Libs.Androidx.coreKtx
    // androidx
    implementation Libs.Androidx.appcompat
    // multidex
    implementation Libs.Androidx.MultiDex
    //activity-ktx
    implementation Libs.Androidx.ActivityKtx
    //fragment-ktx
    implementation Libs.Androidx.FragmentKtx
    // constraintlayout
    implementation Libs.Androidx.constraintlayout
    // material
    implementation Libs.material
    // 协程
    implementation Libs.Coroutines.Coroutines
    implementation Libs.Coroutines.CoroutinesAndroid
    // lifecycle ktx
    implementation Libs.LifeCycle.LifeCycleViewmodelKtx
    // gson
    implementation Libs.Gson
    // Aspectj
//    implementation Libs.Aspectj
    //
    implementation Libs.BaseRecyclerViewAdapterHelper
    //拼音库
    implementation Libs.pinyin
    implementation Libs.pinyin_android
    // glide
    implementation Libs.Glide.glide
    implementation Libs.Glide.glideTransform
    // zxing
    implementation Libs.zxing
    // okhttp & retrofit
    implementation Libs.Retrofit.Okhttp3
    implementation Libs.Retrofit.Okhttp3Logging
    implementation Libs.Retrofit.Retrofit
    implementation Libs.Retrofit.RetrofitConverterGson
    // Media
    implementation Libs.Media.exoplayerCore

    implementation Libs.Media.media

  /**************************Test ***********************************/
    implementation Libs.AndroidTest.test_fragment
    testImplementation Libs.AndroidTest.junit
    androidTestImplementation Libs.AndroidTest.androidxTestExtJunit
//    androidTestImplementation Libs.AndroidTest.androidxTestEspresso
    testImplementation Libs.AndroidTest.mockito_junit
    testImplementation Libs.AndroidTest.robolectric
    testImplementation Libs.AndroidTest.robolectric_492
    testImplementation Libs.AndroidTest.shadows_multidex
    testImplementation Libs.AndroidTest.mockito_lib
    testImplementation Libs.AndroidTest.mockito_inline
    testImplementation Libs.AndroidTest.test_fragment
    // mockito用于java的mock，无法mock final类，但android中必须依赖该包
//    androidTestImplementation Libs.AndroidTest.mockito_android_lib

    // kotlin中的mock使用mockk, 可以mock final类
    testImplementation Libs.AndroidTest.mockk_lib
//    androidTestImplementation Libs.AndroidTest.mockk_android_lib
    testImplementation Libs.AndroidTest.mockk_agent_jvm_lib
    // 测试livedata, 可让liveData
    testImplementation Libs.AndroidTest.testing_core
    // java可以mockfinal类，但不能加，与mockito_android_lib有冲突，记录在这以示提醒
//    androidTestImplementation 'org.mockito:mockito-inline:4.11.0'
    /**************************Test ***********************************/
}