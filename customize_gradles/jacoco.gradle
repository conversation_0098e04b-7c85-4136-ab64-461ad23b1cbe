apply plugin: 'jacoco'

android {
    buildTypes{
        debug{
            // 需要再对应的 flavor 或 type 处做一下声明
            testCoverageEnabled true
        }
    }
}

jacoco {
    toolVersion = "0.8.12" //代码覆盖库jacoco版本号
}

tasks.withType(Test) {
    jacoco.includeNoLocationClasses = true
    jacoco.excludes = ['jdk.internal.*']
}

boolean configed = false
// 适配多 Flavor 场景， 直接运行 gralde jacocoTestReportT2Debug  即可
android.applicationVariants.configureEach { variant ->
    var tsName = "${variant.flavorName.capitalize()}${variant.buildType.name.capitalize()}"
    task "jacocoTestReport${tsName}"(type: JacocoReport, dependsOn: ["test${tsName}UnitTest"]) {
        group = 'Reporting'
        description = 'Generate Jacoco coverage reports after running tests.'

        reports {
            xml.required = true
            csv.required =  false
        }

        classDirectories.setFrom(
                fileTree(dir: "build/intermediates/javac/${tsName}", excludes: ['**/R.class', '**/R$*.class', '**/*$ViewInjector*.*', '**/BuildConfig.*'])
                + fileTree(dir: "build/tmp/kotlin-classes/${tsName}", excludes: ['**/R.class', '**/R$*.class', '**/*$ViewInjector*.*', '**/BuildConfig.*']))
        sourceDirectories.setFrom(files('src/main/java'))
        executionData.setFrom(fileTree(dir: "build/", includes: ['**/*.exec']))
    }

    //  仅匹配第一个flavor Debug 任务
    if (variant.buildType.name == "debug" && !configed) {
        configed = true
        task jacocoTestReport(dependsOn: "jacocoTestReport${tsName}")
    }
}

// 匹配全部 jacocoTestReport 任务
// task jacocoTestReport(dependsOn: tasks.matching { it.name.startsWith('jacocoTestReport') && it.name != 'jacocoTestReport' })