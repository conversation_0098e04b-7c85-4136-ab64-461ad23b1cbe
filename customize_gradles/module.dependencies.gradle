import com.autoai.car.buildsrc.Libs
apply from: rootProject.file('customize_gradles/necessary.dependencies.gradle')

kapt {
//    arguments {
//        arg("AROUTER_MODULE_NAME", project.getName())
//    }


}

dependencies {
//    implementation Libs.Arouter.arouterApi
//    kapt Libs.Arouter.arouterCompiler
    implementation Libs.utilcodex
    implementation Libs.MMKV
    implementation Libs.EventBus


//    implementation Libs.Retrofit.Okhttp3
//    implementation Libs.Retrofit.Retrofit
//    implementation Libs.Retrofit.Okhttp3Logging
//    implementation Libs.Retrofit.RetrofitConverterGson
}
