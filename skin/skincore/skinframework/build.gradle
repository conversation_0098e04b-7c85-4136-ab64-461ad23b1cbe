plugins {
    id 'com.android.library'
}
apply from: 'maven-publish.gradle'
android {
    compileSdk rootProject.ext.targetSdkVersion
    defaultConfig {
        minSdk rootProject.ext.minSdkVersion
        targetSdk rootProject.ext.targetSdkVersion
        //
        versionCode rootProject.ext.skinframeworkVersionCode
        versionName rootProject.ext.skinframeworkVersionName
        //
        resourcePrefix "skin_framework_"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'
        //日志是否显示打印位置
        def isLogShowTrace = getLocalProperty(project, "isLogShowTrace", "false", "Log:: Show Stack Trace")
        buildConfigField "boolean", "isLogShowTrace", "${isLogShowTrace}"
        //昼夜模式 系统同步。“设置”App中调用，其他App跟着一起切换
        def isSystemSync = getLocalProperty(project, "isSystemSync", "false", "Log:: system sync")
        buildConfigField "boolean", "isSystemSync", "${isSystemSync}"
        //昼夜模式 系统同步。“设置”App中调用，其他App跟着一起切换
        def isShowDebuggingLog = getLocalProperty(project, "isShowDebuggingLog", "false", "Log:: show debugging log")
        buildConfigField "boolean", "isShowDebuggingLog", "${isShowDebuggingLog}"
        //
        def isForceShowAllLog = getLocalProperty(project, "isForceShowAllLog", "false", "Log:: force show all log")
        buildConfigField "boolean", "isForceShowAllLog", "${isForceShowAllLog}"
        //
        buildConfigField "String", "VERSION", "\"" + rootProject.ext.skinframeworkVersionName + "\""
    }

    buildTypes {
        release {
            // 启用代码压缩、优化及混淆
            minifyEnabled false
            // 启用资源压缩，需配合 minifyEnabled=true 使用
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            //Instrumentation代码覆盖率测试报告开关
            testCoverageEnabled false
        }
        debug {
            // 启用代码压缩、优化及混淆
            minifyEnabled false
            // 启用资源压缩，需配合 minifyEnabled=true 使用
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            //Instrumentation代码覆盖率测试报告开关
            testCoverageEnabled false
        }
    }

    lintOptions {
        disable 'GoogleAppIndexingWarning'
    }
}

dependencies {
    compileOnly project(':skin:autoinflater')
//    releaseCompileOnly "com.autoai.baseline.skincore:autoinflater:${rootProject.ext.autoinflaterVersionName}"

    if (rootProject.hasProperty("aspectjrt")) {
        implementation rootProject.ext.aspectjrt
    }

    implementation "androidx.annotation:annotation:1.3.0"
    compileOnly('com.google.android.material:material:1.4.0') {
        exclude group: 'androidx.recyclerview', module: 'recyclerview'
    }
}
