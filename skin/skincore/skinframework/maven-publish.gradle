apply plugin: "maven-publish"

////将源码打包
//task androidSourcesJar(type: Jar) {
//    archiveClassifier.set('sources')
//    from android.sourceSets.main.java.srcDirs
//}
publishing {
    repositories { RepositoryHandler handler ->
        handler.maven {
            // 凭证
            credentials {
                username = getLocalProperty(project, "username", "null", "maven-publish: wdnexus.autoai.com-->user name")
                password = getLocalProperty(project, "password", "null", "maven-publish: wdnexus.autoai.com-->user password")
            }
//            url "https://wdnexus.autoai.com/content/repositories/snapshots/"
            url "https://wdnexus.autoai.com/content/repositories/releases/"
        }
    }

    publications { PublicationContainer publication ->
        chucanMaven(MavenPublication) {// 容器可配置的信息 MavenPublication
            // 方式一：生成aar包:依赖 bundleReleaseAar 任务，并上传其产出的aar
            afterEvaluate { artifact(tasks.getByName("bundleReleaseAar")) }
            // 方式二：指定生成的aar路径
            // artifact "$buildDir/outputs/aar/${project.name}-release.aar"
            groupId "com.autoai.baseline.skincore"
            artifactId "skinframework"
            version rootProject.ext.skinframeworkVersionName
            pom.withXml {
                def dependenciesNode = asNode().appendNode('dependencies')
                configurations.releaseImplementation.allDependencies.each {
                    def dependencyNode = dependenciesNode.appendNode('dependency')
                    dependencyNode.appendNode('groupId', it.group)
                    dependencyNode.appendNode('artifactId', it.name)
                    dependencyNode.appendNode('version', it.version)
                }
                configurations.implementation.allDependencies.each {
                    def dependencyNode = dependenciesNode.appendNode('dependency')
                    dependencyNode.appendNode('groupId', it.group)
                    dependencyNode.appendNode('artifactId', it.name)
                    dependencyNode.appendNode('version', it.version)
                }
            }
//            //源码打包
//            artifact(androidSourcesJar) {
//                classifier = 'sources'
//            }
        }

    }
}
project.getTasksByName("publish", false)[0].doLast {
    println "------------------publishing-------------------"
    println("com.autoai.baseline.skincore:skinframework:${rootProject.ext.skinframeworkVersionName}")
    println "-----------------------------------------------"
}
