<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!--    <attr name="skinTypeface" format="string" tools:ignore="ResourceName" />-->
    <declare-styleable name="skin" tools:ignore="ResourceName">
        <attr name="skinEnable" format="boolean" />
        <attr name="android:background" />
        <attr name="android:foreground" />
        <attr name="android:textColor" />
        <attr name="android:text" />
        <attr name="android:hint" />
        <attr name="android:textColorHint" />
        <attr name="android:textColorHighlight" />
        <attr name="android:textColorLink" />
        <attr name="android:src" />
        <attr name="android:divider" />
        <attr name="android:button" />
        <attr name="android:progressDrawable" />
        <attr name="android:drawableLeft" />
        <attr name="android:drawableStart" />
        <attr name="android:drawableTop" />
        <attr name="android:drawableRight" />
        <attr name="android:drawableEnd" />
        <attr name="android:drawableBottom" />
        <attr name="android:scrollbarThumbVertical" />
        <attr name="android:scrollbarThumbHorizontal" />
        <attr name="android:scrollbarTrackHorizontal" />
        <attr name="android:scrollbarTrackVertical" />
        <attr name="android:thumb" />
        <attr name="android:track" />
        <attr name="android:indeterminateDrawable" />
        <attr name="tabIndicatorColor" format="color" />
        <attr name="tabIndicator" format="reference" />
        <attr name="tabSelectedTextColor" format="color" />
        <attr name="tabTextColor" format="color" />
        <attr name="android:textCursorDrawable" />
    </declare-styleable>
</resources>