package com.autoai.baseline.support.skincore.applier;

import android.annotation.SuppressLint;
import android.view.View;
import android.widget.AbsSeekBar;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.ProgressBar;
import android.widget.Switch;
import android.widget.TextView;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.language.LanguageViewUtil;
import com.autoai.baseline.support.skincore.language.bean.StringBean;
import com.autoai.baseline.support.skincore.applier.views.CompoundButtonUtil;
import com.autoai.baseline.support.skincore.applier.views.ImageViewUtil;
import com.autoai.baseline.support.skincore.applier.views.ListViewUtil;
import com.autoai.baseline.support.skincore.applier.views.ProgressBarUtil;
import com.autoai.baseline.support.skincore.applier.views.SwitchUtil;
import com.autoai.baseline.support.skincore.applier.views.TextViewUtil;
import com.autoai.baseline.support.skincore.applier.views.ViewUtil;
import com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils;

/**
 * 换肤，动态编码支持
 *
 * <AUTHOR>
 */
public class DynamicCodingApplier {

    private DynamicCodingApplier() {
    }

    public static void setBackground(final View view, final int resourceId) {
        if (resourceId == SkinConfigs.ID_NULL) {
            SkinAttributesUtils.removeViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_BACKGROUND);
            view.setBackground(null);
        } else {
            SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_BACKGROUND, resourceId);
            ViewUtil.setBackground(view, resourceId);
        }
    }

    public static void setForeground(final View view, final int resourceId) {
        if (resourceId == SkinConfigs.ID_NULL) {
            SkinAttributesUtils.removeViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_FOREGROUND);
            view.setForeground(null);
        } else {
            SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_FOREGROUND, resourceId);
            ViewUtil.setForeground(view, resourceId);
        }
    }

    public static void setImageSrc(final ImageView view, final int resourceId) {
        SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_SRC, resourceId);
        ImageViewUtil.setImageSrc(view, resourceId);
    }

    public static void setTextColor(final TextView view, final int resourceId) {
        SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_TEXT_COLOR, resourceId);
        TextViewUtil.setTextColor(view, resourceId);
    }

    public static void setDrawableStart(final TextView view, final int resourceId) {
        SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_START, resourceId);
        TextViewUtil.setDrawableStart(view, resourceId);
    }

    public static void setDrawableTop(final TextView view, final int resourceId) {
        SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_TOP, resourceId);
        TextViewUtil.setDrawableTop(view, resourceId);
    }

    public static void setDrawableEnd(final TextView view, final int resourceId) {
        SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_END, resourceId);
        TextViewUtil.setDrawableEnd(view, resourceId);
    }

    public static void setDrawableBottom(final TextView view, final int resourceId) {
        SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_BOTTOM, resourceId);
        TextViewUtil.setDrawableBottom(view, resourceId);
    }

    public static void setCompoundDrawablesRelativeWithIntrinsicBounds(final TextView view,
                                                                       final int leftResourceId,
                                                                       final int topResourceId,
                                                                       final int rightResourceId,
                                                                       final int bottomResourceId) {
        if (leftResourceId != SkinConfigs.ID_NULL) {
            SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_START, leftResourceId);
        }
        if (topResourceId != SkinConfigs.ID_NULL) {
            SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_TOP, topResourceId);
        }
        if (rightResourceId != SkinConfigs.ID_NULL) {
            SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_END, rightResourceId);
        }
        if (bottomResourceId != SkinConfigs.ID_NULL) {
            SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_BOTTOM, bottomResourceId);
        }
        TextViewUtil.setCompoundDrawablesRelativeWithIntrinsicBounds(view, leftResourceId, topResourceId, rightResourceId, bottomResourceId);
    }

    public static void setCompoundDrawablesWithIntrinsicBounds(final TextView view,
                                                               final int leftResourceId,
                                                               final int topResourceId,
                                                               final int rightResourceId,
                                                               final int bottomResourceId) {
        if (leftResourceId != SkinConfigs.ID_NULL) {
            SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_START, leftResourceId);
        }
        if (topResourceId != SkinConfigs.ID_NULL) {
            SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_TOP, topResourceId);
        }
        if (rightResourceId != SkinConfigs.ID_NULL) {
            SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_END, rightResourceId);
        }
        if (bottomResourceId != SkinConfigs.ID_NULL) {
            SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_BOTTOM, bottomResourceId);
        }
        TextViewUtil.setCompoundDrawablesWithIntrinsicBounds(view, leftResourceId, topResourceId, rightResourceId, bottomResourceId);
    }

    public static void setCompoundDrawables(final TextView view,
                                            final int leftResourceId,
                                            final int topResourceId,
                                            final int rightResourceId,
                                            final int bottomResourceId) {
        if (leftResourceId != SkinConfigs.ID_NULL) {
            SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_START, leftResourceId);
        }
        if (topResourceId != SkinConfigs.ID_NULL) {
            SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_TOP, topResourceId);
        }
        if (rightResourceId != SkinConfigs.ID_NULL) {
            SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_END, rightResourceId);
        }
        if (bottomResourceId != SkinConfigs.ID_NULL) {
            SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_BOTTOM, bottomResourceId);
        }
        TextViewUtil.setCompoundDrawables(view, leftResourceId, topResourceId, rightResourceId, bottomResourceId);
    }

    public static void setProgressDrawable(final ProgressBar view, final int resourceId) {
        SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_PROGRESS_DRAWABLE, resourceId);
        ProgressBarUtil.setProgressDrawable(view, resourceId);
    }

    public static void setProgressIndeterminateDrawable(final ProgressBar view, final int resourceId) {
        SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_INDETERMINATE_DRAWABLE, resourceId);
        ProgressBarUtil.setProgressIndeterminateDrawable(view, resourceId);
    }

    public static void setAbsSeekBarThumb(final AbsSeekBar view, final int resourceId) {
        SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_SWITCH_THUMB, resourceId);
        ProgressBarUtil.setAbsSeekBarThumb(view, resourceId);
    }


    public static void setScrollbarThumbVertical(final View view, final int resourceId) {
        SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_SCROLLBAR_THUMB_VERTICAL, resourceId);
        ViewUtil.setScrollbarThumbVertical(view, resourceId);
    }

    public static void setScrollbarThumbHorizontal(final View view, final int resourceId) {
        SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_SCROLLBAR_THUMB_HORIZONTAL, resourceId);
        ViewUtil.setScrollbarThumbHorizontal(view, resourceId);
    }

    public static void setScrollbarTrackHorizontal(final View view, final int resourceId) {
        SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_SCROLLBAR_TRACK_HORIZONTAL, resourceId);
        ViewUtil.setScrollbarTrackHorizontal(view, resourceId);
    }

    public static void setScrollbarTrackVertical(final View view, final int resourceId) {
        SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_SCROLLBAR_TRACK_VERTICAL, resourceId);
        ViewUtil.setScrollbarTrackVertical(view, resourceId);
    }

    public static void setButtonDrawable(final CompoundButton view, final int resourceId) {
        SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_BUTTON, resourceId);
        CompoundButtonUtil.setButtonDrawable(view, resourceId);
    }

    public static void setSwitchThumb(@SuppressLint("UseSwitchCompatOrMaterialCode") final Switch view, final int resourceId) {
        SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_SWITCH_THUMB, resourceId);
        SwitchUtil.setSwitchThumb(view, resourceId);
    }

    public static void setSwitchTrack(@SuppressLint("UseSwitchCompatOrMaterialCode") final Switch view, final int resourceId) {
        SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_SWITCH_TRACK, resourceId);
        SwitchUtil.setSwitchTrack(view, resourceId);
    }

    public static void setHintTextColor(final TextView view, final int resourceId) {
        SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_TEXT_COLOR_HINT, resourceId);
        TextViewUtil.setHintTextColor(view, resourceId);
    }

    public static void setTextColorHighlight(final TextView view, final int resourceId) {
        SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_TEXT_COLOR_HIGH_LIGHT, resourceId);
        TextViewUtil.setTextColorHighlight(view, resourceId);
    }

    public static void setTextColorLink(final TextView view, final int resourceId) {
        SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_TEXT_COLOR_LINK, resourceId);
        TextViewUtil.setTextColorLink(view, resourceId);
    }

    public static void setDivider(final ListView listView, final int resourceId) {
        SkinAttributesUtils.updateViewAttribute(listView, SkinAttributesUtils.ATTRIBUTE_LIST_VIEW_DIVIDER, resourceId);
        ListViewUtil.setDivider(listView, resourceId);
    }
}
