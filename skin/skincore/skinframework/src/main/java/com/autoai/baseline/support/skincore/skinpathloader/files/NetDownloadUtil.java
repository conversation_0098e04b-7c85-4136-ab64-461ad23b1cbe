package com.autoai.baseline.support.skincore.skinpathloader.files;

import android.text.TextUtils;

import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.SkinManager;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * 网络下载文件
 *
 * <AUTHOR>
 */
public class NetDownloadUtil {
    private static final String PATH = "NetSkins";

    private NetDownloadUtil() {
    }

    /**
     * @param downloadPath 皮肤包存储的网络地址
     */
    public static void download(final String downloadPath, final FileOperateCallback callback) {

        new Thread(new Runnable() {
            @Override
            public void run() {
                String filePath = null;
                try {
//                     SkinLogger.v("download skin file : downloadPath = " + downloadPath);
                    URL url = new URL(downloadPath);
                    HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                    conn.setConnectTimeout(5000);
                    conn.setReadTimeout(5000);
                    conn.setRequestMethod("GET");
                    if (conn.getResponseCode() == 200) {
                        String fileName = conn.getURL().getFile();
                        int fileNameStartPos = fileName.lastIndexOf("/") + 1;
                        if (fileNameStartPos < fileName.length()) {
                            fileName = fileName.substring(fileNameStartPos) + "_" + System.currentTimeMillis() + ".skin";
                        } else {
                            fileName = fileName.replace("/", "_") + "_" + System.currentTimeMillis() + ".skin";
                        }
//                         SkinLogger.v("download skin file : fileName = " + fileName);
                        String dirPath = SkinManager.getInstance().getApplicationContext().getExternalCacheDir() + File.separator + PATH;
                        File dir = new File(dirPath);
                        if (!dir.exists()) {
                            dir.mkdirs();
                        }
                        dir.setWritable(true);
                        dir.setReadable(true);
                        filePath = dirPath + File.separator + fileName;
                        File info = new File(filePath);
                        if (info.exists()) {
                            if (info.delete()) {
                                if (info.createNewFile()) {
                                    info.setWritable(true);
                                    info.setReadable(true);
                                }
                            }
                        }
                        long size = conn.getContentLength();//获取到最大值之后设置到进度条的MAX
//                         SkinLogger.v("download file size : " + size / 1024);
                        //开始下载
                        byte[] bytes = new byte[4096];//可以设置大点提高下载速度
                        int len;
                        InputStream in = conn.getInputStream();
                        FileOutputStream out = new FileOutputStream(filePath, false);
                        long progress = 0;
                        while ((len = in.read(bytes)) != -1) {
                            progress = progress + bytes.length;
//                             SkinLogger.v("downloading......" + (progress / size) * 100 + "%");
                            out.write(bytes, 0, len);
                            out.flush();

                        }
                        out.close();
                        in.close();
                    }
                    if (!TextUtils.isEmpty(filePath)) {
                        callback.onSuccess(filePath);
                    }
                } catch (Exception e) {
                    SkinLogger.e("NetDownload", e);
                    callback.onFailed("皮肤包下载失败：" + downloadPath);
                }
            }
        }).start();

    }
}
