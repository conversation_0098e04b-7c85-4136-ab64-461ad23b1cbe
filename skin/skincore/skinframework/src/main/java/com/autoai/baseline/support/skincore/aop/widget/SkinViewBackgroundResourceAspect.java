package com.autoai.baseline.support.skincore.aop.widget;

import android.view.View;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.applier.DynamicCodingApplier;
import com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinViewBackgroundResourceAspect {

    public static SkinViewBackgroundResourceAspect aspectOf() {
        return new SkinViewBackgroundResourceAspect();
    }

    @Pointcut("call(* android.view.View+.setBackgroundResource(..)) " + SkinConfigs.AOP_WITHOUT)
    public void backgroundResourcePointcut() {
    }

    @Around("backgroundResourcePointcut()")
    public Object aroundBackgroundResource(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        if (target instanceof View) {
            View view = (View) target;
            Object[] params = joinPoint.getArgs();
            if (params.length > 0) {
                Object param = params[0];
                if (param instanceof Integer) {
                    int resId = (Integer) param;
                    SkinLogger.d("AOP View+.setBackgroundResource resId = " + resId);
                    DynamicCodingApplier.setBackground(view, resId);
                    return null;
                } else if (param == null) {
                    SkinLogger.d("AOP View+.setBackgroundResource param == null");
                    DynamicCodingApplier.setBackground(view, SkinConfigs.ID_NULL);
                } else {
                    SkinAttributesUtils.removeViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_BACKGROUND);
                }
            }
        }
        //执行拦截方法
        return joinPoint.proceed();
    }
}
