package com.autoai.baseline.support.skincore.daynight;

import static com.autoai.baseline.support.skincore.SkinConfigs.IS_USE_SYSTEM_SYNC;

import android.content.ContentResolver;
import android.database.ContentObserver;
import android.net.Uri;
import android.provider.Settings;

import com.autoai.baseline.support.autoinflater.AutoInflaterThreadPoolUtil;
import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.SkinManager;
import com.autoai.baseline.support.skincore.skinpathloader.SkinChanger;

/**
 * 昼夜模式管理类
 *
 * <AUTHOR>
 */
public class DayNightUtil {
    /**
     * 默认是白天
     */
    private static DatNightMode curDayNightMode = DatNightMode.DAY;

    /**
     * 昼夜切换，如果需要系统同步功能，则需要先初始化同步功能
     */
    public static void init() {
        if (IS_USE_SYSTEM_SYNC && SkinConfigs.isSystemSync()) {
            DayNightSync.init();
        }
    }

    /**
     * 切换到黑夜模式
     */
    public static void changeToNightMode() {
        SkinLogger.v("切换到夜间模式");
        if (IS_USE_SYSTEM_SYNC && SkinConfigs.isSystemSync()) {
            try {
                DayNightSync.changeToNightMode();
            } catch (SecurityException e) {
                SkinLogger.w("SkinFramework: current App is not system App, can`t Sync", e);
                setMode(DatNightMode.NIGHT, true);
            }
        } else {
            setMode(DatNightMode.NIGHT, true);
        }
    }


    /**
     * 切换到黑夜模式
     */
    public static void changeToDayMode() {
        SkinLogger.v("切换到白天模式");
        if (IS_USE_SYSTEM_SYNC && SkinConfigs.isSystemSync()) {
            try {
                DayNightSync.changeToDayMode();
            } catch (SecurityException e) {
                SkinLogger.w("SkinFramework: current App is not system App, can`t Sync", e);
                setMode(DatNightMode.DAY, true);
            }
        } else {
            setMode(DatNightMode.DAY, true);
        }
    }

    private static void setMode(DatNightMode mode, boolean apply) {
//        if (mode == curDayNightMode) {
//            SkinLogger.d("当前昼夜模式已经是：" + mode);
//            return;
//        }
        curDayNightMode = mode;
        if (apply) {
//            SkinLogger.v("昼夜模式切换");
            SkinChanger.changeDayNight();
        }
    }

    public static DatNightMode getMode() {
        return curDayNightMode;
    }

    /**
     * 当前是否是夜间模式
     */
    public static boolean isNight() {
        boolean isNight = DatNightMode.NIGHT == curDayNightMode;
//        SkinLogger.w("isNight --> " + isNight);
        return isNight;
    }

    /**
     * 当前是否是夜间模式
     */
    public static boolean isDay() {
        boolean isDay = DatNightMode.DAY == curDayNightMode;
//        SkinLogger.w("isDay --> " + isDay);
        return isDay;
    }

    /**
     * 昼夜模式 系统同步。“设置”App中调用，其他App跟着一起切换
     *
     * <AUTHOR>
     */
    private static class DayNightSync {
        private static final int DAY = 1;
        private static final int NIGHT = 2;
        public static final String SKIN_SYSTEM_DAY_NIGHT_KEY = "day_night_mode_frame";

        private static void init() {
            readContentObserver(false);
            Uri dayNightUri = Settings.System.getUriFor(SKIN_SYSTEM_DAY_NIGHT_KEY);
            ContentResolver contentResolver = SkinManager.getInstance().getApplicationContext().getContentResolver();
            contentResolver.registerContentObserver(dayNightUri, false,
                    new ContentObserver(AutoInflaterThreadPoolUtil.getInstance().getMainHandler()) {
                        @Override
                        public void onChange(boolean selfChange) {
                            SkinLogger.d("registerContentObserver onChange selfChange = " + selfChange);
                            long start = System.currentTimeMillis();
                            readContentObserver(true);
                            SkinLogger.d("昼夜切换总耗时：" + (System.currentTimeMillis() - start) + "ms");
                        }
                    });
        }

        private static void readContentObserver(boolean apply) {
            //比如，新安装的应用，第一次启动，自身没有保存昼夜状态，初始读取系统设置，进行配置
            ContentResolver contentResolver = SkinManager.getInstance().getApplicationContext().getContentResolver();
            try {
                int mode = Settings.System.getInt(contentResolver, SKIN_SYSTEM_DAY_NIGHT_KEY);
                if (DAY == mode) {
                    setMode(DatNightMode.DAY, apply);
                } else {
                    setMode(DatNightMode.NIGHT, apply);
                }
                SkinLogger.d("readContentObserver DayNightMode  = " + mode + ", apply = " + apply);
            } catch (Settings.SettingNotFoundException exception) {
                SkinLogger.w("readContentObserver DayNightMode apply = " + apply, exception);
                setMode(DatNightMode.DAY, apply);
            }
        }

        /**
         * 切换到黑夜模式
         */
        private static void changeToNightMode() {
            //白天黑夜模式保存到车机系统设置里面
            SkinLogger.i("changeToNightMode：Settings.System.putInt NIGHT");
            ContentResolver contentResolver = SkinManager.getInstance().getApplicationContext().getContentResolver();
            Settings.System.putInt(contentResolver, SKIN_SYSTEM_DAY_NIGHT_KEY, NIGHT);
        }

        /**
         * 切换到白天模式
         */
        private static void changeToDayMode() {
            //白天黑夜模式保存到车机系统设置里面
            SkinLogger.i("changeToDayMode：Settings.System.putInt DAY");
            ContentResolver contentResolver = SkinManager.getInstance().getApplicationContext().getContentResolver();
            Settings.System.putInt(contentResolver, SKIN_SYSTEM_DAY_NIGHT_KEY, DAY);
        }
    }
}
