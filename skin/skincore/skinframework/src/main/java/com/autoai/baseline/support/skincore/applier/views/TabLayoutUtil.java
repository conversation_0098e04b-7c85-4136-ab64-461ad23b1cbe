package com.autoai.baseline.support.skincore.applier.views;

import android.content.res.ColorStateList;
import android.graphics.Color;
import android.graphics.drawable.Drawable;

import com.autoai.baseline.support.skincore.res.ResManager;
import com.google.android.material.tabs.TabLayout;

/**
 * TabLayout 资源应用
 *
 * <AUTHOR>
 */
public class TabLayoutUtil {
    /**
     * 隐藏构造方法，不允许创建实例
     */
    private TabLayoutUtil() {
    }

    public static void setSelectedTabIndicatorColor(TabLayout tabLayout, int resId) {
        final int color = ResManager.getInstance().getColor(resId);
        tabLayout.setSelectedTabIndicatorColor(color);
        tabLayout.selectTab(tabLayout.getTabAt(tabLayout.getSelectedTabPosition()));
    }

    public static void setSelectedTabIndicator(TabLayout tabLayout, int resId) {
        final Drawable drawable = ResManager.getInstance().getDrawable(resId);
        final Drawable oldDrawable = tabLayout.getTabSelectedIndicator();
        if (drawable != null) {
            drawable.setBounds(oldDrawable.getBounds());
        }
        tabLayout.setSelectedTabIndicatorHeight(oldDrawable.getBounds().bottom);
        tabLayout.setSelectedTabIndicator(drawable);
        tabLayout.selectTab(tabLayout.getTabAt(tabLayout.getSelectedTabPosition()));
    }

    public static void setTabTextColors(TabLayout tabLayout, int resId) {
        final int defaultColor = ResManager.getInstance().getColor(resId);
        ColorStateList colorStateList = tabLayout.getTabTextColors();
        if (colorStateList != null) {
           int selectedColor = colorStateList.getColorForState(new int[]{android.R.attr.state_selected},
                    Color.BLACK);
            tabLayout.setTabTextColors(defaultColor, selectedColor);
        }
        tabLayout.selectTab(tabLayout.getTabAt(tabLayout.getSelectedTabPosition()));
    }
    public static void setTabSelectTextColors(TabLayout tabLayout, int resId) {
        final int selectedColor = ResManager.getInstance().getColor(resId);
        ColorStateList colorStateList = tabLayout.getTabTextColors();
        if (colorStateList != null) {
            tabLayout.setTabTextColors(colorStateList.getDefaultColor(), selectedColor);
        }
        tabLayout.selectTab(tabLayout.getTabAt(tabLayout.getSelectedTabPosition()));
    }
}
