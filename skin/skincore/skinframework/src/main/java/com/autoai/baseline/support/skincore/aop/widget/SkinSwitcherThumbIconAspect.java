package com.autoai.baseline.support.skincore.aop.widget;

import android.widget.Switch;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinSwitcherThumbIconAspect {

    public static SkinSwitcherThumbIconAspect aspectOf() {
        return new SkinSwitcherThumbIconAspect();
    }

    /**
     * 任何一个包下面的任何一个类下面的任何一个带有SkinActivityInit的方法，构成了这个切面
     */
    @Pointcut("call(* android.widget.Switch+.setThumbIcon(..)) " + SkinConfigs.AOP_WITHOUT)
    public void setThumbIconPointcut() {
    }

    /**
     * 拦截方法
     */
    @Around("setThumbIconPointcut()")
    public Object aroundThumbIcon(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        if (target instanceof Switch) {
            SkinLogger.d("AOP Switch+.setThumbIcon removeViewAttribute");
            Switch aSwitch = (Switch) target;
            SkinAttributesUtils.removeViewAttribute(aSwitch, SkinAttributesUtils.ATTRIBUTE_SWITCH_THUMB);
        }
        //执行拦截方法
        return joinPoint.proceed();
    }
}
