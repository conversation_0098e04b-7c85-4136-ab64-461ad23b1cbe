package com.autoai.baseline.support.skincore.skinpathloader.files;

import android.content.Context;

import com.autoai.baseline.support.autoinflater.AutoInflaterThreadPoolUtil;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.SkinManager;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;

/**
 * Asset资源拷贝
 *
 * <AUTHOR>
 */
public class AssetCopyUtil {
    private static final String PATH = "AssetsSkins";

    private AssetCopyUtil() {
    }

    /**
     * @param srcPath Assets里面存放皮肤包的的文件夹路径
     */
    public static void copyAssetsToSdCard(final String srcPath, final FileOperateCallback callback) {
        AutoInflaterThreadPoolUtil.getInstance().singExecute(new Runnable() {
            @Override
            public void run() {
                SkinLogger.v("copyAssetsToSdCard srcPath = " + srcPath);
                boolean success;
                String dirPath = null;
                try {
                    Context context = SkinManager.getInstance().getApplicationContext();
                    String[] fileNames = context.getAssets().list(srcPath);
                    if (fileNames != null && fileNames.length > 0) {
                        File dir = new File(context.getExternalCacheDir() + File.separator + PATH);
                        dir.delete();
                        dir.mkdir();
                        for (String fileName : fileNames) {
                            if (fileName.endsWith(".skin")) {
                                File outFile = new File(dir.getAbsolutePath(), fileName);
                                outFile.delete();
                                outFile.createNewFile();
                                String sourcePath;
                                if (!"".equals(srcPath)) {
                                    // assets 文件夹下的目录
                                    sourcePath = srcPath + File.separator + fileName;
                                } else {
                                    // assets 文件夹
                                    sourcePath = fileName;
                                }
                                InputStream is = context.getAssets().open(sourcePath);
                                FileOutputStream fos = new FileOutputStream(outFile);
                                byte[] buffer = new byte[2048];
                                int byteCount;
                                while ((byteCount = is.read(buffer)) != -1) {
                                    fos.write(buffer, 0, byteCount);
                                }
                                fos.flush();
                                is.close();
                                fos.close();
                            }
                        }
                        dirPath = dir.getAbsolutePath();
                        success = true;
                    } else {
                        success = false;
                    }
                } catch (Exception e) {
                    SkinLogger.e("copyAssetsToSdCard", e);
                    success = false;
                }
                if (success) {
                    if (callback != null) {
                        callback.onSuccess(dirPath);
                    }
                } else {
                    if (callback != null) {
                        callback.onFailed("Assets中没有皮肤包文件");
                    }
                }
            }
        });
    }
}
