package com.autoai.baseline.support.skincore;

import android.app.Activity;
import android.app.Application;
import android.os.Bundle;

import androidx.annotation.NonNull;

import com.autoai.baseline.support.skincore.viewholders.WindowHolder;

/**
 * Activity 生命周期监听，用于判断前后台、Activity检测
 *
 * <AUTHOR>
 */
public class SkinActivityLifecycleCallbacks implements Application.ActivityLifecycleCallbacks {
    /**
     * 活跃activity的count数
     */
    private static int activityCount;

    public SkinActivityLifecycleCallbacks() {
    }

    private static void addActivityCount(boolean add) {
        if (add) {
            activityCount++;
        } else {
            activityCount--;
        }
        SkinLogger.v("ActivityLifecycle: activityCount = " + activityCount);
//        if (!SkinConfigs.isIgnoreForeground()) {
//            SkinChanger.changeUi();
//        }
    }


    /**
     * 判断是否在前台
     */
    public static boolean isForeground() {
        return activityCount > 0;
    }

    @Override
    public void onActivityCreated(@NonNull Activity activity, Bundle savedInstanceState) {
        SkinLogger.v("顺序 ActivityLifecycle: onActivityCreated =============================================");
        WindowHolder.addActivity(activity);
    }

    @Override
    public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {
        SkinLogger.v("ActivityLifecycle: onActivitySaveInstanceState");
    }

    @Override
    public void onActivityStarted(@NonNull Activity activity) {
        SkinLogger.v("ActivityLifecycle: onActivityStarted");
        addActivityCount(true);
    }

    @Override
    public void onActivityResumed(@NonNull Activity activity) {
        SkinLogger.v("ActivityLifecycle: onActivityResumed");
    }

    @Override
    public void onActivityPaused(@NonNull Activity activity) {
        SkinLogger.v("ActivityLifecycle: onActivityPaused");
    }

    @Override
    public void onActivityStopped(@NonNull Activity activity) {
        SkinLogger.v("ActivityLifecycle: onActivityStopped");
        addActivityCount(false);
    }

    @Override
    public void onActivityDestroyed(@NonNull Activity activity) {
        SkinLogger.v("ActivityLifecycle: onActivityDestroyed");
        WindowHolder.removeActivity(activity);
    }
}
