package com.autoai.baseline.support.skincore;

import android.app.Activity;
import android.app.Dialog;
import android.view.View;
import android.widget.PopupWindow;

import java.util.ArrayList;

/**
 * 换肤应用的时候Activity、Dialog等顺序定制
 *
 * <AUTHOR>
 */
public class SkinApplySortBean {
    private ArrayList<Dialog> dialogList;
    private ArrayList<PopupWindow> popupWindowList;
    private ArrayList<Activity> activityList;
    private ArrayList<View> viewList;

    public ArrayList<Dialog> getDialogList() {
        return dialogList;
    }

    public void setDialogList(ArrayList<Dialog> dialogList) {
        this.dialogList = dialogList;
    }

    public ArrayList<PopupWindow> getPopupWindowList() {
        return popupWindowList;
    }

    public void setPopupWindowList(ArrayList<PopupWindow> popupWindowList) {
        this.popupWindowList = popupWindowList;
    }

    public ArrayList<Activity> getActivityList() {
        return activityList;
    }

    public void setActivityList(ArrayList<Activity> activityList) {
        this.activityList = activityList;
    }

    public ArrayList<View> getViewList() {
        return viewList;
    }

    public void setViewList(ArrayList<View> viewList) {
        this.viewList = viewList;
    }
}
