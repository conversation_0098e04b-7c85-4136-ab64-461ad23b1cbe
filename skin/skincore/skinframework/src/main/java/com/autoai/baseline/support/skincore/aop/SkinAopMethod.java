package com.autoai.baseline.support.skincore.aop;

import android.view.View;
import android.widget.AbsListView;
import android.widget.ScrollView;

/**
 * ！！！！有反射，AOP切面调用：位置名称、参数 不能修改
 */
public class SkinAopMethod {

    /**
     * ！！！！此方法有反射，AOP切面调用：位置名称、参数 不能修改
     */
    public static boolean judgeView(View view) {
        return (view instanceof AbsListView) || (view instanceof ScrollView);
    }

    /**
     * ！！！！此方法有反射，AOP切面调用：位置名称、参数 不能修改
     * <p>
     * 用于处理RecyclerView、ListView等等的
     */
    @SuppressWarnings("unused")
    public static void applyView(View view) {
    }
}
