package com.autoai.baseline.support.skincore.applier;

import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsSeekBar;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.ProgressBar;
import android.widget.Switch;
import android.widget.TextView;

import com.autoai.baseline.support.skincore.R;
import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.SkinViewSupport;
import com.autoai.baseline.support.skincore.aop.SkinAopMethod;
import com.autoai.baseline.support.skincore.applier.views.CompoundButtonUtil;
import com.autoai.baseline.support.skincore.applier.views.ImageViewUtil;
import com.autoai.baseline.support.skincore.applier.views.ListViewUtil;
import com.autoai.baseline.support.skincore.applier.views.ProgressBarUtil;
import com.autoai.baseline.support.skincore.applier.views.SwitchUtil;
import com.autoai.baseline.support.skincore.applier.views.TabLayoutUtil;
import com.autoai.baseline.support.skincore.applier.views.TextViewUtil;
import com.autoai.baseline.support.skincore.applier.views.ViewUtil;
import com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils;
import com.autoai.baseline.support.skincore.attribute.SkinState;
import com.autoai.baseline.support.skincore.attribute.ViewTagUtil;
import com.autoai.baseline.support.skincore.daynight.DayNightUtil;
import com.autoai.baseline.support.skincore.language.LanguageViewUtil;
import com.autoai.baseline.support.skincore.language.bean.StringBean;
import com.autoai.baseline.support.skincore.res.ResManager;
import com.google.android.material.tabs.TabLayout;

import java.util.Map;
import java.util.Set;

/**
 * 换肤处理类，
 *
 * <AUTHOR>
 */
public class ViewApplier {
    /**
     * 换肤：更换当前VIew及其子View 的UI
     */
    public static void applyRootView(final View view) {
        SkinLogger.v("applyRootView view  : " + view);
        //取消 isViewNeedApply，避免切换语言的时候不切换
        final SkinState skinState = ViewTagUtil.getTagViewSkin(view);
        if (isViewNeedApply(skinState)) {
            doApply(view);
        }
    }

    /**
     * 当前这个 View 进行换肤处理
     */
    private static void doApply(final View view) {
        if (view == null) {
            return;
        }
        final SkinState skinState = ViewTagUtil.getTagViewSkin(view);
        if (SkinLogger.isLoggable()) {
            SkinLogger.v("doApply---------------------------------------------------------------------------------------------------");
            SkinLogger.v("doApply view = " + view + ", skinState = " + skinState
                    + ", skinState.isSkinEnable = " + (skinState == null ? "!!!skinState == null!!!" : skinState.isSkinEnable() + ""));
        }
        //fix:BAC-8673【1A】【系统测试】【机型-L】【2M】【Navi】【Bench】导航中，点击行程分享-充电站按钮，切换主题，点击返回，刷新按钮显示异常
        //fix:BAC-8403【1A】【系统测试】【机型-L】【2M】【Navi】【Bench】路线规划画面，进行周边搜，点击交通事件，切换主题，点击返回，列表画面颜色异常
        //不适用post 延迟，因为容易如果出现App卡顿，堆栈在此，则会让换肤框架背锅
//        if (view.getVisibility() == View.VISIBLE) {
        changeSingleView(view, skinState);
//        } else {
//            AutoInflaterThreadPoolUtil.getInstance().runOnUiThreadDelayed(new Runnable() {
//                @Override
//                public void run() {
//                    changeSingleView(view, skinState);
//                }
//            }, 1000);
//        }
    }

    private static void changeSingleView(View view, SkinState skinState) {
        if (view instanceof SkinViewSupport) {
            ((SkinViewSupport) view).applySkin();
        }
        if (skinState != null && skinState.isSkinEnable()) {
            if (!skinState.isEmpty()) {
                for (Map.Entry<String, Integer> entry : skinState.entrySet()) {
                    changeViewAttribute(view, entry.getKey(), entry.getValue());
                }
            }
            skinState.setSkinNickName(SkinConfigs.getSkinNickName());
            skinState.setDayNightMode(DayNightUtil.getMode());
        }
        //处理 子View
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            //适配 RecyclerView 、 ListView、ViewPager 等等
            SkinAopMethod.applyView(viewGroup);
            //
            if (skinState != null) {
                Set<View> set = skinState.getViewSet();
                if (set != null) {
                    for (View item : set) {
                        doApply(item);
                    }
                }
            }
            //处理子View
            int childCount = viewGroup.getChildCount();
            for (int i = childCount - 1; i >= 0; i--) {
                View child = viewGroup.getChildAt(i);
                doApply(child);
            }
        }
    }

    /**
     * 当前 View 是否需要应用换肤
     */
    private static boolean isViewNeedApply(SkinState skinState) {
        if (skinState == null) {
            return true;
        } else {
            boolean isModeEqual = skinState.getDayNightMode() == DayNightUtil.getMode();
            boolean isSkinEqual = TextUtils.equals(skinState.getSkinNickName(), SkinConfigs.getSkinNickName());
            boolean isLocalEqual = SkinConfigs.localEqual(skinState.getLocale(), ResManager.getInstance().getLocale());
            return !isModeEqual || !isSkinEqual || !isLocalEqual;
        }
    }

    /**
     * 换肤：变更属性值
     */
    public static void changeViewAttribute(View view, String attributeName, int resourceId) {
        if (SkinLogger.isShowDebuggingLog() && SkinLogger.isLoggable()) {
            SkinLogger.i("changeViewAttribute -->\n" + view + ", attributeName = " + attributeName + ", resourceId = " + resourceId);
        }
        switch (attributeName) {
            case SkinAttributesUtils.ATTRIBUTE_BACKGROUND:
                ViewUtil.setBackground(view, resourceId);
                break;
            case SkinAttributesUtils.ATTRIBUTE_FOREGROUND:
                ViewUtil.setForeground(view, resourceId);
                break;
            case SkinAttributesUtils.ATTRIBUTE_SRC:
                if (view instanceof ImageView) {
                    ImageViewUtil.setImageSrc(((ImageView) view), resourceId);
                }
                break;
            case SkinAttributesUtils.ATTRIBUTE_TEXT_COLOR:
                if (view instanceof TextView) {
                    TextViewUtil.setTextColor(((TextView) view), resourceId);
                }
                break;
            case SkinAttributesUtils.ATTRIBUTE_TEXT:
                if (view instanceof TextView) {
                    Object object = view.getTag(R.id.tag_skin_text_args);
                    if (object instanceof StringBean) {
                        LanguageViewUtil.setText((TextView) view, (StringBean) object);
                    } else {
                        LanguageViewUtil.setText((TextView) view, resourceId);
                    }
                }
                break;
            case SkinAttributesUtils.ATTRIBUTE_HINT:
                if (view instanceof TextView) {
                    LanguageViewUtil.setHint((TextView) view, resourceId);
                }
                break;
            case SkinAttributesUtils.ATTRIBUTE_DRAWABLE_LEFT:
            case SkinAttributesUtils.ATTRIBUTE_DRAWABLE_START:
                if (view instanceof TextView) {
                    TextViewUtil.setDrawableStart(((TextView) view), resourceId);
                }
                break;
            case SkinAttributesUtils.ATTRIBUTE_DRAWABLE_TOP:
                if (view instanceof TextView) {
                    TextViewUtil.setDrawableTop(((TextView) view), resourceId);
                }
                break;
            case SkinAttributesUtils.ATTRIBUTE_DRAWABLE_RIGHT:
            case SkinAttributesUtils.ATTRIBUTE_DRAWABLE_END:
                if (view instanceof TextView) {
                    TextViewUtil.setDrawableEnd(((TextView) view), resourceId);
                }
                break;
            case SkinAttributesUtils.ATTRIBUTE_DRAWABLE_BOTTOM:
                if (view instanceof TextView) {
                    TextViewUtil.setDrawableBottom(((TextView) view), resourceId);
                }
                break;
            case SkinAttributesUtils.ATTRIBUTE_PROGRESS_DRAWABLE:
                if (view instanceof ProgressBar) {
                    ProgressBarUtil.setProgressDrawable(((ProgressBar) view), resourceId);
                }
                break;
            case SkinAttributesUtils.ATTRIBUTE_INDETERMINATE_DRAWABLE:
                if (view instanceof ProgressBar) {
                    ProgressBarUtil.setProgressIndeterminateDrawable(((ProgressBar) view), resourceId);
                }
                break;
            case SkinAttributesUtils.ATTRIBUTE_SCROLLBAR_THUMB_VERTICAL:
                ViewUtil.setScrollbarThumbVertical(view, resourceId);
                break;
            case SkinAttributesUtils.ATTRIBUTE_SCROLLBAR_THUMB_HORIZONTAL:
                ViewUtil.setScrollbarThumbHorizontal(view, resourceId);
                break;
            case SkinAttributesUtils.ATTRIBUTE_SCROLLBAR_TRACK_VERTICAL:
                ViewUtil.setScrollbarTrackVertical(view, resourceId);
                break;
            case SkinAttributesUtils.ATTRIBUTE_SCROLLBAR_TRACK_HORIZONTAL:
                ViewUtil.setScrollbarTrackHorizontal(view, resourceId);
                break;
            case SkinAttributesUtils.ATTRIBUTE_BUTTON:
                if (view instanceof CompoundButton) {
                    CompoundButtonUtil.setButtonDrawable((CompoundButton) view, resourceId);
                }
                break;
            case SkinAttributesUtils.ATTRIBUTE_SWITCH_THUMB:
                if (view instanceof Switch) {
                    SwitchUtil.setSwitchThumb((Switch) view, resourceId);
                } else if (view instanceof AbsSeekBar) {
                    ProgressBarUtil.setAbsSeekBarThumb((AbsSeekBar) view, resourceId);
                }
                break;
            case SkinAttributesUtils.ATTRIBUTE_SWITCH_TRACK:
                if (view instanceof Switch) {
                    SwitchUtil.setSwitchTrack((Switch) view, resourceId);
                }
                break;
            case SkinAttributesUtils.ATTRIBUTE_TEXT_COLOR_HINT:
                if (view instanceof TextView) {
                    TextViewUtil.setHintTextColor(((TextView) view), resourceId);
                }
                break;
            case SkinAttributesUtils.ATTRIBUTE_TEXT_COLOR_HIGH_LIGHT:
                if (view instanceof TextView) {
                    TextViewUtil.setTextColorHighlight(((TextView) view), resourceId);
                }
                break;
            case SkinAttributesUtils.ATTRIBUTE_TEXT_COLOR_LINK:
                if (view instanceof TextView) {
                    TextViewUtil.setTextColorLink(((TextView) view), resourceId);
                }
                break;
            case SkinAttributesUtils.ATTRIBUTE_LIST_VIEW_DIVIDER:
                if (view instanceof ListView) {
                    ListViewUtil.setDivider(((ListView) view), resourceId);
                }
                break;
            case SkinAttributesUtils.ATTRIBUTE_TAB_INDICATOR_COLOR:
                if (view instanceof TabLayout) {
                    TabLayoutUtil.setSelectedTabIndicatorColor(((TabLayout) view), resourceId);
                }
                break;
            case SkinAttributesUtils.ATTRIBUTE_TAB_INDICATOR:
                if (view instanceof TabLayout) {
                    TabLayoutUtil.setSelectedTabIndicator(((TabLayout) view), resourceId);
                }
                break;
            case SkinAttributesUtils.ATTRIBUTE_TAB_SELECTED_TEXT_COLOR:
                if (view instanceof TabLayout) {
                    TabLayoutUtil.setTabSelectTextColors(((TabLayout) view), resourceId);
                }
                break;
            case SkinAttributesUtils.ATTRIBUTE_TAB_TEXT_COLOR:
                if (view instanceof TabLayout) {
                    TabLayoutUtil.setTabTextColors(((TabLayout) view), resourceId);
                }
                break;
            case SkinAttributesUtils.ATTRIBUTE_TEXT_CURSOR_DRAWABLE:
                if (view instanceof EditText) {
                    TextViewUtil.setTextCursorDrawable(((EditText) view), resourceId);
                }
                break;
            default:
                break;
        }
    }
}
