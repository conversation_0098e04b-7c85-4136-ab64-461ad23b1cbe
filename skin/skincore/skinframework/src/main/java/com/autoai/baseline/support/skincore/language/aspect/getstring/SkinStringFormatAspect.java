//package com.autoai.baseline.support.skincore.aop.getstring;
//
//import com.autoai.baseline.support.skincore.SkinConfigs;
//import com.autoai.baseline.support.skincore.SkinLogger;
//
//import org.aspectj.lang.ProceedingJoinPoint;
//import org.aspectj.lang.Signature;
//import org.aspectj.lang.annotation.Around;
//import org.aspectj.lang.annotation.Aspect;
//import org.aspectj.lang.annotation.Pointcut;
//import org.aspectj.lang.reflect.SourceLocation;
//
//import java.util.Locale;
//
///**
// * String.format
// *
// * <AUTHOR>
// */
//@SuppressWarnings("unused")
//@Aspect
//public class SkinStringFormatAspect {
//
//    public static SkinStringFormatAspect aspectOf() {
//        return new SkinStringFormatAspect();
//    }
//
//    @Pointcut("call(* java.lang.String.format(..)) " + SkinConfigs.AOP_WITHOUT
//            + "&& !within(com.autoai.avs.common.base.log.L) "
//            + "&& !within(okhttp3.internal..*) "
//            + "&& !within(com.getkeepsafe..*) "
//            + "&& !within(com.autoai.xcrash..*) "
//            + "&& !within(com.autoai.media.center.util.Logger) "
//            + "&& !within(com.alibaba.android.arouter..*) "
//            + "&& !within(me.jessyan.autosize..*) "
//            + "&& !within(com.tencent.bugly..*) "
//    )
//    public void stringFormatPointcut() {
//    }
//
//    @Around("stringFormatPointcut()")
//    public Object aroundStringFormat(ProceedingJoinPoint joinPoint) throws Throwable {
//        SourceLocation sourceLocation = joinPoint.getSourceLocation();
//        if (SkinLogger.isLoggable()) {
//            SkinLogger.printStackTrace("String.format");
//            Signature signature = joinPoint.getSignature();
//            String signatureName = null;
//            if (signature != null) {
//                signatureName = signature.getName();
//            }
//            SkinLogger.d("AOP StringFormat << SkinStringFormatAspect-->"
//                    + sourceLocation.getFileName()
//                    + ":" + sourceLocation.getLine()
//                    + ", target = " + joinPoint.getTarget()
//                    + ", signatureName = " + signatureName
//            );
//        }
//
//        if (SkinConfigs.isSupportTextStr()) {
//            Object[] params = joinPoint.getArgs();
//            switch (params.length) {
//                case 2: {
//                    Object paramStr = params[0];
//                    Object paramArgs = params[1];
//                    if (paramStr instanceof String) {
//                        String str = (String) paramStr;
//                        return SkinStringUtil.formatJoinPoint(joinPoint, null, str, paramArgs);
//                    }
//                }
//                break;
//                case 3: {
//                    Object param0 = params[0];
//                    Object paramStr = params[1];
//                    Object paramArgs = params[2];
//                    if (param0 instanceof Locale) {
//                        Locale locale = (Locale) param0;
//                        if (paramStr instanceof String) {
//                            String str = (String) paramStr;
//                            return SkinStringUtil.formatJoinPoint(joinPoint, locale, str, paramArgs);
//                        }
//                    }
//                }
//                break;
//                default:
//                    break;
//            }
//        }
//        return joinPoint.proceed();
//    }
//}
