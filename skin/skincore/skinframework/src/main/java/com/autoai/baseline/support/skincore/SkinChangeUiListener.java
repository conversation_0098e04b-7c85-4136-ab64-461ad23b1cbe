package com.autoai.baseline.support.skincore;

import com.autoai.baseline.support.skincore.daynight.DatNightMode;

import java.util.Locale;
/**
 * 换肤、切换昼夜模式、语言切换（如果开启了文言支持）通知回调
 *
 * <AUTHOR>
 */
public interface SkinChangeUiListener {
    /**
     * 换肤、切换昼夜模式、语言切换（如果开启了文言支持）都会触发此通知回调
     *
     * @param skinNickName 当前皮肤包昵称
     * @param dayNightMode 当前昼夜模式
     * @param locale       语言环境
     */
    void change(String skinNickName, DatNightMode dayNightMode, Locale locale);
}
