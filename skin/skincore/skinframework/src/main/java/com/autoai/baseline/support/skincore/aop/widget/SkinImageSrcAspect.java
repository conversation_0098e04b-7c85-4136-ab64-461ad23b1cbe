package com.autoai.baseline.support.skincore.aop.widget;

import android.widget.ImageView;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.applier.DynamicCodingApplier;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinImageSrcAspect {

    public static SkinImageSrcAspect aspectOf() {
        return new SkinImageSrcAspect();
    }

    /**
     * 任何一个包下面的任何一个类下面的任何一个带有SkinActivityInit的方法，构成了这个切面
     */
    @Pointcut("call(* android.widget.ImageView+.setImageResource(..)) "
            + "&& !within(com.bumptech.glide.request.target.*) "
            + SkinConfigs.AOP_WITHOUT
    )
    public void setSrcPointcut() {
    }

    /**
     * 拦截方法
     */
    @Around("setSrcPointcut()")
    public Object aroundSrc(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        if (target instanceof ImageView) {
            Object[] params = joinPoint.getArgs();
            ImageView imageView = (ImageView) target;
            if (params.length > 0) {
                Object params1 = params[0];
                if (params1 instanceof Integer) {
                    int resourceId = (Integer) params1;
//                    SkinLogger.d("排查ImageDrawable SkinImageSrcAspect SkinDynamicCoding.setImageSrc 2 resId = " + resourceId);
                    DynamicCodingApplier.setImageSrc(imageView, resourceId);
                    return null;
                }
            }
        }
        //执行拦截方法
        return joinPoint.proceed();
    }
}
