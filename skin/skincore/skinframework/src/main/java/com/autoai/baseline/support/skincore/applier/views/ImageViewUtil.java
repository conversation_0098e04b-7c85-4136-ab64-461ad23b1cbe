package com.autoai.baseline.support.skincore.applier.views;

import android.graphics.drawable.AnimationDrawable;
import android.graphics.drawable.Drawable;
import android.widget.ImageView;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.res.ResManager;

/**
 * ImageView 资源应用
 *
 * <AUTHOR>
 */
public class ImageViewUtil {
    /**
     * 隐藏构造方法，不允许创建实例
     */
    private ImageViewUtil() {
    }

    /**
     * ImageView 设置图片
     */
    public static void setImageSrc(final ImageView imageView, int resId) {
        if (resId == SkinConfigs.ID_NULL) {
            return;
        }
//        SkinLogger.d("切换ImageView src");
        final Drawable newDrawable = ResManager.getInstance().getDrawable(resId);
        if (newDrawable != null) {
            Drawable preDrawable = imageView.getDrawable();
//            updateDrawableState(resId, preDrawable, newDrawable);
            //
            if (preDrawable instanceof AnimationDrawable) {
                AnimationDrawable animation = (AnimationDrawable) preDrawable;
                if (animation.isRunning()) {
                    animation.stop();
                    imageView.setImageDrawable(newDrawable);
                    if (newDrawable instanceof AnimationDrawable) {
                        ((AnimationDrawable) newDrawable).start();
                    }
                } else {
                    imageView.setImageDrawable(newDrawable);
                }
            } else {
                imageView.setImageDrawable(newDrawable);
            }
        }
    }
}
