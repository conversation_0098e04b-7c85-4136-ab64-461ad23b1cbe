package com.autoai.baseline.support.skincore.aop.widget;

import static com.autoai.baseline.support.skincore.SkinConfigs.ID_NULL;

import android.graphics.drawable.Drawable;
import android.widget.CompoundButton;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.res.ResManager;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.applier.DynamicCodingApplier;
import com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinCompoundButtonDrawableAspect {

    public static SkinCompoundButtonDrawableAspect aspectOf() {
        return new SkinCompoundButtonDrawableAspect();
    }

    /**
     * 任何一个包下面的任何一个类下面的任何一个带有SkinActivityInit的方法，构成了这个切面
     */
    @Pointcut("call(* android.widget.CompoundButton+.setButtonDrawable(..)) " + SkinConfigs.AOP_WITHOUT)
    public void setButtonDrawablePointcut() {
    }

    /**
     * 拦截方法
     */
    @Around("setButtonDrawablePointcut()")
    public Object aroundButtonDrawable(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        if (target instanceof CompoundButton) {
            Object[] params = joinPoint.getArgs();
            CompoundButton compoundButton = (CompoundButton) target;
            if (params.length > 0) {
                Object params1 = params[0];
                if (params1 instanceof Integer) {
                    int resId = (Integer) params1;
                    SkinLogger.d("AOP CompoundButton+.setButtonDrawable resId = " + resId);
                    DynamicCodingApplier.setButtonDrawable(compoundButton, resId);
                    return null;
                } else if (params1 instanceof Drawable) {
                    String key1 = params1.hashCode() + "";
                    Integer resId = ResManager.RES_MAP.get(key1);
                    SkinLogger.d("AOP CompoundButton+.setButtonDrawable resId = " + resId);
                    if (resId != null && resId != ID_NULL) {
                        DynamicCodingApplier.setButtonDrawable(compoundButton, resId);
                        ResManager.RES_MAP.remove(key1);
                        return null;
                    } else {
                        SkinAttributesUtils.removeViewAttribute(compoundButton, SkinAttributesUtils.ATTRIBUTE_BUTTON);
                    }
                } else {
                    SkinAttributesUtils.removeViewAttribute(compoundButton, SkinAttributesUtils.ATTRIBUTE_BUTTON);
                }
            }
        }
        //执行拦截方法
        return joinPoint.proceed();
    }
}
