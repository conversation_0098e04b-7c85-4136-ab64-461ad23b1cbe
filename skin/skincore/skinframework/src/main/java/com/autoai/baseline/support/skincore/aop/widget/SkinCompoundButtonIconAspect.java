package com.autoai.baseline.support.skincore.aop.widget;

import android.widget.CompoundButton;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinCompoundButtonIconAspect {

    public static SkinCompoundButtonIconAspect aspectOf() {
        return new SkinCompoundButtonIconAspect();
    }

    /**
     * 任何一个包下面的任何一个类下面的任何一个带有SkinActivityInit的方法，构成了这个切面
     */
    @Pointcut("call(* android.widget.CompoundButton+.setButtonIcon(..)) " + SkinConfigs.AOP_WITHOUT)
    public void setButtonIconPointcut() {
    }

    /**
     * 拦截方法
     */
    @Around("setButtonIconPointcut()")
    public Object aroundButtonIcon(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        if (target instanceof CompoundButton) {
            SkinLogger.d("AOP CompoundButton+.setButtonIcon removeViewAttribute");
            CompoundButton compoundButton = (CompoundButton) target;
            SkinAttributesUtils.removeViewAttribute(compoundButton, SkinAttributesUtils.ATTRIBUTE_BUTTON);
        }
        //执行拦截方法
        return joinPoint.proceed();
    }
}
