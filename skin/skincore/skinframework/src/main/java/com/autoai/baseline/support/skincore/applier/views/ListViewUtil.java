package com.autoai.baseline.support.skincore.applier.views;

import android.graphics.drawable.Drawable;
import android.widget.ListView;

import com.autoai.baseline.support.skincore.res.ResManager;

/**
 * ListView 资源应用
 *
 * <AUTHOR>
 */
public class ListViewUtil {
    /**
     * 隐藏构造方法，不允许创建实例
     */
    private ListViewUtil() {
    }

    public static void setDivider(final ListView listView, int resId) {
        final Drawable newDrawable = ResManager.getInstance().getDrawable(resId);
        if (newDrawable != null) {
//            Drawable oldDrawable = listView.getDivider();
//            updateDrawableState(resId, oldDrawable, newDrawable);
            listView.setDivider(newDrawable);
        }
    }
}
