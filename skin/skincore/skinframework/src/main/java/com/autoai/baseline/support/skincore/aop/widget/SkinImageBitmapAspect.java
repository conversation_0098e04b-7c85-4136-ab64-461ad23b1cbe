package com.autoai.baseline.support.skincore.aop.widget;

import android.widget.ImageView;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinImageBitmapAspect {

    public static SkinImageBitmapAspect aspectOf() {
        return new SkinImageBitmapAspect();
    }

    /**
     * 任何一个包下面的任何一个类下面的任何一个带有SkinActivityInit的方法，构成了这个切面
     */
    @Pointcut("call(* android.widget.ImageView+.setImageBitmap(..)) "
            + SkinConfigs.AOP_WITHOUT
            + "&& !within(com.bumptech.glide.request.target.*) "
    )
    public void imageBitmapPointcut() {
    }

    /**
     * 拦截方法
     */
    @Around("imageBitmapPointcut()")
    public Object aroundImageBitmap(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        if (target instanceof ImageView) {
            ImageView imageView = (ImageView) target;
//            SkinLogger.d("排查ImageBitmap AttributeUtils.removeViewAttribute 5");
            SkinAttributesUtils.removeViewAttribute(imageView, SkinAttributesUtils.ATTRIBUTE_SRC);
        }
        //执行拦截方法
        return joinPoint.proceed();
    }
}
