package com.autoai.baseline.support.skincore.applier.views;

import android.graphics.drawable.Drawable;
import android.widget.CompoundButton;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.res.ResManager;

/**
 * CompoundButton 资源应用
 *
 * <AUTHOR>
 */
public class CompoundButtonUtil {
    /**
     * 隐藏构造方法，不允许创建实例
     */
    private CompoundButtonUtil() {
    }

    public static void setButtonDrawable(final CompoundButton view, int resId) {
        if (resId == SkinConfigs.ID_NULL) {
            return;
        }
        final Drawable drawable = ResManager.getInstance().getDrawable(resId);
        if (drawable != null) {
//            Drawable oldDrawable = view.getButtonDrawable();
            SkinLogger.v("view.getHeight = " + view.getHeight() + ", view.getWidth = " + view.getWidth());
//            updateDrawableState(resId, oldDrawable, drawable);
            view.setButtonDrawable(drawable);
        }
    }
}
