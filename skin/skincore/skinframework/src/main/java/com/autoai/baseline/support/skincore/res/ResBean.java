package com.autoai.baseline.support.skincore.res;

import static com.autoai.baseline.support.skincore.SkinConfigs.ID_NULL;
import static com.autoai.baseline.support.skincore.res.ResManager.MODE_SUFFIX;

import android.annotation.SuppressLint;
import android.content.res.Resources;

import androidx.annotation.NonNull;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;

import java.util.HashMap;
import java.util.Map;

/**
 * 资源信息
 *
 * <AUTHOR>
 */
public class ResBean {
    /**
     * 默认的包名
     */
    private final String pkgName;
    /**
     * 皮肤包的包名
     */
    private static String skinPkgName = "";
    /**
     * 资源类型
     */
    private final String resType;
    /**
     * 日间资源名称
     */
    private final String resName;
    /**
     * 默认的日间资源id
     */
    private final int resId;
    /**
     * 夜间资源名称
     */
    private final String resNameNight;
    /**
     * 默认的夜间资源id
     */
    private int nightResId = -1;
    /**
     * 皮肤包的日间资源id
     */
    private final Map<String, Integer> skinResIdMap = new HashMap<>(4);
    /**
     * 皮肤包的夜间资源id
     */
    private final Map<String, Integer> skinNightResIdMap = new HashMap<>(4);

    /**
     * 构造方法
     *
     * @param resId   默认的日间资源id
     * @param resName 日间资源名称
     * @param resType 资源类型
     * @param pkgName 默认的包名
     */
    public ResBean(int resId, String resName, String resType, String pkgName) {
        this.resId = resId;
        this.resName = resName;
        this.resNameNight = resName + MODE_SUFFIX;
        this.resType = resType;
        this.pkgName = pkgName;
    }

    /**
     * 设置皮肤包包名
     */
    public static void setSkinPkgName(String name) {
        skinPkgName = name;
    }

    /**
     * 获取 默认的日间资源id
     */
    public int getResId() {
        return resId;
    }

    /**
     * 获取 默认的夜间资源id
     */
    @SuppressLint("DiscouragedApi")
    public int getNightResId(Resources resources) {
        if (resources != null && nightResId == -1) {
            nightResId = resources.getIdentifier(resNameNight, resType, pkgName);
        }
        return nightResId;
    }

    /**
     * 获取 皮肤包的日间资源id
     */
    @SuppressLint("DiscouragedApi")
    public int getSkinResId(Resources skinResource) {
        Integer skinResId = skinResIdMap.get(SkinConfigs.getSkinNickName());
        if (skinResId == null) {
            if (skinResource != null) {
                int id = skinResource.getIdentifier(resName, resType, skinPkgName);
                skinResIdMap.put(SkinConfigs.getSkinNickName(), id);
                SkinLogger.i("getSkinResId 第一次获取 id = " + id + ", resName = " + resName + ", resType = " + resType + ", skinPkgName = " + skinPkgName + ", skinResource = " + skinResource);
                return id;
            } else {
                return ID_NULL;
            }
        } else {
            SkinLogger.i("getSkinResId 从Map拿取 id = " + skinResId + ", resName = " + resName + ", resType = " + resType + ", skinPkgName = " + skinPkgName + ", skinResource = " + skinResource);
            return skinResId;
        }
    }

    /**
     * 获取 皮肤包的夜间资源id
     */
    @SuppressLint("DiscouragedApi")
    public int getSkinNightResId(Resources skinResource) {
        Integer skinNightResId = skinNightResIdMap.get(SkinConfigs.getSkinNickName());
        if (skinNightResId == null) {
            if (skinResource != null) {
                int id = skinResource.getIdentifier(resNameNight, resType, skinPkgName);
                skinNightResIdMap.put(SkinConfigs.getSkinNickName(), id);
                SkinLogger.i("getSkinNightResId 第一次获取 id = " + id + ", resNameNight = " + resNameNight + ", resType = " + resType + ", skinPkgName = " + skinPkgName + ", skinResource = " + skinResource);
                return id;
            } else {
                return ID_NULL;
            }
        } else {
            SkinLogger.i("getSkinNightResId 从Map拿取 id = " + skinNightResId + ", resNameNight = " + resNameNight + ", resType = " + resType + ", skinPkgName = " + skinPkgName + ", skinResource = " + skinResource);
            return skinNightResId;
        }
    }

    /**
     * 获取日间资源名称
     */
    public String getResName() {
        return resName;
    }

    /**
     * 获取资源类型
     */
    public String getResType() {
        return resType;
    }

    /**
     * 获取默认包名
     */
    public String getPkgName() {
        return pkgName;
    }

    @NonNull
    @Override
    public String toString() {
        return "ResBean { resName = " + resName
                + ", resNameNight = " + resNameNight
                + ", resType = " + resType
                + ", resId = " + resId
                + ", nightResId = " + nightResId
                + ", pkgName = " + pkgName
                + ", skinResId = " + skinResIdMap.get(SkinConfigs.getSkinNickName())
                + ", skinNightResId = " + skinNightResIdMap.get(SkinConfigs.getSkinNickName())
                + ", skinPkgName = " + skinPkgName
                + " }";
    }
}
