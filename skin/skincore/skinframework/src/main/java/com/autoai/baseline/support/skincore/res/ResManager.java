package com.autoai.baseline.support.skincore.res;

import static com.autoai.baseline.support.skincore.SkinLogger.excludeList;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.res.AssetManager;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.os.LocaleList;
import android.text.TextUtils;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.SkinManager;
import com.autoai.baseline.support.skincore.daynight.DayNightUtil;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * 资源管理类
 *
 * <AUTHOR>
 */
public class ResManager {
    /**
     * 夜间模式资源，在后面加 _n 后缀
     */
    public static final String MODE_SUFFIX = "_n";
    public static final String TYPE_COLOR = "color";
    @SuppressWarnings("unused")
    public static final String TYPE_DRAWABLE = "drawable";
    /**
     * 缓存资源对象
     */
    public static final Map<String, Integer> RES_MAP = new HashMap<>();
    public static final String COLOR_KEY_HEAD = "color_";
    /**
     * 用于优化换肤资源查询速度，对内容进行缓存
     */
    private final Map<String, Boolean> NO_RES_ID_MAP = new HashMap<>();
    /**
     * 用于优化换肤资源查询速度，对内容进行缓存
     */
    private static final Map<String, ResBean> RES_BEAN_MAP = new HashMap<>();
    private Locale locale;
    private boolean isEnglish = false;
    private boolean isLocaleChange = true;
    /**
     * 默认资源
     */
    private Resources resources;
    /**
     * 默认主题
     */
    private Resources.Theme theme = null;
    /**
     * 皮肤资源
     */
    private Resources skinResources;
    /**
     * 皮肤主题
     */
    private Resources.Theme skinTheme = null;

    private boolean isUseSkin = false;

    private ResManager() {
    }

    private static final class Holder {
        private static final ResManager INSTANCE = new ResManager();
    }

    public static ResManager getInstance() {
        return Holder.INSTANCE;
    }

    public Locale getLocale() {
        return locale;
    }

    /**
     * 重装context中的resource资源
     *
     * @param skinPath 资源路径
     * @param locale   语言类型，可为空
     */
    @SuppressWarnings("all")
    public void resetResource(String skinPath, Locale locale) {
        this.locale = locale;
        isLocaleChange = true;
        Context context = SkinManager.getInstance().getApplicationContext();
        SkinLogger.forceI("resetResource context = " + context + ", skinPath: " + skinPath + " locale: " + locale);
        if (context == null) {
            return;
        }
        this.theme = context.getTheme();
        if (locale != null) {
            this.resources = LocaleUtil.changeLocale(context.getResources(), locale);
        } else {
            this.resources = context.getResources();
        }

        //皮肤处理：如果加载过的皮肤包不需要重新加载
        Resources skinResource = null;
        if (!TextUtils.isEmpty(skinPath)) {
            try {
                AssetManager assetManager = AssetManager.class.newInstance();
                // 添加资源进入资源管理器
                Method addAssetPath = assetManager.getClass().getMethod("addAssetPath", String.class);
                addAssetPath.setAccessible(true);
                addAssetPath.invoke(assetManager, skinPath);
                //
                //当前应用的resources对象，获取到屏幕相关的参数和配置
                skinResource = new Resources(assetManager, this.resources.getDisplayMetrics(), this.resources.getConfiguration());
                //配置语言环境
                if (locale != null) {
                    skinResource = LocaleUtil.changeLocale(skinResource, locale);
                }
                //获取外部Apk(皮肤包) 包名
                PackageManager mPm = context.getPackageManager();
                PackageInfo info = mPm.getPackageArchiveInfo(skinPath, PackageManager.GET_ACTIVITIES);
                ResBean.setSkinPkgName(info.packageName);
            } catch (Exception e) {
                SkinLogger.forceE("resetResource skinPath: " + skinPath + ", locale: " + locale, e);
            }
        }
        // fix: restore 不生效:TOYOTA25MM-14472【1A】【系统测试】【机型-T1】【A2】【Navi】【bench】进行主题换肤后，将主题切换为默认主题，导航按钮及画面仍显示主题皮肤
        this.skinResources = skinResource;
        this.isUseSkin = skinResource != null;
        this.skinTheme = this.isUseSkin ? skinResource.newTheme() : null;
    }


    /**
     * 当前是否是英文环境
     */
    public boolean isEnglish() {
        if (isLocaleChange) {
            isLocaleChange = false;
            if (locale == null) {
                if (resources == null) {
                    isEnglish = false;
                } else {
                    LocaleList localeList = resources.getConfiguration().getLocales();
                    locale = localeList.get(0);
                    if (locale == null) {
                        isEnglish = false;
                    } else {
                        String english = Locale.ENGLISH.getLanguage();
                        String current = locale.getLanguage();
                        SkinLogger.v("Locale.ENGLISH = " + english + ", locale = " + current);
                        isEnglish = TextUtils.equals(english, current);
                    }
                }
            } else {
                String english = Locale.ENGLISH.getLanguage();
                String current = locale.getLanguage();
                SkinLogger.v("Locale.ENGLISH = " + english + ", locale = " + current);
                isEnglish = TextUtils.equals(english, current);
            }
        }
        SkinLogger.v("isEnglish = " + isEnglish);
        return isEnglish;
    }

    public boolean isUseSkin() {
        return isUseSkin;
    }

    public Resources getResources() {
        return resources;
    }

    public Resources getSkinResources() {
        return skinResources;
    }

    /**
     * 没有黑夜资源，并且没有皮肤包的资源
     */
    public boolean noResId(int resId) {
        Boolean result = NO_RES_ID_MAP.get(resId + "");
        if (result == null) {
            ResBean resBean = getResBean(resId);
            int nightResId = resBean.getNightResId(this.resources);
            boolean noNightResId = nightResId == SkinConfigs.ID_NULL;
            if (noNightResId) {
                if (isUseSkin) {
                    //皮肤包中对应资源
                    int skinResId = resBean.getSkinResId(this.skinResources);
                    if (skinResId == SkinConfigs.ID_NULL) {
                        int skinNightResId = resBean.getSkinNightResId(this.skinResources);
                        result = skinNightResId == SkinConfigs.ID_NULL;
                    } else {
                        result = false;
                    }
                } else {
                    result = true;
                }
            } else {
                result = false;
            }
            NO_RES_ID_MAP.put(resId + "", result);
        }
        return result;
    }


    public ResBean getResBean(int resId) {
        String key = resId + "";
        ResBean resBean = RES_BEAN_MAP.get(key);
        if (resBean == null) {
            String str = resources.getResourceName(resId);
            //
            String[] split = str.split(":");
            String pkgName = null;
            String resType = null;
            String resName = null;
            if (split.length > 1) {
                pkgName = split[0];
                //
                String splitStr = split[1];
                String[] split2 = splitStr.split("/");
                if (split2.length > 1) {
                    resType = split2[0];
                    resName = split2[1];
                }
            }
//            SkinLogger.forceV("getResBean --> pkgName = " + pkgName + ", resType = " + resType + ", resName = " + resName);
            if (TextUtils.isEmpty(pkgName)) {
                pkgName = resources.getResourcePackageName(resId);
            }
            if (TextUtils.isEmpty(resName)) {
                resName = resources.getResourceEntryName(resId);
            }
            if (TextUtils.isEmpty(resType)) {
                resName = resources.getResourceTypeName(resId);
            }
            resBean = new ResBean(resId, resName, resType, pkgName);
            RES_BEAN_MAP.put(key, resBean);
        }
        if (SkinLogger.isLoggable()) {
            if (!excludeList.contains(resBean.getResName())) {
                SkinLogger.v("getResBean--> " + resBean);
            }
        }
        return resBean;
    }

    /**
     * 获取当前id对应的夜间模式的资源id
     */
    public int getNightResId(int resId) {
        ResBean resBean = getResBean(resId);
        int nightResId = resBean.getNightResId(this.resources);
        if (nightResId != SkinConfigs.ID_NULL) {
            return nightResId;
        } else {
            return resId;
        }
    }

    /**
     * 根据默认资源resId获取皮肤包中对应的resId，同名替换
     *
     * @return 0 返回0表示皮肤包中没有对应的资源
     */
    public int getSkinResId(int resId) {
        if (this.isUseSkin) {
            ResBean resBean = getResBean(resId);
            //皮肤包中对应资源
            return resBean.getSkinResId(this.skinResources);
        } else {
            return SkinConfigs.ID_NULL;
        }
    }

    /**
     * 获取颜色值
     */
    public int getColor(int resId) {
        if (resId == SkinConfigs.ID_NULL) {
            SkinLogger.w("getColor --> resId is ID_NULL");
            return 0;
        }
        int color;
        if (DayNightUtil.isNight()) {
            ResBean resBean = getResBean(resId);
            if (this.isUseSkin) {
                int skinNightResId = resBean.getSkinNightResId(this.skinResources);
                if (skinNightResId != SkinConfigs.ID_NULL) {
                    color = this.skinResources.getColor(skinNightResId, this.skinTheme);
                } else {
                    SkinLogger.w("getColor --> skinNightResId is ID_NULL-->" + resBean.getResName());
                    color = getDefaultNightColor(resBean);
                }
            } else {
                color = getDefaultNightColor(resBean);
            }
        } else {
            if (this.isUseSkin) {
                ResBean resBean = getResBean(resId);
                int skinResId = resBean.getSkinResId(this.skinResources);
                if (skinResId != SkinConfigs.ID_NULL) {
                    color = this.skinResources.getColor(skinResId, this.skinTheme);
                } else {
                    SkinLogger.w("getColor --> skinResId is ID_NULL-->" + resBean.getResName());
                    color = this.resources.getColor(resId, this.theme);
                }
            } else {
                color = this.resources.getColor(resId, this.theme);
            }
        }
        if (color != 0) {
            RES_MAP.put(COLOR_KEY_HEAD + color, resId);
        }
        return color;
    }

    /**
     * 获取默认资源的 夜间模式 的 Color
     */
    private int getDefaultNightColor(ResBean resBean) {
        int nightResId = resBean.getNightResId(this.resources);
        if (SkinLogger.isShowDebuggingLog()) {
            SkinLogger.w("getColor --> " + resBean);
        }
        if (nightResId != SkinConfigs.ID_NULL) {
            return this.resources.getColor(nightResId, this.theme);
        } else {

            return this.resources.getColor(resBean.getResId(), this.theme);
        }
    }

    /**
     * 获取selector类型色值
     */
    @Nullable
    public ColorStateList getColorStateList(TextView view, int resId) {
//        StringBuilder stringBuilder = new StringBuilder();
        if (resId == SkinConfigs.ID_NULL) {
            SkinLogger.w("getColorStateList --> resId is ID_NULL");
            return null;
        }
//        stringBuilder.append("view = ").append(view).append("\n");
        ColorStateList colorStateList;
        if (DayNightUtil.isNight()) {
//            stringBuilder.append("isNight = true");
            ResBean resBean = getResBean(resId);
            if (this.isUseSkin) {
//                stringBuilder.append(", isUseSkin = true");
                int skinNightResId = resBean.getSkinNightResId(this.skinResources);
//                stringBuilder.append(", skinNightResId = ").append(skinNightResId);
                if (skinNightResId != SkinConfigs.ID_NULL) {
                    colorStateList = this.skinResources.getColorStateList(skinNightResId, this.skinTheme);
//                    stringBuilder.append(", skinResources.getColorStateList::resBean = ").append(resBean);
                } else {
//                    SkinLogger.w("getColorStateList --> skinNightResId is ID_NULL-->" + resBean.getResName());
                    colorStateList = getDefaultNightColorStateList(resBean);
//                    stringBuilder.append(", getDefaultNightColorStateList 1::resBean = ").append(resBean);
                }
            } else {
//                stringBuilder.append(", isUseSkin = false ");
                colorStateList = getDefaultNightColorStateList(resBean);
//                stringBuilder.append(", getDefaultNightColorStateList 2::resBean = ").append(resBean);
            }
        } else {
//            stringBuilder.append("isNight = false");
            if (this.isUseSkin) {
//                stringBuilder.append(", isUseSkin = true");
                ResBean resBean = getResBean(resId);
                int skinResId = resBean.getSkinResId(this.skinResources);
//                stringBuilder.append(", skinNightResId = ").append(skinResId);
                if (skinResId != SkinConfigs.ID_NULL) {
                    colorStateList = this.skinResources.getColorStateList(skinResId, this.skinTheme);
//                    stringBuilder.append(", skinResources.getColorStateList::resBean = ").append(resBean);
                } else {
//                    SkinLogger.w(", getColorStateList --> skinResId is ID_NULL-->" + resBean.getResName());
                    colorStateList = this.resources.getColorStateList(resId, this.theme);
//                    stringBuilder.append(", resources.getColorStateList::resBean = ").append(resBean);
                }
            } else {
//                stringBuilder.append(", isUseSkin = false");
                colorStateList = this.resources.getColorStateList(resId, this.theme);
//                stringBuilder.append(", resources.getColorStateList::resBean = ");
            }
        }
        RES_MAP.put(colorStateList.hashCode() + "", resId);
//        if (view != null) {
//            String text = view.getText().toString();
//            if ("去这里".equals(text)) {
//                stringBuilder.append(" \n结果：getColorStateList -->  colorStateList-->").append(colorStateList);
//                SkinLogger.forceE("---------------------去这里------------------------\n" + stringBuilder);
//            }
//        }
        return colorStateList;
    }

    /**
     * 获取默认资源的 夜间模式 的 ColorStateList
     */
    @NonNull
    private ColorStateList getDefaultNightColorStateList(ResBean resBean) {
        int nightResId = resBean.getNightResId(this.resources);
        if (nightResId != SkinConfigs.ID_NULL) {
            if (SkinLogger.isShowDebuggingLog()) {
                SkinLogger.w("getColorStateList --> nightResId is --> " + resBean);
            }
            return this.resources.getColorStateList(nightResId, this.theme);
        } else {
            if (SkinLogger.isShowDebuggingLog()) {
                SkinLogger.w("getColorStateList --> nightResId is ID_NULL --> " + resBean);
            }
            return this.resources.getColorStateList(resBean.getResId(), this.theme);
        }
    }

    /**
     * 获取 Drawable
     */
    @Nullable
    public Drawable getDrawable(int resId) {
        if (resId == SkinConfigs.ID_NULL) {
            SkinLogger.w("getDrawable --> resId is ID_NULL");
            return null;
        }
        Drawable drawable;
        if (DayNightUtil.isNight()) {
            //夜间模式
            ResBean resBean = getResBean(resId);
            if (this.isUseSkin) {
                //换肤
                int skinNightResId = resBean.getSkinNightResId(this.skinResources);
                if (skinNightResId != SkinConfigs.ID_NULL) {
                    if (SkinLogger.isShowDebuggingLog()) {
                        SkinLogger.d("getDrawable --> skinResources.getDrawable(skinNightResId, this.skinTheme) --> " + resBean);
                    }
                    drawable = DrawableUtil.getDrawable(this.skinResources, skinNightResId, this.skinTheme);
                } else {
                    if (SkinLogger.isShowDebuggingLog()) {
                        SkinLogger.w("getDrawable --> getDefaultNightDrawable 1 -->" + resBean);
                    }
                    return getDefaultNightDrawable(resBean);
                }
            } else {
                if (SkinLogger.isShowDebuggingLog()) {
                    SkinLogger.d("getDrawable --> getDefaultNightDrawable 2 -->" + resBean);
                }
                return getDefaultNightDrawable(resBean);
            }
        } else {
            if (this.isUseSkin) {
                //换肤
                ResBean resBean = getResBean(resId);
                int skinResId = resBean.getSkinResId(this.skinResources);
                if (skinResId != SkinConfigs.ID_NULL) {
                    if (SkinLogger.isShowDebuggingLog()) {
                        SkinLogger.d("getDrawable --> skinResources.getDrawable(skinResId, this.skinTheme) --> " + resBean);
                    }
                    drawable = DrawableUtil.getDrawable(this.skinResources, skinResId, this.skinTheme);
                } else {
                    if (SkinLogger.isShowDebuggingLog()) {
                        SkinLogger.w("getDrawable --> skinResId is ID_NULL-->" + resBean);
                    }
                    drawable = DrawableUtil.getDrawable(this.resources, resId, this.theme);
                }
            } else {
                if (SkinLogger.isShowDebuggingLog()) {
                    SkinLogger.d("getDrawable --> resources.getDrawable(resId, this.theme)");
                }
                drawable = DrawableUtil.getDrawable(this.resources, resId, this.theme);
            }
        }
        if (drawable != null) {
            RES_MAP.put(drawable.hashCode() + "", resId);
        }
        return drawable;
    }

    /**
     * 获取默认资源的 夜间模式 的 Drawable
     */
    @Nullable
    private Drawable getDefaultNightDrawable(ResBean resBean) {
        int nightResId = resBean.getNightResId(this.resources);
        if (nightResId != SkinConfigs.ID_NULL) {
            if (SkinLogger.isShowDebuggingLog()) {
                SkinLogger.w("getDefaultNightDrawable --> nightResId --> " + resBean);
            }
            return DrawableUtil.getDrawable(this.resources, nightResId, this.theme);
        } else {
            if (SkinLogger.isShowDebuggingLog()) {
                SkinLogger.w("getDefaultNightDrawable --> nightResId is ID_NULL --> " + resBean);
            }
            return DrawableUtil.getDrawable(this.resources, resBean.getResId(), this.theme);
        }
    }

//    /**
//     * 获得字体
//     */
//    @SuppressWarnings("unused")
//    public Typeface getTypeface(int resId) {
//        if (resId == ID_NULL) {
//            return Typeface.DEFAULT;
//        }
//        ResBean resBean = getResBean(resId);
//        String resName = resBean.getResName();
//        String resType = resBean.getResType();
//        String pkgName = resBean.getPkgName();
//        if (DayNightManager.isNight()) {
//            resId = getIdentifier(resources, resBean.getResNameNight(), resType, this.skinPkgName);
//        }
//        String skinTypefacePath;
//        Typeface typeface = Typeface.DEFAULT;
//        //
//        if (this.isUseSkin) {
//            int skinResId = getIdentifier(this.skinResources, resName, resType, this.skinPkgName);
//            if (skinResId != ID_NULL) {
//                skinTypefacePath = this.skinResources.getString(skinResId, this.skinTheme);
//            } else {
//                skinTypefacePath = this.resources.getString(resId, this.theme);
//            }
//            if (!TextUtils.isEmpty(skinTypefacePath)) {
//                try {
//                    typeface = Typeface.createFromAsset(this.skinResources.getAssets(), skinTypefacePath);
//                } catch (RuntimeException e) {
//                    SkinLogger.w("getTypeface: ", e);
//                }
//            }
//        } else {
//            skinTypefacePath = this.resources.getString(resId, this.theme);
//            if (!TextUtils.isEmpty(skinTypefacePath)) {
//                try {
//                    typeface = Typeface.createFromAsset(this.resources.getAssets(), skinTypefacePath);
//                } catch (RuntimeException e) {
//                    SkinLogger.w("getTypeface: ", e);
//                }
//            }
//        }
//        SkinLogger.d("getTypeface --> isNightMode = " + DayNightManager.isNight() + ", resName = " + resName + ", resType = " + resType + ", nightResId = " + resId + ", typeface = " + typeface + ", pkgName " + pkgName);
//        return typeface;
//    }

}