package com.autoai.baseline.support.skincore.attribute;

import android.view.View;

import com.autoai.baseline.support.skincore.R;
import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.language.bean.StringBean;

import java.util.HashMap;
import java.util.Map;

/**
 * 换肤支持的属性配置
 *
 * <AUTHOR>
 */
public class SkinAttributesUtils {
    /**
     * "skinEnable" 资源命名空间
     */
    public static final String NAME_SPACE = "http://schemas.android.com/apk/res-auto";
    /**
     * 用于标记当前View时候需要进行换肤
     */
    public static final String SKIN_ENABLE = "skinEnable";
    public static final String ATTRIBUTE_BACKGROUND = "background";

    //
    public static final String ATTRIBUTE_SRC = "src";

    public static final String ATTRIBUTE_TEXT = "text";
    public static final String ATTRIBUTE_HINT = "hint";
    //
    public static final String ATTRIBUTE_TEXT_COLOR = "textColor";
    public static final String ATTRIBUTE_TEXT_COLOR_HINT = "textColorHint";
    public static final String ATTRIBUTE_TEXT_COLOR_HIGH_LIGHT = "textColorHighlight";
    public static final String ATTRIBUTE_TEXT_COLOR_LINK = "textColorLink";
    //
    public static final String ATTRIBUTE_PROGRESS_DRAWABLE = "progressDrawable";
    //
    public static final String ATTRIBUTE_DRAWABLE_LEFT = "drawableLeft";
    public static final String ATTRIBUTE_DRAWABLE_START = "drawableStart";
    public static final String ATTRIBUTE_DRAWABLE_TOP = "drawableTop";
    public static final String ATTRIBUTE_DRAWABLE_RIGHT = "drawableRight";
    public static final String ATTRIBUTE_DRAWABLE_END = "drawableEnd";
    public static final String ATTRIBUTE_DRAWABLE_BOTTOM = "drawableBottom";

    //Build.VERSION_CODES.Q (Android 29)开始支持的属性
    public static final String ATTRIBUTE_SCROLLBAR_THUMB_VERTICAL = "scrollbarThumbVertical";
    public static final String ATTRIBUTE_SCROLLBAR_THUMB_HORIZONTAL = "scrollbarThumbHorizontal";
    public static final String ATTRIBUTE_SCROLLBAR_TRACK_VERTICAL = "scrollbarTrackVertical";
    public static final String ATTRIBUTE_SCROLLBAR_TRACK_HORIZONTAL = "scrollbarTrackHorizontal";
    //
    public static final String ATTRIBUTE_BUTTON = "button";
    public static final String ATTRIBUTE_SWITCH_THUMB = "thumb";
    public static final String ATTRIBUTE_SWITCH_TRACK = "track";
    public static final String ATTRIBUTE_FOREGROUND = "foreground";

    /**
     * 换肤支持的属性 ListView分割线
     */
    public static final String ATTRIBUTE_LIST_VIEW_DIVIDER = "divider";

    public static final String ATTRIBUTE_INDETERMINATE_DRAWABLE = "indeterminateDrawable";

    public static final String ATTRIBUTE_TAB_INDICATOR_COLOR = "tabIndicatorColor";

    public static final String ATTRIBUTE_TAB_INDICATOR = "tabIndicator";

    public static final String ATTRIBUTE_TAB_SELECTED_TEXT_COLOR = "tabSelectedTextColor";
    public static final String ATTRIBUTE_TAB_TEXT_COLOR = "tabTextColor";

    public static final String ATTRIBUTE_TEXT_CURSOR_DRAWABLE = "textCursorDrawable";

    /**
     * 定义换肤设计的View的属性
     */
    public static final Map<Integer, String> SUPPORT_ATTR = new HashMap<>();

    public static void init() {
        SUPPORT_ATTR.put(R.styleable.skin_skinEnable, SKIN_ENABLE);
        SUPPORT_ATTR.put(R.styleable.skin_android_background, ATTRIBUTE_BACKGROUND);
        SUPPORT_ATTR.put(R.styleable.skin_android_src, ATTRIBUTE_SRC);
        SUPPORT_ATTR.put(R.styleable.skin_android_textColor, ATTRIBUTE_TEXT_COLOR);
        if (SkinConfigs.isSupportTextStr()) {
            SUPPORT_ATTR.put(R.styleable.skin_android_text, ATTRIBUTE_TEXT);
            SUPPORT_ATTR.put(R.styleable.skin_android_hint, ATTRIBUTE_HINT);
            SkinLogger.d("SUPPORT_ATTR  add ATTRIBUTE_TEXT、ATTRIBUTE_HINT");
        }
        SUPPORT_ATTR.put(R.styleable.skin_android_drawableLeft, ATTRIBUTE_DRAWABLE_LEFT);
        SUPPORT_ATTR.put(R.styleable.skin_android_drawableStart, ATTRIBUTE_DRAWABLE_START);
        SUPPORT_ATTR.put(R.styleable.skin_android_drawableTop, ATTRIBUTE_DRAWABLE_TOP);
        SUPPORT_ATTR.put(R.styleable.skin_android_drawableRight, ATTRIBUTE_DRAWABLE_RIGHT);
        SUPPORT_ATTR.put(R.styleable.skin_android_drawableEnd, ATTRIBUTE_DRAWABLE_END);
        SUPPORT_ATTR.put(R.styleable.skin_android_drawableBottom, ATTRIBUTE_DRAWABLE_BOTTOM);
        SUPPORT_ATTR.put(R.styleable.skin_android_progressDrawable, ATTRIBUTE_PROGRESS_DRAWABLE);
        SUPPORT_ATTR.put(R.styleable.skin_android_scrollbarThumbVertical, ATTRIBUTE_SCROLLBAR_THUMB_VERTICAL);
        SUPPORT_ATTR.put(R.styleable.skin_android_scrollbarThumbHorizontal, ATTRIBUTE_SCROLLBAR_THUMB_HORIZONTAL);
        SUPPORT_ATTR.put(R.styleable.skin_android_scrollbarTrackVertical, ATTRIBUTE_SCROLLBAR_TRACK_VERTICAL);
        SUPPORT_ATTR.put(R.styleable.skin_android_scrollbarTrackHorizontal, ATTRIBUTE_SCROLLBAR_TRACK_HORIZONTAL);
        SUPPORT_ATTR.put(R.styleable.skin_android_button, ATTRIBUTE_BUTTON);
        SUPPORT_ATTR.put(R.styleable.skin_android_thumb, ATTRIBUTE_SWITCH_THUMB);
        SUPPORT_ATTR.put(R.styleable.skin_android_track, ATTRIBUTE_SWITCH_TRACK);
        SUPPORT_ATTR.put(R.styleable.skin_android_foreground, ATTRIBUTE_FOREGROUND);
        SUPPORT_ATTR.put(R.styleable.skin_android_textColorHint, ATTRIBUTE_TEXT_COLOR_HINT);
        SUPPORT_ATTR.put(R.styleable.skin_android_textColorHighlight, ATTRIBUTE_TEXT_COLOR_HIGH_LIGHT);
        SUPPORT_ATTR.put(R.styleable.skin_android_textColorLink, ATTRIBUTE_TEXT_COLOR_LINK);
        SUPPORT_ATTR.put(R.styleable.skin_android_divider, ATTRIBUTE_LIST_VIEW_DIVIDER);
        SUPPORT_ATTR.put(R.styleable.skin_android_indeterminateDrawable, ATTRIBUTE_INDETERMINATE_DRAWABLE);
        SUPPORT_ATTR.put(R.styleable.skin_tabIndicatorColor, ATTRIBUTE_TAB_INDICATOR_COLOR);
        SUPPORT_ATTR.put(R.styleable.skin_tabIndicator, ATTRIBUTE_TAB_INDICATOR);
        SUPPORT_ATTR.put(R.styleable.skin_tabTextColor, ATTRIBUTE_TAB_TEXT_COLOR);
        SUPPORT_ATTR.put(R.styleable.skin_tabSelectedTextColor, ATTRIBUTE_TAB_SELECTED_TEXT_COLOR);
        SUPPORT_ATTR.put(R.styleable.skin_android_textCursorDrawable, ATTRIBUTE_TEXT_CURSOR_DRAWABLE);
    }



    public static void updateViewAttribute(View view, String attribute, int resId) {
        SkinLogger.v("updateViewAttribute view = " + view + ", attribute = " + attribute + ", resourceId = " + resId);
        if (resId == SkinConfigs.ID_NULL) {
            return;
        }
        SkinState skinState = ViewTagUtil.getTagViewSkin(view);
        if (skinState == null) {
            skinState = SkinState.getNewInstance();
            ViewTagUtil.setTagViewSkin(view, skinState);
        }
        skinState.put(attribute, resId);
    }

    public static void updateViewAttribute(View view, String attribute, StringBean stringBean) {
        SkinLogger.v("updateViewAttribute view = " + view + ", attribute = " + attribute);
        if (stringBean == null || stringBean.getResId() == SkinConfigs.ID_NULL) {
            return;
        }
        SkinState skinState = ViewTagUtil.getTagViewSkin(view);
        if (skinState == null) {
            skinState = SkinState.getNewInstance();
            ViewTagUtil.setTagViewSkin(view, skinState);
        }
        SkinLogger.d("文言排查:: 更新 View Tag:: " + stringBean);
        view.setTag(R.id.tag_skin_text_args, stringBean);
        skinState.put(attribute, stringBean.getResId());
    }

    public static void removeViewAttribute(final View view, final String attribute) {
        SkinLogger.v("removeViewAttribute view = " + view + ", attribute = " + attribute);
        SkinState skinState = ViewTagUtil.getTagViewSkin(view);
        if (skinState != null) {
            skinState.remove(attribute);
            skinState.remove(attribute + "_objects");
            if (skinState.isEmpty()) {
                SkinLogger.w("removeViewAttribute view = " + view + ", clear SkinState");
                ViewTagUtil.setTagViewSkin(view, null);
            }
        }
    }
}
