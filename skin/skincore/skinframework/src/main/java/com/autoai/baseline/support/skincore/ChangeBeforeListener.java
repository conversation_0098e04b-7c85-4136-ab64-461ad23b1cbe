package com.autoai.baseline.support.skincore;

import com.autoai.baseline.support.skincore.daynight.DatNightMode;

import java.util.Locale;

public interface ChangeBeforeListener {
    /**
     * 换肤框架切换结束的回调操作
     *
     * @param skinNickName 当前皮肤包昵称
     * @param dayNightMode 当前昼夜模式
     * @param locale       语言环境
     */
    void finish(String skinNickName, DatNightMode dayNightMode, Locale locale);
}
