package com.autoai.baseline.support.skincore;

import static com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils.ATTRIBUTE_TEXT_COLOR;
import static com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils.ATTRIBUTE_TEXT_COLOR_HIGH_LIGHT;
import static com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils.ATTRIBUTE_TEXT_COLOR_HINT;
import static com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils.ATTRIBUTE_TEXT_COLOR_LINK;

import android.annotation.SuppressLint;
import android.app.Application;
import android.content.ComponentCallbacks;
import android.content.Context;
import android.content.res.Configuration;
import android.content.res.TypedArray;
import android.os.LocaleList;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.autoai.baseline.support.autoinflater.AutoInflaterManager;
import com.autoai.baseline.support.autoinflater.Plugin;
import com.autoai.baseline.support.skincore.aop.SkinAopMethod;
import com.autoai.baseline.support.skincore.applier.ViewApplier;
import com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils;
import com.autoai.baseline.support.skincore.attribute.SkinState;
import com.autoai.baseline.support.skincore.attribute.ViewTagUtil;
import com.autoai.baseline.support.skincore.daynight.DatNightMode;
import com.autoai.baseline.support.skincore.daynight.DayNightUtil;
import com.autoai.baseline.support.skincore.res.ResManager;

import java.text.SimpleDateFormat;
import java.util.Locale;
import java.util.Map;

/**
 * 换肤  LayoutFactory 拦截器
 *
 * <AUTHOR>
 */
public class SkinPlugin extends Plugin {
    //
    private static int scrollbarThumb = SkinConfigs.ID_NULL;
    private static boolean scrollbarThumbForce = false;
    //
    private static int textColor = SkinConfigs.ID_NULL;
    private static boolean textColorForce = false;
    //
    private static int textColorHint = SkinConfigs.ID_NULL;
    private static boolean textColorHintForce = false;
    //
    private static int textColorLink = SkinConfigs.ID_NULL;
    private static boolean textColorLinkForce = false;
    //
    private static int textColorHighlight = SkinConfigs.ID_NULL;
    private static boolean textColorHighlightForce = false;

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.CHINA);

    /**
     * 构造方法
     */
    public SkinPlugin() {
        super();
    }

    @Override
    @SuppressLint("InflateParams")
    public void init(Application application) {
        super.init(application);
        SkinAttributesUtils.init();
        ResManager.getInstance().resetResource(SkinConfigs.getSkinPath(), null);
        application.registerActivityLifecycleCallbacks(new SkinActivityLifecycleCallbacks());
        AutoInflaterManager.getInstance().register(application);
        //不可删除！！！！获取初始TypeArray 的默认 一些属性值。用于后面排除。初始加载一个layout获取，避免一个app启动第一个View定义了这些属性而拿错
        View view = LayoutInflater.from(application).inflate(R.layout.default_value_holder_layout, null);
        SkinLogger.forceI("多余属性排除2--> R.layout.default_value_layout --> " + view);
        //
        SkinLogger.forceI("多余属性排除2--> scrollbarThumb --> " + scrollbarThumb);
        if (scrollbarThumb == SkinConfigs.ID_NULL) {
            scrollbarThumbForce = true;
        }
        SkinLogger.forceI("多余属性排除2--> textColor --> " + textColor);
        if (textColor == SkinConfigs.ID_NULL) {
            textColorForce = true;
        }
        SkinLogger.forceI("多余属性排除2--> textColorHint --> " + textColorHint);
        if (textColorHint == SkinConfigs.ID_NULL) {
            textColorHintForce = true;
        }

        SkinLogger.forceI("多余属性排除2--> textColorLink --> " + textColorLink);
        if (textColorLink == SkinConfigs.ID_NULL) {
            textColorLinkForce = true;
        }
        SkinLogger.forceI("多余属性排除2--> textColorHighlight --> " + textColorHighlight);
        if (textColorHighlight == SkinConfigs.ID_NULL) {
            textColorHighlightForce = true;
        }
        //
        DayNightUtil.init();
        SkinLogger.forceI("【换肤框架】isAutoDayNight = " + SkinConfigs.isAuto());
        if (SkinConfigs.isAuto()) {
            // 监听昼夜切换
            application.registerComponentCallbacks(new ComponentCallbacks() {

                @Override
                public void onConfigurationChanged(@NonNull Configuration newConfig) {
                    changeAuto(newConfig, true);
                }

                @Override
                public void onLowMemory() {
                    SkinLogger.forceW("【换肤框架】onLowMemory !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
                }
            });
            changeAuto(application.getResources().getConfiguration(), false);
        }
    }

    public void changeAuto(@NonNull Configuration newConfig, boolean isOnConfigurationChanged) {
        long start = System.currentTimeMillis();
        if (isOnConfigurationChanged) {
            SkinLogger.forceI("【换肤框架】收到系统通知：" + DATE_FORMAT.format(start));
        }
        boolean localEqual = true;
        if (SkinConfigs.isSupportTextStr()) {
            LocaleList localeList = newConfig.getLocales();
            Locale locale;
            if (!localeList.isEmpty()) {
                locale = localeList.get(0);
            } else {
                locale = newConfig.locale;
            }
            SkinLogger.forceI("【切换语言】: 系统 = " + locale+", 当前 = " + ResManager.getInstance().getLocale());
            localEqual = SkinConfigs.localEqual(ResManager.getInstance().getLocale(), locale);
            if (!localEqual) {
                ResManager.getInstance().resetResource(SkinConfigs.getSkinPath(), locale);
            }
        }
        int currentNightMode = newConfig.uiMode & Configuration.UI_MODE_NIGHT_MASK;
        DatNightMode datNightMode = null;
        if (currentNightMode == Configuration.UI_MODE_NIGHT_NO) {
            datNightMode = DatNightMode.DAY;
        } else if (currentNightMode == Configuration.UI_MODE_NIGHT_YES) {
            datNightMode = DatNightMode.NIGHT;
        }
        boolean isDayNightEqual = datNightMode == SkinManager.getInstance().getDayNightMode();
        //
        SkinLogger.forceI("【切换语言】: localEqual = " + localEqual + ", isDayNightEqual = " + isDayNightEqual);
        if (!localEqual || !isDayNightEqual) {
            if (currentNightMode == Configuration.UI_MODE_NIGHT_NO) {
                //切换到白天模式
                SkinLogger.forceI("【切换】changeToDayMode");
                DayNightUtil.changeToDayMode();
                long end = System.currentTimeMillis();
                SkinLogger.forceI("【切换】结束：" + DATE_FORMAT.format(end));
                SkinLogger.forceI("【切换】总耗时：" + (end - start) + "ms");
            } else if (currentNightMode == Configuration.UI_MODE_NIGHT_YES) {
                //切换到夜间模式
                SkinLogger.forceI("【切换】changeToNightMode");
                DayNightUtil.changeToNightMode();
                long end = System.currentTimeMillis();
                SkinLogger.forceI("【切换】结束：" + DATE_FORMAT.format(end));
                SkinLogger.forceI("【切换】总耗时：" + (end - start) + "ms");
            }
            SkinConfigs.notifyChangeFinish();
        }
    }

    /**
     * 拦截
     */
    @Override
    public void task(@NonNull final Context context, @NonNull final AttributeSet attrs, @NonNull View view) {
        SkinState skinState = isSkinEnable(attrs);
        if (SkinLogger.isShowDebuggingLog()) {
            SkinLogger.v("task::skinState:: --> view = " + view + ", skinState = " + skinState);
        }
        if (skinState.isSkinEnable()) {
            final TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.skin);
            parse(view, typedArray, skinState);
            //此方案废弃
//            SkinAttributesUtils.isRecyclerView(view);
            typedArray.recycle();
        }
    }

    /**
     * 判断是否定义了"skinEnable"属性，并获取属性值，界定当前View要不要处理换肤
     */
    @NonNull
    protected SkinState isSkinEnable(@NonNull AttributeSet attrs) {
        String value = attrs.getAttributeValue(SkinAttributesUtils.NAME_SPACE, SkinAttributesUtils.SKIN_ENABLE);
        //判断skinEnable属性
        SkinState skinState = SkinState.getNewInstance();
        SkinLogger.v("isSkinEnable:: getAttributeValue --> " + value);
        skinState.setSkinEnable(!"false".equals(value));
        return skinState;
    }

    /**
     * 解析支持换肤的属性
     */
    protected void parse(View view, TypedArray typedArray, SkinState skinState) {
        //
        boolean needApply = DayNightUtil.isNight() || ResManager.getInstance().isUseSkin();
        //
        for (Map.Entry<Integer, String> entry : SkinAttributesUtils.SUPPORT_ATTR.entrySet()) {
            int key = entry.getKey();
            String attributeName = entry.getValue();
            //
            int resId = typedArray.getResourceId(key, SkinConfigs.ID_NULL);
            if (SkinLogger.isShowDebuggingLog()) {
                SkinLogger.d(view + "\n属性：key = " + key + ", attributeName = " + attributeName + ", resId = " + resId);
            }
            if (resId != SkinConfigs.ID_NULL && typedArray.hasValue(key)) {
                if (SkinLogger.isShowDebuggingLog()) {
                    SkinLogger.d(view + "\ntypedArray.hasValue(key)：key = " + key + ", attributeName = " + attributeName);
                }
                skinState = parseAttribute(view, attributeName, skinState, resId, needApply);
            }
        }
        if (SkinLogger.isShowDebuggingLog()) {
            SkinLogger.d(view + ", skinState = " + skinState);
        }
        if (skinState != null && !skinState.isEmpty()) {
            ViewTagUtil.setTagViewSkin(view, skinState);
        }
        if (view instanceof SkinViewSupport) {
            ((SkinViewSupport) view).applySkin();
        }
    }

    private SkinState parseAttribute(View view, String attributeName, SkinState skinState, int resId, boolean needApply) {
        if (attributeName.startsWith("scrollbar")) {
            if (scrollbarThumbForce) {
                skinState = parseSingleAttribute(view, skinState, attributeName, resId, needApply);
            } else {
                //系统默认所有 View 都有 scrollbarThumbVertical、scrollbarThumbHorizontal 等属性，并且给了默认值
                if (scrollbarThumb == SkinConfigs.ID_NULL && !SkinAopMethod.judgeView(view)) {
                    scrollbarThumb = resId;
                    SkinLogger.d(" --> 多余属性排除：scrollbarThumb = " + scrollbarThumb);
                }
                if (resId != scrollbarThumb) {
                    skinState = parseSingleAttribute(view, skinState, attributeName, resId, needApply);
                }
            }

        } else if (attributeName.startsWith(ATTRIBUTE_TEXT_COLOR)) {
            //textColor、textColorHint、textColorLink、textColorHighlight
            switch (attributeName) {
                case ATTRIBUTE_TEXT_COLOR: {
                    if (textColorForce) {
                        skinState = parseSingleAttribute(view, skinState, attributeName, resId, needApply);
                    } else {
                        if (textColor == SkinConfigs.ID_NULL && !(view instanceof TextView)) {
                            textColor = resId;
                            SkinLogger.d(" --> 多余属性排除：_textColor_ = " + textColor);
                        }
//                        SkinLogger.d(view + " --> ATTRIBUTE_TEXT_COLOR：_textColor_ = " + textColor + ", resId = " + ResManager.getInstance().getResBean(resId));
                        if (resId != textColor) {
                            skinState = parseSingleAttribute(view, skinState, attributeName, resId, needApply);
                        }
                    }
                    break;
                }
                case ATTRIBUTE_TEXT_COLOR_HINT: {
                    if (textColorHintForce) {
                        skinState = parseSingleAttribute(view, skinState, attributeName, resId, needApply);
                    } else {
                        if (textColorHint == SkinConfigs.ID_NULL && !(view instanceof TextView)) {
                            textColorHint = resId;
                            SkinLogger.d("多余属性排除：textColorHint = " + textColorHint);
                        }
                        if (resId != textColorHint) {
                            skinState = parseSingleAttribute(view, skinState, attributeName, resId, needApply);
                        }
                    }
                    break;
                }
                case ATTRIBUTE_TEXT_COLOR_LINK: {
                    if (textColorLinkForce) {
                        skinState = parseSingleAttribute(view, skinState, attributeName, resId, needApply);
                    } else {
                        if (textColorLink == SkinConfigs.ID_NULL && !(view instanceof TextView)) {
                            textColorLink = resId;
                            SkinLogger.d("多余属性排除：textColorLink = " + textColorLink);
                        }
                        if (resId != textColorLink) {
                            skinState = parseSingleAttribute(view, skinState, attributeName, resId, needApply);
                        }
                    }
                    break;
                }
                case ATTRIBUTE_TEXT_COLOR_HIGH_LIGHT: {
                    if (textColorHighlightForce) {
                        skinState = parseSingleAttribute(view, skinState, attributeName, resId, needApply);
                    } else {
                        if (textColorHighlight == SkinConfigs.ID_NULL && !(view instanceof TextView)) {
                            textColorHighlight = resId;
                            SkinLogger.d("多余属性排除：textColorHighlight = " + textColorHighlight);
                        }
                        if (resId != textColorHighlight) {
                            skinState = parseSingleAttribute(view, skinState, attributeName, resId, needApply);
                        }
                    }
                    break;
                }
                default: {
                    skinState = parseSingleAttribute(view, skinState, attributeName, resId, needApply);
                    break;
                }
            }
        } else {
            skinState = parseSingleAttribute(view, skinState, attributeName, resId, needApply);
        }
        return skinState;
    }

    /**
     * 处理单个View的单个属性
     */
    private SkinState parseSingleAttribute(View view, SkinState skinState, String attributeName, int resId, boolean needApply) {
        if (SkinAttributesUtils.ATTRIBUTE_TEXT.equals(attributeName) || SkinAttributesUtils.ATTRIBUTE_HINT.equals(attributeName)) {
            if (SkinLogger.isShowDebuggingLog()) {
                String resName = ResManager.getInstance().getResources().getResourceEntryName(resId);
                SkinLogger.d(view + "is_Text_or_Hint --> attributeName  = " + attributeName + ", resId = " + resId + ", resName = " + resName);
            }
        } else {
            if (ResManager.getInstance().noResId(resId)) {
                SkinLogger.d(view + " -->noResId --> attributeName  = " + attributeName + ", resId = " + resId + ", skinState = " + skinState);
                return skinState;
            }
        }

        //属性名称替换
        if (SkinAttributesUtils.ATTRIBUTE_DRAWABLE_LEFT.equals(attributeName)) {
            attributeName = SkinAttributesUtils.ATTRIBUTE_DRAWABLE_START;
        }
        if (SkinAttributesUtils.ATTRIBUTE_DRAWABLE_RIGHT.equals(attributeName)) {
            attributeName = SkinAttributesUtils.ATTRIBUTE_DRAWABLE_END;
        }
        //
        if (skinState == null) {
            skinState = SkinState.getNewInstance();
        }
        if (SkinLogger.isShowDebuggingLog()) {
            String resName = ResManager.getInstance().getResources().getResourceEntryName(resId);
            SkinLogger.d(view + "-->skinState.put  = " + attributeName + ", resId = " + resId + ", resName = " + resName);
        }

        skinState.put(attributeName, resId);
        if (needApply) {
            ViewApplier.changeViewAttribute(view, attributeName, resId);
        } else {
            if ((view instanceof TextView) && ResManager.getInstance().isEnglish()) {
                ViewTagUtil.changeTextSize(((TextView) view));
            }
        }
        return skinState;
    }
}
