package com.autoai.baseline.support.skincore.aop.widget;

import android.content.res.ColorStateList;
import android.widget.TextView;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.res.ResManager;
import com.autoai.baseline.support.skincore.applier.DynamicCodingApplier;
import com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinTextHintColorAspect {

    public static SkinTextHintColorAspect aspectOf() {
        return new SkinTextHintColorAspect();
    }

    @Pointcut("call(* android.widget.TextView+.setHintTextColor(..)) " + SkinConfigs.AOP_WITHOUT)
    public void hintColorPointcut() {
    }

    @Around("hintColorPointcut()")
    public Object aroundHintColor(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        if (target instanceof TextView) {
            TextView textView = (TextView) target;
            //
            Object[] params = joinPoint.getArgs();
            Object param = params[0];

            if (param instanceof Integer) {
                String key1 = ResManager.COLOR_KEY_HEAD + param;
                Integer resId = ResManager.RES_MAP.get(key1);
                if (resId != null && resId != SkinConfigs.ID_NULL) {
                    DynamicCodingApplier.setHintTextColor(textView, resId);
                    ResManager.RES_MAP.remove(key1);
                    return null;
                } else {
                    SkinAttributesUtils.removeViewAttribute(textView, SkinAttributesUtils.ATTRIBUTE_TEXT_COLOR_HINT);
                }
            } else if (param instanceof ColorStateList) {
                String key1 = param.hashCode() + "";
                Integer resId = ResManager.RES_MAP.get(key1);
                if (resId != null && resId != SkinConfigs.ID_NULL) {
                    DynamicCodingApplier.setHintTextColor(textView, resId);
                    ResManager.RES_MAP.remove(key1);
                    return null;
                } else {
                    SkinAttributesUtils.removeViewAttribute(textView, SkinAttributesUtils.ATTRIBUTE_TEXT_COLOR_HINT);
                }
            } else {
                SkinAttributesUtils.removeViewAttribute(textView, SkinAttributesUtils.ATTRIBUTE_TEXT_COLOR_HINT);
            }
        }
        return joinPoint.proceed();
    }
}
