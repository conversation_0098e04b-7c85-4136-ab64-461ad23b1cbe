package com.autoai.baseline.support.skincore.aop.resources;

import static com.autoai.baseline.support.skincore.SkinConfigs.ID_NULL;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.res.ResManager;
import com.autoai.baseline.support.skincore.SkinLogger;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinResourcesGetDrawableAspect {

    public static SkinResourcesGetDrawableAspect aspectOf() {
        return new SkinResourcesGetDrawableAspect();
    }

    @Pointcut("call(* android.content.res.Resources+.getDrawable(..)) " + SkinConfigs.AOP_WITHOUT)
    public void getDrawablePointcut() {
    }

    @Around("getDrawablePointcut()")
    public Object aroundGetDrawable(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] params = joinPoint.getArgs();
        int resId = ID_NULL;
        if (params.length > 0) {
            Object param = params[0];
            if (param instanceof Integer) {
                resId = (int) param;
            }
        }
        SkinLogger.d("AOP Resources+.getDrawable resId = " + resId);
        Object result = joinPoint.proceed();
        if (resId != ID_NULL && result != null) {
            ResManager.RES_MAP.put(result.hashCode() + "", resId);
        }
        return result;
    }
}
