package com.autoai.baseline.support.skincore.language;

import static com.autoai.baseline.support.skincore.SkinLogger.excludeList;

import android.content.res.Resources;

import com.autoai.baseline.support.skincore.CloudTextConfig;
import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.language.bean.StringBean;
import com.autoai.baseline.support.skincore.res.ResBean;
import com.autoai.baseline.support.skincore.res.ResManager;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class StringResManager {
    public static final Map<String, Integer> RES_MAP = new HashMap<>();
    public static final Map<String, StringBean> FORMAT_STRING_MAP = new HashMap<>();
    /**
     * 用于优化换肤资源查询速度，对内容进行缓存
     */
    private static final Map<String, ResBean> RES_BEAN_MAP = new HashMap<>();
    /**
     * 云端文言配置
     */
    private CloudTextConfig cloudTextConfig;

    private StringResManager() {
    }

    private static final class Holder {
        private static final StringResManager INSTANCE = new StringResManager();
    }

    public static StringResManager getInstance() {
        return StringResManager.Holder.INSTANCE;
    }

    public void setCloudTextListener(CloudTextConfig cloudTextConfig) {
        this.cloudTextConfig = cloudTextConfig;
    }

    private ResBean getResBean(int resId) {
        String key = resId + "";
        ResBean resBean = RES_BEAN_MAP.get(key);
        if (resBean == null) {
            Resources resources = ResManager.getInstance().getResources();
            String resName = resources.getResourceEntryName(resId);
            String resType = resources.getResourceTypeName(resId);
            String pkgName = resources.getResourcePackageName(resId);
            resBean = new ResBean(resId, resName, resType, pkgName);
            RES_BEAN_MAP.put(key, resBean);
        }
        if (!excludeList.contains(resBean.getResName())) {
            SkinLogger.v("文言排查::getResBean--> " + resBean);
        }
        return resBean;
    }
    public String[] getStringArray(int resId, boolean save) {
        if (resId == SkinConfigs.ID_NULL) {
            return new String[]{};
        }
        ResBean resBean = getResBean(resId);
        String resName = resBean.getResName();
        String[] result = ResManager.getInstance().getResources().getStringArray(resId);
        SkinLogger.v("文言排查::getStringArray --> resId = " + resId + "\n resName = " + resName + "\n result = " + Arrays.toString(result));
//        if (cloudTextConfig != null) {
//            result = cloudTextConfig.callback(resId, resName, result);
//            SkinLogger.v("文言排查::getString 云端文言 --> resId = " + resId + "\n resName = " + resName + "\n result = " + result);
//        }
        if (save) {
            String key = Arrays.hashCode(result) + "";
            RES_MAP.put(key, resId);
            SkinLogger.v("文言排查::RES_MAP.put key = " + key + ", resId = " + resId);
        }
        return result;
    }
    public String getString(int resId, boolean save) {
        if (resId == SkinConfigs.ID_NULL) {
            return "";
        }
        ResBean resBean = getResBean(resId);
        String resName = resBean.getResName();
        String result = ResManager.getInstance().getResources().getString(resId);
        SkinLogger.v("文言排查::getString --> resId = " + resId + "\n resName = " + resName + "\n result = " + result);
        if (cloudTextConfig != null) {
            result = cloudTextConfig.callback(resId, resName, result);
            SkinLogger.v("文言排查::getString 云端文言 --> resId = " + resId + "\n resName = " + resName + "\n result = " + result);
        }
        if (save && result != null) {
            String key = result.hashCode() + "";
            RES_MAP.put(key, resId);
            SkinLogger.v("文言排查::RES_MAP.put key = " + key + ", resId = " + resId);
        }
        return result;
    }

    public String getString(int resId, boolean save, Object... formatArgs) {
        if (resId == SkinConfigs.ID_NULL) {
            return "";
        }
        ResBean resBean = getResBean(resId);
        String resName = resBean.getResName();
        String result;
        if (formatArgs != null && formatArgs.length > 0) {
            result = ResManager.getInstance().getResources().getString(resId, formatArgs);
            SkinLogger.v("文言排查::getString --> resId = " + resId
                    + "\n resName = " + resName
                    + "\n objects = " + Arrays.toString(formatArgs)
                    + "\n result = " + result);
            if (cloudTextConfig != null) {
                result = cloudTextConfig.callback(resId, resName, result);
            }
            if (save && result != null) {
                //todo
                // 如果是 String getString(int resId, Object... formatArgs) 参数一直不同，那么可能会一直有存储，导致内存泄漏
                // 那么 result一直不同，比如参数是下载进度，则会造成内存泄漏，现在的方式最好是使用resId, 合并保存数据内容
                StringBean stringBean = new StringBean(resId, formatArgs);
                String key = result.hashCode() + "";
                FORMAT_STRING_MAP.put(key, stringBean);
                SkinLogger.v("文言排查::FORMAT_STRING_MAP.put key = " + key + ", stringBean = " + stringBean);
            }
        } else {
            result = ResManager.getInstance().getResources().getString(resId);
            SkinLogger.v("文言排查::getString --> resId = " + resId
                    + "\n resName = " + resName
                    + "\n result = " + result);
            if (cloudTextConfig != null) {
                result = cloudTextConfig.callback(resId, resName, result);
                SkinLogger.v("文言排查::getString 云端文言 --> resId = " + resId
                        + "\n resName = " + resName
                        + "\n result = " + result);
            }
            if (save && result != null) {
                String key = result.hashCode() + "";
                RES_MAP.put(key, resId);
                SkinLogger.v("文言排查::RES_MAP.put key = " + key + ", resId = " + resId);
            }
        }
        return result;
    }

}
