package com.autoai.baseline.support.skincore.language.aspect.getstring;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * Fragment 的 getString
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinFragment2GetStringAspect {
    public static SkinFragment2GetStringAspect aspectOf() {
        return new SkinFragment2GetStringAspect();
    }

    @Pointcut("call(* androidx.fragment.app.Fragment+.getString(..)) " + SkinConfigs.AOP_WITHOUT)
    public void fragment2GetString() {
    }

    @Around("fragment2GetString()")
    public Object aroundFragment2GetString(ProceedingJoinPoint joinPoint) throws Throwable {
        if (SkinConfigs.isSupportTextStr()) {
            SkinLogger.d("文言排查::AOP getString << SkinFragment2GetStringAspect");
            return SkinStringUtil.getStringJoinPoint(joinPoint);
        }else {
            return joinPoint.proceed();
        }
    }
}
