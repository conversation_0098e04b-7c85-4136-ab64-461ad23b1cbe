package com.autoai.baseline.support.skincore.aop.resources;

import static com.autoai.baseline.support.skincore.SkinConfigs.ID_NULL;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.res.ResManager;
import com.autoai.baseline.support.skincore.SkinLogger;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinResourcesCompatGetColorAspect {

    public static SkinResourcesCompatGetColorAspect aspectOf() {
        return new SkinResourcesCompatGetColorAspect();
    }

    @Pointcut("call(* androidx.core.content.res.ResourcesCompat+.getColor(..)) " + SkinConfigs.AOP_WITHOUT)
    public void getColorPointcut() {
    }

    @Around("getColorPointcut()")
    public Object aroundGetColor(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] params = joinPoint.getArgs();
        int resId = ID_NULL;
        if (params.length > 1) {
            Object param = params[1];
            if (param instanceof Integer) {
                resId = (int) param;
            }
        }
        SkinLogger.d("AOP ResourcesCompat+.getColor resId = " + resId);
        Object result = joinPoint.proceed();
        if (resId != ID_NULL && result != null) {
            ResManager.RES_MAP.put(ResManager.COLOR_KEY_HEAD + result, resId);
        }
        return result;
    }
}
