package com.autoai.baseline.support.skincore.applier.views;

import android.annotation.SuppressLint;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.LayerDrawable;
import android.graphics.drawable.StateListDrawable;
import android.os.Build;
import android.view.View;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * android sdk 版本适配工具类
 *
 * <AUTHOR>
 */
class SdkVerAdapterUtil {
    /**
     * 隐藏构造方法，不允许创建实例
     */
    private SdkVerAdapterUtil() {
    }

    static boolean needsTileify(Drawable dr) {
        if (dr instanceof LayerDrawable) {
            final LayerDrawable orig = (LayerDrawable) dr;
            final int n = orig.getNumberOfLayers();
            for (int i = 0; i < n; i++) {
                if (needsTileify(orig.getDrawable(i))) {
                    return true;
                }
            }
            return false;
        }

        if (dr instanceof StateListDrawable) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                final StateListDrawable in = (StateListDrawable) dr;
                final int stateCount = in.getStateCount();
                for (int i = 0; i < stateCount; i++) {
                    if (needsTileify(in.getStateDrawable(i))) {
                        return true;
                    }
                }
            }
            return false;
        }

        // If there's a bitmap that's not wrapped with a ClipDrawable or
        // ScaleDrawable, we'll need to wrap it and apply tiling.
        return dr instanceof BitmapDrawable;
    }

    @SuppressLint("PrivateApi")
    static void setThumbUnderQ(View view, final String methodName, final Drawable drawable)
            throws ClassNotFoundException, IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        Class<?> clazz = Class.forName("android.view.View");
        Field[] f = clazz.getDeclaredFields();
        for (Field field : f) {
            field.setAccessible(true);
            if ("mScrollCache".equals(field.getName())) {
                Object mScrollCache = field.get(view);
                if (mScrollCache != null) {
                    Class<?> scrollabilityCacheClazz = Class.forName("android.view.View$ScrollabilityCache");
                    Field[] scrollabilityCacheFields = scrollabilityCacheClazz.getDeclaredFields();
                    for (Field scrollabilityCacheField : scrollabilityCacheFields) {
                        scrollabilityCacheField.setAccessible(true);
                        if ("scrollBar".equals(scrollabilityCacheField.getName())) {
                            final Object scrollBar = scrollabilityCacheField.get(mScrollCache);
                            //                            SkinLogger.e("scrollBar = " + scrollBar);
                            if (scrollBar != null) {
                                Class<?> scrollBarDrawableClazz = Class.forName("android.widget.ScrollBarDrawable");
                                final Method setMethod = scrollBarDrawableClazz.getDeclaredMethod(methodName, Drawable.class);
                                setMethod.setAccessible(true);
                                setMethod.invoke(scrollBar, drawable);
                            }
                            break;
                        }
                    }
                }
                break;
            }
        }
    }

    @SuppressLint("PrivateApi")
    static void setTrackUnderQ(final View view, final String methodName, final Drawable drawable)
            throws ClassNotFoundException, IllegalAccessException,
            NoSuchMethodException, InvocationTargetException {
        Class<?> clazz = Class.forName("android.view.View");
        Field[] f = clazz.getDeclaredFields();
        for (Field field : f) {
            field.setAccessible(true);
            if ("mScrollCache".equals(field.getName())) {
                Object mScrollCache = field.get(view);
                if (mScrollCache != null) {
                    Class<?> scrollabilityCacheClazz = Class.forName("android.view.View$ScrollabilityCache");
                    Field[] scrollabilityCacheFields = scrollabilityCacheClazz.getDeclaredFields();
                    for (Field scrollabilityCacheField : scrollabilityCacheFields) {
                        scrollabilityCacheField.setAccessible(true);
                        if ("scrollBar".equals(scrollabilityCacheField.getName())) {
                            final Object scrollBar = scrollabilityCacheField.get(mScrollCache);
                            //                            SkinLogger.e("scrollBar = " + scrollBar);
                            if (scrollBar != null) {
                                Class<?> scrollBarDrawableClazz = Class.forName("android.widget.ScrollBarDrawable");
                                //绕过系统hide限制
                                // 公开API，无问题
                                Method metaGetDeclaredMethod = Class.class.getDeclaredMethod("getDeclaredMethods");
                                // 系统类通过反射使用隐藏 API，检查直接通过。
                                Method[] methods = (Method[]) metaGetDeclaredMethod.invoke(scrollBarDrawableClazz);
                                if (methods != null) {
                                    for (final Method method : methods) {
                                        if (method.getName().contains(methodName)) {
                                            method.setAccessible(true);
                                            // 正确找到 Method 直接反射调用
                                            method.invoke(scrollBar, drawable);
                                            break;
                                        }
                                    }
                                }
                            }
                            break;
                        }
                    }
                }
                break;
            }
        }
    }

}
