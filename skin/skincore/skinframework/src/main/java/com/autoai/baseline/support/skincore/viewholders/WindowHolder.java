package com.autoai.baseline.support.skincore.viewholders;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.view.View;
import android.view.Window;
import android.widget.PopupWindow;

import com.autoai.baseline.support.autoinflater.AutoInflaterManager;
import com.autoai.baseline.support.autoinflater.AutoInflaterThreadPoolUtil;
import com.autoai.baseline.support.skincore.SKinApplyListener;
import com.autoai.baseline.support.skincore.SkinApplySortBean;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.applier.ViewApplier;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * 换肤内容持有类
 *
 * <AUTHOR>
 */
public class WindowHolder {
    /**
     * 换肤应用的时候Activity、Dialog等顺序定制
     */
    private static SKinApplyListener SKIN_APPLY_LISTENER;
    private static final ArrayList<Dialog> DIALOG_LIST = new ArrayList<>();
    private static final ArrayList<PopupWindow> POPUP_WINDOW_LIST = new ArrayList<>();
    private static final ArrayList<Activity> ACTIVITY_LIST = new ArrayList<>();
    private static final ArrayList<View> VIEW_LIST = new ArrayList<>();
    private static final Runnable FRAGMENT_DIALOG_RUNNABLE = new Runnable() {
        @Override
        public void run() {
            int size;
            if (DIALOG_LIST.isEmpty()) {
                size = 0;
            } else {
                checkFragmentDialogBymShowing();
                size = DIALOG_LIST.size();
            }
            SkinLogger.d("refreshFragmentDialogList last size " + size);
            if (size != 0) {
                AutoInflaterThreadPoolUtil.getInstance().runOnUiThreadDelayed(FRAGMENT_DIALOG_RUNNABLE, 2000);
            }
        }
    };

    private static final Runnable POPUP_WINDOW_REFRESH_RUNNABLE = new Runnable() {
        @Override
        public void run() {
            int size;
            if (POPUP_WINDOW_LIST.isEmpty()) {
                size = 0;
            } else {
                List<PopupWindow> list = new ArrayList<>(POPUP_WINDOW_LIST);
                for (PopupWindow popupWindow : list) {
                    if (!popupWindow.isShowing()) {
                        POPUP_WINDOW_LIST.remove(popupWindow);
                    }
                }
                size = POPUP_WINDOW_LIST.size();
            }
            SkinLogger.d("refreshPopWindowList last size " + size);
            if (size != 0) {
                AutoInflaterThreadPoolUtil.getInstance().runOnUiThreadDelayed(POPUP_WINDOW_REFRESH_RUNNABLE, 2000);
            }
        }
    };

    public static void setSKinApplyListener(SKinApplyListener listener) {
        SKIN_APPLY_LISTENER = listener;
    }

    @SuppressLint("DiscouragedPrivateApi")
    @SuppressWarnings("all")
    private static void checkFragmentDialogBymShowing() {
        List<Dialog> list = new ArrayList<>(DIALOG_LIST);
        for (Dialog dialog : list) {
            if (dialog == null) {
                continue;
            }
            try {
                Class<? extends Dialog> clazz = android.app.Dialog.class;
//                Field[] fields = clazz.getDeclaredFields();
//                for (Field item : fields) {
//                    SkinLogger.d("refreshFragmentDialogList Field = " + item.toString());
//                }
                //
                Field field = clazz.getDeclaredField("mShowing");
                field.setAccessible(true);
                Object o = field.get(dialog);
                SkinLogger.d("refreshFragmentDialogList dialog = " + dialog + ", mShowing1 = " + o);
                if (o instanceof Boolean) {
                    boolean mShowing = (Boolean) o;
                    if (!mShowing) {
                        removeDialog(dialog);
                    }
                }
            } catch (IllegalAccessException | NoSuchFieldException e) {
                SkinLogger.e("checkFragmentDialogBymShowing::Error accessing dialog field", e);
            }
        }
    }

    public static void addActivity(Activity activity) {
        if (!ACTIVITY_LIST.contains(activity)) {
            SkinLogger.d("addActivity = " + activity);
            ACTIVITY_LIST.add(0, activity);
            AutoInflaterManager.getInstance().register(activity);
        }
    }

    public static void removeActivity(Activity activity) {
        SkinLogger.d("removeActivity = " + activity);
        ACTIVITY_LIST.remove(activity);
        AutoInflaterManager.getInstance().removeRegisted(activity);
    }

    public static void addDialog(Dialog dialog) {
        if (!DIALOG_LIST.contains(dialog)) {
            SkinLogger.d("addFragmentDialog = " + dialog);
            DIALOG_LIST.add(0, dialog);
            //存在保存Dialog对象，换肤后，重新调用 show。所以此处还是需要 apply 一次的。
            Window window = dialog.getWindow();
            if (window != null) {
                View decorView = window.getDecorView();
                ViewApplier.applyRootView(decorView);
            } else {
                SkinLogger.forceE(dialog + " --> Window is null");
            }
            AutoInflaterThreadPoolUtil.getInstance().runOnUiThreadDelayed(FRAGMENT_DIALOG_RUNNABLE, 2000);
        }else{
            Window window = dialog.getWindow();
            if (window != null) {
                View decorView = window.getDecorView();
                ViewApplier.applyRootView(decorView);
            } else {
                SkinLogger.forceE(dialog + " --> Window is null");
            }
        }
    }

    public static void removeDialog(Dialog dialog) {
        SkinLogger.d("removeFragmentDialog = " + dialog);
        DIALOG_LIST.remove(dialog);
    }

    public static void addPopupWindow(PopupWindow popupWindow) {
        if (!POPUP_WINDOW_LIST.contains(popupWindow)) {
            AutoInflaterThreadPoolUtil.getInstance().runOnUiThreadDelayed(POPUP_WINDOW_REFRESH_RUNNABLE, 2000);
            SkinLogger.d("addPopupWindow = " + popupWindow);
            POPUP_WINDOW_LIST.add(0, popupWindow);
        }
        View decorView = popupWindow.getContentView();
        if (decorView != null) {
            ViewApplier.applyRootView(decorView);
        } else {
            SkinLogger.forceE(popupWindow+" --> decorView is null");
        }
    }

    public static void removePopupWindow(PopupWindow popupWindow) {
        SkinLogger.d("removePopupWindow = " + popupWindow);
        POPUP_WINDOW_LIST.remove(popupWindow);
    }

    public static void addView(View decorView) {
        if(decorView == null){
            SkinLogger.forceE("addView --> null !!!!");
            return;
        }
        if (!VIEW_LIST.contains(decorView)) {
            SkinLogger.d("viewList addView = " + decorView);
            VIEW_LIST.add(0, decorView);
        }
        ViewApplier.applyRootView(decorView);
    }

    public static void removeView(View decorView) {
        SkinLogger.d("viewList removeView = " + decorView);
        VIEW_LIST.remove(decorView);
    }

    public static void applyUi() {
        SkinLogger.forceI("----------------------- applyUi -----------------------");
        List<Dialog> dialogList = null;
        List<PopupWindow> popupWindowList = null;
        List<Activity> activityList = null;
        List<View> viewList = null;
        if (SKIN_APPLY_LISTENER != null) {
            //换肤应用的时候Activity、Dialog等顺序定制
            SkinApplySortBean bean = SKIN_APPLY_LISTENER.getOrderList(DIALOG_LIST,
                    POPUP_WINDOW_LIST, ACTIVITY_LIST, VIEW_LIST
            );
            if (bean != null) {
                dialogList = bean.getDialogList();
                popupWindowList = bean.getPopupWindowList();
                activityList = bean.getActivityList();
                viewList = bean.getViewList();
            }
        }
        if (dialogList == null || dialogList.isEmpty()) {
            dialogList = DIALOG_LIST;
        }
        if (popupWindowList == null || popupWindowList.isEmpty()) {
            popupWindowList = POPUP_WINDOW_LIST;
        }
        if (activityList == null || activityList.isEmpty()) {
            activityList = ACTIVITY_LIST;
        }
        if (viewList == null || viewList.isEmpty()) {
            viewList = VIEW_LIST;
        }
        if (!dialogList.isEmpty()) {
            applyDialogList(dialogList);
        }
        if (!popupWindowList.isEmpty()) {
            applyPopupWindowList(popupWindowList);
        }
        if (!activityList.isEmpty()) {
            applyActivityList(activityList);
        }
        if (!viewList.isEmpty()) {
            applyViewList(viewList);
        }
    }

    private static void applyDialogList(List<Dialog> dialogList) {
        int size = dialogList.size();
        SkinLogger.d("applySkin dialogList Size : " + size);
        if (size > 0) {
            List<Dialog> list = new ArrayList<>(dialogList);
            for (Dialog dialog : list) {
                SkinLogger.forceI("applySkin dialog : " + dialog);
                Window window = dialog.getWindow();
                if (window != null) {
                    View decorView = window.getDecorView();
                    ViewApplier.applyRootView(decorView);
                }
            }
        }
    }

    private static void applyPopupWindowList(List<PopupWindow> popupWindowList) {
        int size = popupWindowList.size();
        SkinLogger.d("applySkin PopupWindow Size : " + size);
        if (size > 0) {
            ArrayList<PopupWindow> list = new ArrayList<>(popupWindowList);
            for (PopupWindow popupWindow : list) {
                SkinLogger.forceI("applySkin popupWindow : " + popupWindow);
                View decorView = popupWindow.getContentView();
                if (decorView != null) {
                    ViewApplier.applyRootView(decorView);
                }
            }
        }
    }

    private static void applyActivityList(List<Activity> activityList) {
        int size = activityList.size();
        SkinLogger.d("applySkin activityList Size : " + size);
        if (size > 0) {
            ArrayList<Activity> list = new ArrayList<>(activityList);
            for (Activity activity : list) {
                SkinLogger.forceI("applySkin activityList : " + activity);
                Window window = activity.getWindow();
                if (window != null) {
                    View decorView = window.getDecorView();
                    ViewApplier.applyRootView(decorView);
                }
            }
        }
    }

    private static void applyViewList(List<View> viewList) {
        int size = viewList.size();
        SkinLogger.d("applySkin viewList Size : " + size);
        if (size > 0) {
            ArrayList<View> list = new ArrayList<>(viewList);
            for (View decorView : list) {
                SkinLogger.forceI("applySkin viewList : " + decorView);
                if (decorView != null) {
                    ViewApplier.applyRootView(decorView);
                }
            }
        }
    }
}
