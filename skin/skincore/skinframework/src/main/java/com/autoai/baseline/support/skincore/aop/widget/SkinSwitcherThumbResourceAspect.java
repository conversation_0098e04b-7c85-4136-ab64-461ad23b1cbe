package com.autoai.baseline.support.skincore.aop.widget;

import android.widget.Switch;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.applier.DynamicCodingApplier;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinSwitcherThumbResourceAspect {

    public static SkinSwitcherThumbResourceAspect aspectOf() {
        return new SkinSwitcherThumbResourceAspect();
    }

    /**
     * 任何一个包下面的任何一个类下面的任何一个带有SkinActivityInit的方法，构成了这个切面
     */
    @Pointcut("call(* android.widget.Switch+.setThumbResource(..)) " + SkinConfigs.AOP_WITHOUT)
    public void setThumbResourcePointcut() {
    }

    /**
     * 拦截方法
     */
    @Around("setThumbResourcePointcut()")
    public Object aroundThumbResource(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        if (target instanceof Switch) {
            Object[] params = joinPoint.getArgs();
            Switch aSwitch = (Switch) target;
            if (params.length > 0) {
                Object params1 = params[0];
                if (params1 instanceof Integer) {
                    int resId = (Integer) params1;
                    SkinLogger.d("AOP Switch+.setThumbResource resId = " + resId);
                    DynamicCodingApplier.setSwitchThumb(aSwitch, resId);
                    return null;
                }
            }
        }
        //执行拦截方法
        return joinPoint.proceed();
    }
}
