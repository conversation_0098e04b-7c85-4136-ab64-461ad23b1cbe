package com.autoai.baseline.support.skincore.aop.popwindow;

import android.widget.PopupWindow;

import com.autoai.baseline.support.skincore.viewholders.WindowHolder;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinPopWindowShowAsDropDownAspect {

    public static SkinPopWindowShowAsDropDownAspect aspectOf() {
        return new SkinPopWindowShowAsDropDownAspect();
    }

    @Pointcut("call(* android.widget.PopupWindow+.showAsDropDown(..))"
            + "&& !within(com.autoai.baseline.support.skincore.aop.popwindow.SkinPopWindowDismissLisAspect)"
            + "&& !within(com.autoai.baseline.support.skincore.aop.popwindow.SkinPopWindowShowAsDropDownAspect)"
            + "&& !within(com.autoai.baseline.support.skincore.aop.popwindow.SkinPopWindowShowAtLocationAspect)"
            + "&& !within(com.autoai.baseline.support.skincore.aop.popwindow.PopWindowUtils)"
    )
    public void showAsDropDownPointcut() {
    }

    @Around("showAsDropDownPointcut()")
    public Object aroundShowAsDropDown(ProceedingJoinPoint joinPoint) throws Throwable {
        //
        Object target = joinPoint.getTarget();
        if (target instanceof PopupWindow) {
            final PopupWindow popupWindow = (PopupWindow) target;
            WindowHolder.addPopupWindow(popupWindow);
            PopWindowUtils.addDismissListener(popupWindow);
        }
        //
        return joinPoint.proceed();
    }
}
