package com.autoai.baseline.support.skincore.res;

import android.content.res.Resources;
import android.graphics.drawable.Drawable;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.res.ResourcesCompat;

public class DrawableUtil {
//    private static DisplayMetrics defaultDisplayMetrics = null;

    @Nullable
    public static Drawable getDrawable(@NonNull Resources res, @DrawableRes int id,
                                       @Nullable Resources.Theme theme) throws Resources.NotFoundException {
//        DisplayMetrics displayMetrics = res.getDisplayMetrics();
//        if (defaultDisplayMetrics == null) {
//            defaultDisplayMetrics = new DisplayMetrics();
//            defaultDisplayMetrics.setTo(displayMetrics);
//            Configuration configuration = res.getConfiguration();
//            // 默认内容 density
//            int widthPixels = displayMetrics.widthPixels;
//            int heightPixels = displayMetrics.heightPixels;
//            float density = displayMetrics.density;
//            int densityDpi = displayMetrics.densityDpi;
//            float confFontScale = configuration.fontScale;
//            int confDensityDpi = configuration.densityDpi;
//            // 打印屏幕的宽度、高度、密度和密度值
//            String msg = "density检测::默认：Width in pixels: " + widthPixels
//                    + "\nHeight in pixels: " + heightPixels
//                    + "\nDensity: " + density
//                    + "\nDensity DPI: " + densityDpi
//                    + "\nconfFontScale: " + confFontScale
//                    + "\nconfDensityDpi: " + confDensityDpi;
//            SkinLogger.d(msg);
//        }
//        if (displayMetrics.density != defaultDisplayMetrics.density) {
//            Configuration configuration = res.getConfiguration();
//            // density 发生变化
//            int widthPixels = displayMetrics.widthPixels;
//            int heightPixels = displayMetrics.heightPixels;
//            float density = displayMetrics.density;
//            int densityDpi = displayMetrics.densityDpi;
//            float confFontScale = configuration.fontScale;
//            int confDensityDpi = configuration.densityDpi;
//            // 打印屏幕的宽度、高度、密度和密度值
//            String msg = "density检测::发生变化： Width in pixels: " + widthPixels
//                    + "\nHeight in pixels: " + heightPixels
//                    + "\nDensity: " + density
//                    + "\nDensity DPI: " + densityDpi
//                    + "\nconfFontScale: " + confFontScale
//                    + "\nconfDensityDpi: " + confDensityDpi;
//            SkinLogger.d(msg);
//            res.updateConfiguration(configuration, defaultDisplayMetrics);
//        }
        //SkinLogger.d("getDrawable --> resId = " + id);
        return ResourcesCompat.getDrawable(res, id, theme);
    }
}
