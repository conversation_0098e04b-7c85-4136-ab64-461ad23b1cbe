package com.autoai.baseline.support.skincore;

import android.text.TextUtils;

import com.autoai.baseline.support.autoinflater.AutoInflaterThreadPoolUtil;
import com.autoai.baseline.support.skincore.daynight.DayNightUtil;
import com.autoai.baseline.support.skincore.res.ResManager;

import java.util.Locale;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * 换肤配置
 *
 * <AUTHOR>
 */
public class SkinConfigs {
    /**
     * 默认资源id，表示没有此资源
     */
    public static final int ID_NULL = 0;
    /**
     * 默认皮肤包名称
     */
    public static final String SKIN_DEFAULT_NAME = "Default";
    public static String skinNickName = SKIN_DEFAULT_NAME;
    /**
     * 默认皮肤包路径
     */
    public static final String SKIN_DEFAULT_PATH = "";
    public static String skinPath = SKIN_DEFAULT_PATH;
    /**
     * AOP需要排查的类
     */
    public static final String AOP_WITHOUT = "&& !within(com.autoai.baseline.support.skincore..*)"
            + " && !within(com.google..*) "
            + " && !within(com.squareup..*) ";

    /**
     * 系统不同功能是否开启
     */
    public static final boolean IS_USE_SYSTEM_SYNC = com.autoai.baseline.support.skincore.BuildConfig.isSystemSync;
    /**
     * 换肤是否保持系统同步
     */
    private static boolean isSystemSync = false;
//    /**
//     * 是否忽略前后台；true 忽略，直接换肤；false，只有应用切换到前台的时候才换肤
//     */
//    private static boolean ignoreForeground = false;
    /**
     * 是否支持 text、hint 等字符串显示内容的 更换，如果是多语言应用可以开始，用于多语言变更控制
     */
    private static boolean supportTextStr = false;
    /**
     * 是否配置了，英文文言 文字大小差值
     */
    public static boolean configEnTextSizeDif = false;
    /**
     * 英文文言 文字大小差值，单位px；
     * <p>
     * 英文语言，自动减指定像素值
     * 项目需求: 我们启用了中英文切换功能，但是需求现在需要切换成英文的时候，整体字号小2像素，想问一下咱们这个框架可以支持么？
     */
    public static float enTextSizeDif = 0;
    /**
     * 文大小表示，单位px；以此值为参考，大于或者小于此值做字号缩小
     */
    public static float enTextSizeDifMarkSize = 0;
    /**
     * true，大于等于 marSize的才做字号缩小；false，小于等于 markSize 的做字号缩小
     */
    public static boolean enTextSizeDifIsUp = true;
    /**
     * 客户注册的换肤回调监听
     */
    private static final Set<SkinChangeUiListener> UI_CHANGE_UI_SET = new CopyOnWriteArraySet<>();
    private static final Set<SkinChangeFinishListener> UI_CHANGE_FINISH_SET = new CopyOnWriteArraySet<>();
    private static final Set<ChangeBeforeListener> UI_CHANGE_BEFORE_SET = new CopyOnWriteArraySet<>();
    private static final Set<SkinChangeListener> UI_CHANGE_SET = new CopyOnWriteArraySet<>();

//    /**
//     * 避免每次前后台切换都执行一次换肤操作
//     */
//    public static boolean mChangeSkin;


    /**
     * 是否使用换肤框架自身主动跟随系统切换的功能
     * <p>
     * 默认使用
     */
    private static boolean isAuto = true;

    public static boolean isAuto() {
        return isAuto;
    }

    public static void setAuto(boolean isAuto) {
        SkinConfigs.isAuto = isAuto;
    }

    /**
     * 换肤是否保持系统同步
     */
    public static boolean isSystemSync() {
        return isSystemSync;
    }

    /**
     * 是否支持昼夜模式应用（系统）同步，这个用打包参数再控制一下。
     */
    public static void setIsSystemSync(boolean isSystemSync) {
        if (IS_USE_SYSTEM_SYNC) {
            SkinConfigs.isSystemSync = isSystemSync;
        }
    }

//    /**
//     * 是否忽略前后台: true 忽略，直接换肤；false，只有应用切换到前台的时候才换肤
//     */
//    public static boolean isIgnoreForeground() {
//        return ignoreForeground;
//    }

//    public static void setIgnoreForeground(boolean ignoreForeground) {
//        SkinConfigs.ignoreForeground = ignoreForeground;
//    }

    public static boolean isSupportTextStr() {
        return supportTextStr;
    }

    public static void setSupportTextStr(boolean supportTextStr) {
        SkinConfigs.supportTextStr = supportTextStr;
    }

    /**
     * 连个 Locale 是否相等
     */
    public static boolean localEqual(Locale locale1, Locale locale2) {
        SkinLogger.i("locale1 = " + locale1 + ", locale2 = " + locale2);
        if (locale1 == null) {
            return locale2 == null;
        } else {
            if (locale2 == null) {
                return false;
            } else {
                return TextUtils.equals(locale1.getLanguage(), locale2.getLanguage())
                        && TextUtils.equals(locale1.getCountry(), locale2.getCountry());
            }
        }
    }

    /**
     * 英文文言 文字大小差值
     * <p>
     * 英文语言，自动减指定像素值
     * 项目需求: 我们启用了中英文切换功能，但是需求现在需要切换成英文的时候，整体字号小2像素，想问一下咱们这个框架可以支持么？
     *
     * @param dif      英文自定缩小多少个字号，单位px（如果为负数，表示放大字号）；
     * @param markSize 英文大小表示，单位px，以此值为参考，大于或者小于此值做字号缩小（放大）
     * @param isUp     true，大于等于 marSize的才做字号缩小（放大）；false，小于等于 markSize 的做字号缩小（放大）
     */
    public static void setEnTextSizeDif(float dif, float markSize, boolean isUp) {
        configEnTextSizeDif = true;
        SkinConfigs.enTextSizeDif = dif;
        SkinConfigs.enTextSizeDifMarkSize = markSize;
        SkinConfigs.enTextSizeDifIsUp = isUp;
    }

    /**
     * 添加 移除 换肤、昼夜切换前回调监听
     */
    public static void addChangeBeforeListener(ChangeBeforeListener listener) {
        UI_CHANGE_BEFORE_SET.add(listener);
    }

    /**
     * 移除 换肤、昼夜切换前回调监听
     */
    public static void removeChangeBeforeListener(ChangeBeforeListener listener) {
        UI_CHANGE_BEFORE_SET.remove(listener);
    }

    /**
     * 换肤、昼夜切换监听，会在 listener 中回调
     * <p>
     * {@link SkinConfigs#addSkinChangeUiListener} 替代
     */
    public static void addSkinChangeListener(SkinChangeListener listener) {
        UI_CHANGE_SET.add(listener);
    }

    /**
     * 移除 换肤、昼夜切换监听，避免内存泄漏
     * <p>
     * {@link SkinConfigs#removeSkinChangeUiListener} 替代
     */
    public static void removeSkinChangeListener(SkinChangeListener listener) {
        UI_CHANGE_SET.remove(listener);
    }

    /**
     * 换肤、昼夜、语言 切换监听，会在 listener 中回调
     */
    public static void addSkinChangeUiListener(SkinChangeUiListener listener) {
        UI_CHANGE_UI_SET.add(listener);
    }

    /**
     * 移除 换肤、昼夜、语言 监听，避免内存泄漏
     */
    public static void removeSkinChangeUiListener(SkinChangeUiListener listener) {
        UI_CHANGE_UI_SET.remove(listener);
    }

    /**
     * 换肤框架切换结束的回调操作
     */
    public static void addChangeFinishListener(SkinChangeFinishListener listener) {
        UI_CHANGE_FINISH_SET.add(listener);
    }

    /**
     * 换肤框架切换结束的回调操作
     */
    public static void removeChangeFinishListener(SkinChangeFinishListener listener) {
        UI_CHANGE_FINISH_SET.remove(listener);
    }

    /**
     * 切换前回调
     */
    public static void notifyChangeBefore() {
        int size = UI_CHANGE_BEFORE_SET.size();
        if (size > 0) {
            for (ChangeBeforeListener item : UI_CHANGE_BEFORE_SET) {
                if (item == null) {
                    continue;
                }
                item.finish(getSkinNickName(), DayNightUtil.getMode(), ResManager.getInstance().getLocale());
            }
        }
    }

    /**
     * 换肤回调，通知用户
     */
    public static void notifyUiChange() {
        int listenerSize = UI_CHANGE_UI_SET.size();
        if (listenerSize > 0) {
            for (SkinChangeUiListener item : UI_CHANGE_UI_SET) {
                item.change(getSkinNickName(), DayNightUtil.getMode(), ResManager.getInstance().getLocale());
            }
        }
        //避免别人设置的此监听中有很多耗时操作，导致换肤卡顿
        //此处使用post处理, 避免阻塞其他程序
        AutoInflaterThreadPoolUtil.getInstance().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                //
                int size = UI_CHANGE_SET.size();
                if (size > 0) {
                    for (SkinChangeListener item : UI_CHANGE_SET) {
                        if (item == null) {
                            continue;
                        }
                        item.change(getSkinNickName(), DayNightUtil.getMode());
                    }
                }
            }
        });
    }

    /**
     * 通知切换结束
     */
    public static void notifyChangeFinish() {
        int size = UI_CHANGE_FINISH_SET.size();
        if (size > 0) {
            for (SkinChangeFinishListener item : UI_CHANGE_FINISH_SET) {
                if (item == null) {
                    continue;
                }
                item.finish(getSkinNickName(), DayNightUtil.getMode(), ResManager.getInstance().getLocale());
            }
        }
    }

    public static String getSkinPath() {
        return skinPath;
    }

    public static String getSkinNickName() {
        return skinNickName;
    }
}
