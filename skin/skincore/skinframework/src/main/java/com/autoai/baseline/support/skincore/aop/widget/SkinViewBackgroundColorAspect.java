package com.autoai.baseline.support.skincore.aop.widget;

import android.view.View;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.res.ResManager;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.applier.DynamicCodingApplier;
import com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinViewBackgroundColorAspect {

    public static SkinViewBackgroundColorAspect aspectOf() {
        return new SkinViewBackgroundColorAspect();
    }

    @Pointcut("call(* android.view.View+.setBackgroundColor(..)) " + SkinConfigs.AOP_WITHOUT)
    public void backgroundColorPointcut() {
    }

    @Around("backgroundColorPointcut()")
    public Object aroundBackgroundColor(ProceedingJoinPoint joinPoint) throws Throwable {

        Object target = joinPoint.getTarget();
        if (target instanceof View) {
            View view = (View) target;
            SkinLogger.d("AOP View+.setBackgroundColor " + view);
            //
            Object[] params = joinPoint.getArgs();
            Object param = params[0];
            if (param instanceof Integer) {
                String key1 = ResManager.COLOR_KEY_HEAD + param;
                Integer resId = ResManager.RES_MAP.get(key1);
                SkinLogger.d("AOP View+.setBackgroundColor resId = " + resId);
                if (resId == null) {
                    DynamicCodingApplier.setBackground(view, SkinConfigs.ID_NULL);
                } else {
                    DynamicCodingApplier.setBackground(view, resId);
                }
                ResManager.RES_MAP.remove(key1);
            } else if (param == null) {
                SkinLogger.d("AOP View+.setBackgroundColor param == null");
                DynamicCodingApplier.setBackground(view, SkinConfigs.ID_NULL);
            } else {
                SkinAttributesUtils.removeViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_BACKGROUND);
            }
        }
        //执行拦截方法
        return joinPoint.proceed();
    }
}
