package com.autoai.baseline.support.skincore.language.aspect.getstring;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * Fragment 的 getString
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinFragmentGetStringAspect {
    public static SkinFragmentGetStringAspect aspectOf() {
        return new SkinFragmentGetStringAspect();
    }

    @Pointcut("call(* android.app.Fragment+.getString(..)) " + SkinConfigs.AOP_WITHOUT)
    public void fragmentGetString() {
    }

    @Around("fragmentGetString()")
    public Object aroundFragmentGetString(ProceedingJoinPoint joinPoint) throws Throwable {
        if (SkinConfigs.isSupportTextStr()) {
            SkinLogger.d("文言排查::AOP getString << SkinFragmentGetStringAspect");
            return SkinStringUtil.getStringJoinPoint(joinPoint);
        }else {
            return joinPoint.proceed();
        }
    }
}
