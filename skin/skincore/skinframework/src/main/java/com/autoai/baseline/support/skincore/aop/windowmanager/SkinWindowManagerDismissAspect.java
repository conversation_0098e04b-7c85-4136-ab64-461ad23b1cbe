package com.autoai.baseline.support.skincore.aop.windowmanager;

import android.view.View;
import android.view.ViewManager;

import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.viewholders.WindowHolder;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinWindowManagerDismissAspect {

    public static SkinWindowManagerDismissAspect aspectOf() {
        return new SkinWindowManagerDismissAspect();
    }

    @Pointcut("call(* android.view.WindowManager+.removeView(..))"
            + "&& !within(com.autoai.baseline.support.skincore.aop.popwindow.SkinPopWindowDismissLisAspect)"
            + "&& !within(com.autoai.baseline.support.skincore.aop.popwindow.SkinPopWindowShowAsDropDownAspect)"
            + "&& !within(com.autoai.baseline.support.skincore.aop.popwindow.SkinPopWindowShowAtLocationAspect)"
            + "&& !within(com.autoai.baseline.support.skincore.aop.popwindow.PopWindowUtils)"
    )
    public void showAsDropDownPointcut() {
    }

    @Around("showAsDropDownPointcut()")
    public Object aroundShowAsDropDown(ProceedingJoinPoint joinPoint) throws Throwable {
        //
        Object target = joinPoint.getTarget();
        if (target instanceof ViewManager) {
            final ViewManager viewManager = (ViewManager) target;
            SkinLogger.d("WindowManager remove(" + viewManager + "): ");
            Object[] args = joinPoint.getArgs();
            if (args.length > 0) {
                Object param = args[0];
                if (param instanceof View) {
                    SkinLogger.d("WindowManager remove(" + viewManager + "): " + param);
                    WindowHolder.removeView((View) param);
                }
            }
        }
        //
        return joinPoint.proceed();
    }
}
