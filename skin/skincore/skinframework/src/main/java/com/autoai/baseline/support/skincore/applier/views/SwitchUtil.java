package com.autoai.baseline.support.skincore.applier.views;

import android.annotation.SuppressLint;
import android.graphics.drawable.Drawable;
import android.widget.Switch;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.res.ResManager;

/**
 * Switch 资源应用
 *
 * <AUTHOR>
 */
public class SwitchUtil {
    /**
     * 隐藏构造方法，不允许创建实例
     */
    private SwitchUtil() {
    }

    public static void setSwitchThumb(@SuppressLint("UseSwitchCompatOrMaterialCode") final Switch view, int resId) {
        if (resId == SkinConfigs.ID_NULL) {
            return;
        }
        final Drawable drawable = ResManager.getInstance().getDrawable(resId);
        if (drawable != null) {
            Drawable oldDrawable = view.getThumbDrawable();
            drawable.setState(oldDrawable.getState());
//            updateDrawableState(resId, oldDrawable, drawable);
            view.setThumbDrawable(drawable);
        }
    }

    public static void setSwitchTrack(@SuppressLint("UseSwitchCompatOrMaterialCode") final Switch view, int resId) {
        if (resId == SkinConfigs.ID_NULL) {
            return;
        }
        SkinLogger.v("setSwitchTrack resId = " + resId);
        final Drawable drawable = ResManager.getInstance().getDrawable(resId);
        if (drawable != null) {
            Drawable oldDrawable = view.getTrackDrawable();
            drawable.setState(oldDrawable.getState());
//            updateDrawableState(resId, oldDrawable, drawable);
            view.setTrackDrawable(drawable);
        }
    }
}
