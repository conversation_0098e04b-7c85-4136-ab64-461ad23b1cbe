package com.autoai.baseline.support.skincore.applier.views;

import android.app.Activity;
import android.content.Context;
import android.content.res.TypedArray;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.res.ResManager;

/**
 * 状态栏和导航栏 换肤处理
 *
 * <AUTHOR>
 */
public class StatusBarUtil {
    /**
     * 状态栏和导航栏
     */
    private static final int[] APPCOMPAT_COLOR_PRIMARY_DARK_ATTRS = {android.R.attr.colorPrimaryDark};
    /**
     * 状态栏和导航栏
     */
    private static final int[] STATUS_BAR_COLOR_ATTRS = {android.R.attr.statusBarColor, android.R.attr.navigationBarColor};

    /**
     * 隐藏构造方法，不允许创建实例
     */
    private StatusBarUtil() {
    }

    /**
     * 状态栏和导航栏
     */
    public static void updateStatusBar(Activity activity) {
        SkinLogger.v("updateStatusBar activity = " + activity);
        int[] resIds = getResId(activity, STATUS_BAR_COLOR_ATTRS);
        //修改状态栏的颜色
        //如果没有配置 属性 则获得0
        if (resIds[0] == SkinConfigs.ID_NULL) {
            int statusBarColorId = getResId(activity, APPCOMPAT_COLOR_PRIMARY_DARK_ATTRS)[0];
            if (statusBarColorId != SkinConfigs.ID_NULL) {
                int color = ResManager.getInstance().getColor(statusBarColorId);
                activity.getWindow().setStatusBarColor(color);
            }
        } else {
            int color = ResManager.getInstance().getColor(resIds[0]);
            activity.getWindow().setStatusBarColor(color);
        }
        //修改底部虚拟按键的颜色
        if (resIds[1] != SkinConfigs.ID_NULL) {
            int color = ResManager.getInstance().getColor(resIds[1]);
            activity.getWindow().setNavigationBarColor(color);
        }
    }

    private static int[] getResId(Context context, int[] attrs) {
        int[] resIds = new int[attrs.length];
        try (TypedArray typedArray = context.obtainStyledAttributes(attrs)) {
            for (int i = 0; i < typedArray.length(); i++) {
                resIds[i] = typedArray.getResourceId(i, 0);
            }
            typedArray.recycle();
        } catch (Exception e) {
            SkinLogger.e("StatusBarUtil.getResId", e);
        }
        return resIds;
    }
}
