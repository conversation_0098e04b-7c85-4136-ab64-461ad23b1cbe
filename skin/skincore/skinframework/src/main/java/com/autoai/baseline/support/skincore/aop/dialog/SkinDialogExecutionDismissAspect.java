package com.autoai.baseline.support.skincore.aop.dialog;

import android.app.Dialog;

import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.viewholders.WindowHolder;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinDialogExecutionDismissAspect {

    public static SkinDialogExecutionDismissAspect aspectOf() {
        return new SkinDialogExecutionDismissAspect();
    }

    @Pointcut("execution(* android.app.Dialog+.dismiss())")
    public void dialogExecutionDismissPointcut() {
    }

    @Around("dialogExecutionDismissPointcut()")
    public Object aroundDialogExecutionDismiss(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = joinPoint.proceed();
        //
        Object target = joinPoint.getTarget();
        if (target instanceof Dialog) {
            Dialog dialog = (Dialog) target;
            SkinLogger.d("SkinDialogDismissAspect dialogList.remove(" + dialog + "): ");
            WindowHolder.removeDialog(dialog);
        }
        //
        return result;
    }
}
