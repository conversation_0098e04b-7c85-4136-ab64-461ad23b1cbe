package com.autoai.baseline.support.skincore.language.aspect;

import android.widget.TextView;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.applier.DynamicCodingApplier;
import com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils;
import com.autoai.baseline.support.skincore.language.DynamicTextApplier;
import com.autoai.baseline.support.skincore.res.ResManager;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinTextHintAspect {

    public static SkinTextHintAspect aspectOf() {
        return new SkinTextHintAspect();
    }

    @Pointcut("call(* android.widget.TextView+.setHint(..)) " + SkinConfigs.AOP_WITHOUT)
    public void hintPointcut() {
    }

    /**
     * TextView.setHint(CharSequence hint)
     * TextView.setHint(@StringRes int resId)
     */
    @Around("hintPointcut()")
    public Object aroundHint(ProceedingJoinPoint joinPoint) throws Throwable {
        if (SkinConfigs.isSupportTextStr()) {
            Object target = joinPoint.getTarget();
            Object[] params = joinPoint.getArgs();
            if (params != null && target instanceof TextView) {
                TextView textView = (TextView) target;
                if (params.length > 0) {
                    Object params1 = params[0];
                    if (params1 instanceof Integer) {
                        int resourceId = (Integer) params1;
                        //修改UI
                        DynamicTextApplier.setHint(textView, resourceId);
                        return null;
                    } else if (params1 instanceof String) {
                        String key1 = params1.hashCode() + "";
                        Integer resId = ResManager.RES_MAP.get(key1);
                        SkinLogger.d("文言排查::AOP TextView+.setHint resId = " + resId);
                        if (resId != null && resId != SkinConfigs.ID_NULL) {
                            //修改UI
                            DynamicTextApplier.setHint(textView, resId);
                            ResManager.RES_MAP.remove(key1);
                            return null;
                        } else {
                            SkinAttributesUtils.removeViewAttribute(textView, SkinAttributesUtils.ATTRIBUTE_TEXT);
                        }
                        return joinPoint.proceed();
                    }
                }
            }

        }
        return joinPoint.proceed();
    }
}
