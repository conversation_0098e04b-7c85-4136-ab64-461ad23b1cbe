package com.autoai.baseline.support.skincore.aop.widget;

import android.graphics.drawable.Drawable;
import android.widget.ImageView;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.res.ResManager;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.applier.DynamicCodingApplier;
import com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinImageDrawableAspect {

    public static SkinImageDrawableAspect aspectOf() {
        return new SkinImageDrawableAspect();
    }

    /**
     * 任何一个包下面的任何一个类下面的任何一个带有SkinActivityInit的方法，构成了这个切面
     */
    @Pointcut("call(* android.widget.ImageView+.setImageDrawable(..)) "
            + SkinConfigs.AOP_WITHOUT
            + "&& !within(com.bumptech.glide.request.target.*) "
    )
    public void imageDrawablePointcut() {
    }

    /**
     * 拦截方法
     */
    @Around("imageDrawablePointcut()")
    public Object aroundImageDrawable(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        if (target instanceof ImageView) {
            ImageView imageView = (ImageView) target;
            Object[] params = joinPoint.getArgs();
            Object param = params[0];
            if (param instanceof Integer) {
                int resId = (int) param;
                if (resId == SkinConfigs.ID_NULL) {
                    SkinLogger.d("排查ImageDrawable AttributeUtils.removeViewAttribute 1");
                    SkinAttributesUtils.removeViewAttribute(imageView, SkinAttributesUtils.ATTRIBUTE_SRC);
                } else {
                    SkinLogger.d("排查ImageDrawable SkinDynamicCoding.setImageSrc 2 resId = " + resId);
                    DynamicCodingApplier.setImageSrc(imageView, resId);
                    return null;
                }
            } else if (param instanceof Drawable) {
                String key1 = param.hashCode() + "";
                Integer resId = ResManager.RES_MAP.get(key1);
                if (resId != null && resId != SkinConfigs.ID_NULL) {
                    SkinLogger.d("排查ImageDrawable SkinDynamicCoding.setImageSrc 3 resId = " + resId);
                    DynamicCodingApplier.setImageSrc(imageView, resId);
                    ResManager.RES_MAP.remove(key1);
                    return null;
                } else {
                    SkinLogger.d("排查ImageDrawable AttributeUtils.removeViewAttribute 4");
                    SkinAttributesUtils.removeViewAttribute(imageView, SkinAttributesUtils.ATTRIBUTE_SRC);
                }
            } else {
                SkinLogger.d("排查ImageDrawable AttributeUtils.removeViewAttribute 5");
                SkinAttributesUtils.removeViewAttribute(imageView, SkinAttributesUtils.ATTRIBUTE_SRC);
            }
        }
        //执行拦截方法
        return joinPoint.proceed();
    }
}
