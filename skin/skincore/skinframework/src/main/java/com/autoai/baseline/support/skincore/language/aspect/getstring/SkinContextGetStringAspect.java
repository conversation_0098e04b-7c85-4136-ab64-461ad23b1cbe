package com.autoai.baseline.support.skincore.language.aspect.getstring;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * Context getString
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinContextGetStringAspect {

    public static SkinContextGetStringAspect aspectOf() {
        return new SkinContextGetStringAspect();
    }

    @Pointcut("call(* android.content.Context+.getString(..)) " + SkinConfigs.AOP_WITHOUT)
    public void getStringPointcut() {
    }

    @Around("getStringPointcut()")
    public Object aroundGetString(ProceedingJoinPoint joinPoint) throws Throwable {
        if (SkinConfigs.isSupportTextStr()) {
            SkinLogger.d("文言排查::AOP getString << SkinContextGetStringAspect");
            return SkinStringUtil.getStringJoinPoint(joinPoint);
        }else {
            return joinPoint.proceed();
        }
    }
}
