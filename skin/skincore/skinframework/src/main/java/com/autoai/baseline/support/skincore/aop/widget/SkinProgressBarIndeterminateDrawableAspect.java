package com.autoai.baseline.support.skincore.aop.widget;

import android.graphics.drawable.Drawable;
import android.widget.ProgressBar;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.res.ResManager;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.applier.DynamicCodingApplier;
import com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinProgressBarIndeterminateDrawableAspect {

    public static SkinProgressBarIndeterminateDrawableAspect aspectOf() {
        return new SkinProgressBarIndeterminateDrawableAspect();
    }

    /**
     * 任何一个包下面的任何一个类下面的任何一个带有SkinActivityInit的方法，构成了这个切面
     */
    @Pointcut("call(* android.widget.ProgressBar+.setIndeterminateDrawable(..)) " + SkinConfigs.AOP_WITHOUT)
    public void indeterminateDrawablePointcut() {
    }

    /**
     * 拦截方法
     */
    @Around("indeterminateDrawablePointcut()")
    public Object aroundIndeterminateDrawable(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        if (target instanceof ProgressBar) {
            ProgressBar progressBar = (ProgressBar) target;
            Object[] params = joinPoint.getArgs();
            Object param = params[0];
            if (param instanceof Drawable) {
                String key1 = param.hashCode() + "";
                Integer resId = ResManager.RES_MAP.get(key1);
                SkinLogger.d("AOP ProgressBar+.setIndeterminateDrawable resId = " + resId);
                if (resId != null && resId != SkinConfigs.ID_NULL) {
                    DynamicCodingApplier.setProgressIndeterminateDrawable(progressBar, resId);
                    ResManager.RES_MAP.remove(key1);
                    return null;
                } else {
                    SkinAttributesUtils.removeViewAttribute(progressBar, SkinAttributesUtils.ATTRIBUTE_INDETERMINATE_DRAWABLE);
                }
            } else {
                SkinAttributesUtils.removeViewAttribute(progressBar, SkinAttributesUtils.ATTRIBUTE_INDETERMINATE_DRAWABLE);
            }
        }
        //执行拦截方法
        return joinPoint.proceed();
    }
}
