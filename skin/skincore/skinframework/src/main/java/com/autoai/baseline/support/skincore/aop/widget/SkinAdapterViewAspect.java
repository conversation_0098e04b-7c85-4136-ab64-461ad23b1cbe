package com.autoai.baseline.support.skincore.aop.widget;

import android.view.View;
import android.view.ViewGroup;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinManager;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinAdapterViewAspect {

    public static SkinAdapterViewAspect aspectOf() {
        return new SkinAdapterViewAspect();
    }

    @Pointcut("execution(* android.widget.Adapter+.getView(..)) " + SkinConfigs.AOP_WITHOUT)
    public void adapterPointcut() {
    }

    @Around("adapterPointcut()")
    public Object aroundAdapter(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        Object convertView = args[1];
        if (convertView == null) {
            ViewGroup parent = (ViewGroup) args[2];
            Object result = joinPoint.proceed();
            if (result instanceof View) {
                View view = (View) result;
                //fix：TOYOTA25MM-19246【1A】【系统测试】【机型-L】【1M】【AppStore】【Bench】应用商店停止服务后应用按钮和文言颜色不一致
                SkinManager.getInstance().applyView(view);
                SkinManager.getInstance().tagInnerView(parent, view);
            }
            return result;
        } else {
            return joinPoint.proceed();
        }
    }
}
