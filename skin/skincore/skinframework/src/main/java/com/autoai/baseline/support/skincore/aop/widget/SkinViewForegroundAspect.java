package com.autoai.baseline.support.skincore.aop.widget;

import android.graphics.drawable.Drawable;
import android.view.View;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.res.ResManager;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.applier.DynamicCodingApplier;
import com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinViewForegroundAspect {

    public static SkinViewForegroundAspect aspectOf() {
        return new SkinViewForegroundAspect();
    }

    @Pointcut("call(* android.view.View+.setForeground(..)) " + SkinConfigs.AOP_WITHOUT)
    public void foregroundPointcut() {
    }

    @Around("foregroundPointcut()")
    public Object aroundForeground(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        if (target instanceof View) {
            View view = (View) target;
            Object[] params = joinPoint.getArgs();
            Object param = params[0];
            if (param instanceof Integer) {
                int resId = (int) param;
                SkinLogger.d("AOP View+.setForeground resId = " + resId);
                if (resId == SkinConfigs.ID_NULL) {
                    SkinAttributesUtils.removeViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_FOREGROUND);
                } else {
                    DynamicCodingApplier.setForeground(view, resId);
                    return null;
                }
            } else if (param instanceof Drawable) {
                String key1 = param.hashCode() + "";
                Integer resId = ResManager.RES_MAP.get(key1);
                SkinLogger.d("AOP View+.setForeground resId = " + resId);
                if (resId == null) {
                    DynamicCodingApplier.setForeground(view, SkinConfigs.ID_NULL);
                } else {
                    DynamicCodingApplier.setForeground(view, resId);
                }
                ResManager.RES_MAP.remove(key1);
            } else if (param == null) {
                SkinLogger.d("AOP View+.setForeground param == null");
                DynamicCodingApplier.setForeground(view, SkinConfigs.ID_NULL);
            } else {
                SkinAttributesUtils.removeViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_FOREGROUND);
            }
        }
        //执行拦截方法
        return joinPoint.proceed();
    }
}
