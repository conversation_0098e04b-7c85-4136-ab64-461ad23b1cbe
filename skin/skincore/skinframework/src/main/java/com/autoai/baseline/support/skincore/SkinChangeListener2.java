package com.autoai.baseline.support.skincore;

import com.autoai.baseline.support.skincore.daynight.DatNightMode;
import com.autoai.baseline.support.skincore.res.ResManager;

import java.util.Locale;

/**
 * 皮肤切换、昼夜模式切换，回调
 *
 * <AUTHOR>
 */
public abstract class SkinChangeListener2 implements SkinChangeListener {
    /**
     * 换肤 或者 切换昼夜模式，都会触发此通知回调
     *
     * @param skinNickName 当前皮肤包昵称
     * @param dayNightMode 当前昼夜模式
     */
    @Override
    public void change(String skinNickName, DatNightMode dayNightMode) {
        change(skinNickName, dayNightMode, ResManager.getInstance().getLocale());
    }

    /**
     * 换肤、切换昼夜模式、语言切换（如果开启了文言支持）都会触发此通知回调
     *
     * @param skinNickName 当前皮肤包昵称
     * @param dayNightMode 当前昼夜模式
     * @param locale       语言环境
     */
    abstract void change(String skinNickName, DatNightMode dayNightMode, Locale locale);
}
