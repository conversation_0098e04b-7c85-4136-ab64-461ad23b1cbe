package com.autoai.baseline.support.skincore.language;

import android.text.TextUtils;
import android.widget.TextView;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.attribute.ViewTagUtil;
import com.autoai.baseline.support.skincore.language.bean.StringBean;

public class LanguageViewUtil {

    /**
     * 文言替换
     */
    public static void setText(TextView view, int resId) {
        SkinLogger.d("文言排查:: setText: view = " + view + ", resId = " + resId);
        if (resId == SkinConfigs.ID_NULL) {
            return;
        }
        final String str = StringResManager.getInstance().getString(resId, false);
        if (TextUtils.isEmpty(str)) {
            return;
        }
        SkinLogger.d("setText 1 --> str = " + str + ", view = " + view);
        ViewTagUtil.changeTextSize(view);
        view.setText(str);
    }

    /**
     * 文言替换
     */
    public static void setText(TextView view, StringBean stringBean) {
        SkinLogger.d("文言排查:: setText: view = " + view + " --> " + stringBean);
        if (stringBean == null || stringBean.getResId() == SkinConfigs.ID_NULL) {
            return;
        }
        final String str = StringResManager.getInstance().getString(stringBean.getResId(), false, stringBean.getArgs());
        if (TextUtils.isEmpty(str)) {
            return;
        }
        SkinLogger.d("setText 1 --> str = " + str + ", view = " + view);
        ViewTagUtil.changeTextSize(view);
        view.setText(str);
    }

    /**
     * 文言替换
     */
    public static void setText(TextView view, int resId, TextView.BufferType bufferType) {
        SkinLogger.d("文言排查:: setText: view = " + view + ", resId = " + resId + ", bufferType = " + bufferType);
        if (resId == SkinConfigs.ID_NULL) {
            return;
        }
        final String str = StringResManager.getInstance().getString(resId, false);
        if (TextUtils.isEmpty(str)) {
            return;
        }
        ViewTagUtil.changeTextSize(view);
        view.setText(str, bufferType);
    }

    /**
     * 文言替换
     */
    public static void setText(TextView view, StringBean stringBean, TextView.BufferType bufferType) {
        SkinLogger.d("文言排查:: setText: view = " + view + ", stringBean = " + stringBean + ", bufferType = " + bufferType);
        if (stringBean == null || stringBean.getResId() == SkinConfigs.ID_NULL) {
            return;
        }
        final String str = StringResManager.getInstance().getString(stringBean.getResId(), false, stringBean.getArgs());
        if (TextUtils.isEmpty(str)) {
            return;
        }
        ViewTagUtil.changeTextSize(view);
        view.setText(str, bufferType);
    }

    /**
     * 文言替换
     */
    public static void setHint(TextView view, int resId) {
        SkinLogger.d("文言排查:: setHint: view = " + view + ", resId = " + resId);
        if (resId == SkinConfigs.ID_NULL) {
            return;
        }
        final String str = StringResManager.getInstance().getString(resId, false);
        if (TextUtils.isEmpty(str)) {
            return;
        }
        ViewTagUtil.changeTextSize(view);
        view.setHint(str);
    }
}
