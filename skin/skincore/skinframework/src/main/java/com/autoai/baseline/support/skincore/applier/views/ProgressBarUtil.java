package com.autoai.baseline.support.skincore.applier.views;

import android.graphics.drawable.Animatable;
import android.graphics.drawable.Drawable;
import android.widget.AbsSeekBar;
import android.widget.ProgressBar;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.res.ResManager;

/**
 * ProgressBarUtil 资源应用
 *
 * <AUTHOR>
 */
public class ProgressBarUtil {
    /**
     * 隐藏构造方法，不允许创建实例
     */
    private ProgressBarUtil() {
    }

    /**
     * SeekBar 设置 Thumb
     */
    public static void setAbsSeekBarThumb(final AbsSeekBar view, int resId) {
        final Drawable drawable = ResManager.getInstance().getDrawable(resId);
        if (drawable != null) {
//            Drawable oldDrawable = view.getThumb();
//            updateDrawableState(resId, oldDrawable, drawable);
            view.setThumb(drawable);
            view.setThumbOffset(0);
        }
    }

    /**
     * 进度 Drawable
     */
    public static void setProgressDrawable(final ProgressBar progressBar, int resId) {
        if (resId == SkinConfigs.ID_NULL) {
            return;
        }
        //
        final Drawable drawable = ResManager.getInstance().getDrawable(resId);
        if (drawable != null) {
//            if (SkinLogger.isLoggable()) {
//                int newHeight = drawable.getIntrinsicHeight();
//                int newWidth = drawable.getIntrinsicWidth();
//                //
//                Drawable oldDrawable = progressBar.getIndeterminateDrawable();
//                int height = oldDrawable.getIntrinsicHeight();
//                int width = oldDrawable.getIntrinsicWidth();
//                //
//                drawable.setBounds(0, 0, width, height);
//                //
//                String resName = ResManager.getInstance().getResources().getResourceEntryName(resId);
//                SkinLogger.v("DrawableSize-->resName = " + resName + ", resId = " + resId +
//                        " \nold:--> Height = " + height + ", Width = " + width
//                        + "\nnew --> Height = " + newHeight + ", Width = " + newWidth
//                        + "\nnew --> Heightw = " + drawable.getIntrinsicHeight() + ", Widthw = " + drawable.getIntrinsicWidth()
//                );
//            }
            boolean needsTileify = SdkVerAdapterUtil.needsTileify(drawable);
            SkinLogger.v("setProgressDrawable:needsTileify = " + needsTileify + ", view = " + progressBar);
            if (needsTileify) {
                //内部重新设置了drawable的宽高
                progressBar.setProgressDrawableTiled(drawable);
            } else {
                //内部重新设置了drawable的宽高
                progressBar.setProgressDrawable(drawable);
            }
            if (drawable instanceof Animatable) {
                ((Animatable) drawable).start();
            }
        }

    }

    /**
     * 无进度的 Drawable
     * <p>
     * 单独处理：ProgressBar.setIndeterminateDrawable不生效或不显示
     * 1、drawable.setBounds
     * 2、主动调用启动动画
     */
    public static void setProgressIndeterminateDrawable(final ProgressBar progressBar, int resId) {
        if (resId == SkinConfigs.ID_NULL) {
            return;
        }
        final Drawable drawable = ResManager.getInstance().getDrawable(resId);
        if (drawable != null) {
            boolean needsTileify = SdkVerAdapterUtil.needsTileify(drawable);
            if (SkinLogger.isLoggable()) {
                SkinLogger.printStackTrace("setProgressIndeterminateDrawable: view = " + progressBar);
                Drawable oldDrawable = progressBar.getIndeterminateDrawable();
                int height = oldDrawable.getIntrinsicHeight();
                int width = oldDrawable.getIntrinsicWidth();
                int newHeight = drawable.getIntrinsicHeight();
                int newWidth = drawable.getIntrinsicWidth();
                //
                String resName = ResManager.getInstance().getResources().getResourceEntryName(resId);
                SkinLogger.v("DrawableSize-->resName = " + resName + ", resId = " + resId +
                        " \nold:--> Height = " + height + ", Width = " + width
                        + "\nnew --> Height = " + newHeight + ", Width = " + newWidth);
            }
            drawable.setBounds(0, 0, progressBar.getMeasuredWidth(), progressBar.getMeasuredHeight());
            if (needsTileify) {
                progressBar.setIndeterminateDrawableTiled(drawable);
            } else {
                progressBar.setIndeterminateDrawable(drawable);
            }
            if (drawable instanceof Animatable) {
                ((Animatable) drawable).start();
            }
        }
    }
}
