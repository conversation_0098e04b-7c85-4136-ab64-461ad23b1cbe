package com.autoai.baseline.support.skincore.aop.popwindow;

import android.annotation.SuppressLint;
import android.widget.PopupWindow;

import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.viewholders.WindowHolder;

import java.lang.reflect.Method;

/**
 * PopWindow dismiss回调，用于释放资源
 *
 * <AUTHOR>
 */
@SuppressWarnings("all")
public class PopWindowUtils {
    public static void addDismissListener(final PopupWindow popupWindow) {
        try {
            Class<?> clazz = PopupWindow.class;
            @SuppressLint("SoonBlockedPrivateApi") Method method = clazz.getDeclaredMethod("getOnDismissListener");
            method.setAccessible(true);
            PopupWindow.OnDismissListener dismissListener = (PopupWindow.OnDismissListener) method.invoke(popupWindow);
            if (dismissListener == null) {
                popupWindow.setOnDismissListener(new PopupWindow.OnDismissListener() {
                    @Override
                    public void onDismiss() {
                        WindowHolder.removePopupWindow(popupWindow);
                    }
                });
            }
        } catch (Exception e) {
            SkinLogger.w("addDismissListener", e);
        }
    }
}
