package com.autoai.baseline.support.skincore.aop;

import android.view.View;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.SkinManager;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinBaseQuickAdapter1Aspect {

    public static SkinBaseQuickAdapter1Aspect aspectOf() {
        return new SkinBaseQuickAdapter1Aspect();
    }

    @Pointcut("execution(* com.chad.library.adapter.base.BaseQuickAdapter+.createBaseViewHolder(..)) " + SkinConfigs.AOP_WITHOUT)
    public void createBaseViewHolderPointcut() {
    }

    @Around("createBaseViewHolderPointcut()")
    public Object aroundBaseViewHolder(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        if (args.length > 0) {
            Object args1 = args[0];
            SkinLogger.d("SkinBaseQuickAdapter1Aspect args1 :" + args1);
            if (args1 instanceof View) {
                View view = (View) args1;
                SkinManager.getInstance().applyView(view);
            }
        }
        //执行拦截方法
        return joinPoint.proceed();
    }
}
