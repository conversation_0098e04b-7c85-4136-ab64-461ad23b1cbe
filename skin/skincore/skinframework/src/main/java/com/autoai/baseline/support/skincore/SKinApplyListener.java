package com.autoai.baseline.support.skincore;

import android.app.Activity;
import android.app.Dialog;
import android.view.View;
import android.widget.PopupWindow;

import java.util.List;

/**
 * 换肤应用的时候Activity、Dialog等顺序定制
 *
 * <AUTHOR>
 */
public interface SKinApplyListener {

    /**
     * 需要变更的的内容
     *
     * @param dialogList      Dialog
     * @param popupWindowList PopupWindow
     * @param activityList    Activity
     * @param viewList        独立添加的View
     */
    SkinApplySortBean getOrderList(List<Dialog> dialogList,
                                   List<PopupWindow> popupWindowList,
                                   List<Activity> activityList,
                                   List<View> viewList);
}
