package com.autoai.baseline.support.skincore.aop.popwindow;

import android.widget.PopupWindow;

import com.autoai.baseline.support.skincore.viewholders.WindowHolder;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinPopWindowDismissLisAspect {

    public static SkinPopWindowDismissLisAspect aspectOf() {
        return new SkinPopWindowDismissLisAspect();
    }

    @Pointcut("call(* android.widget.PopupWindow+.setOnDismissListener(..))"
            + "&& !within(com.autoai.baseline.support.skincore.aop.popwindow.SkinPopWindowDismissLisAspect)"
            + "&& !within(com.autoai.baseline.support.skincore.aop.popwindow.SkinPopWindowShowAsDropDownAspect)"
            + "&& !within(com.autoai.baseline.support.skincore.aop.popwindow.SkinPopWindowShowAtLocationAspect)"
            + "&& !within(com.autoai.baseline.support.skincore.aop.popwindow.PopWindowUtils)"
    )
    public void dismissPointcut() {
    }

    @Around("dismissPointcut()")
    public Object aroundDismiss(ProceedingJoinPoint joinPoint) throws Throwable {
        //
        Object target = joinPoint.getTarget();
        Object[] targets = joinPoint.getArgs();
        if (targets.length > 0) {
            final PopupWindow.OnDismissListener dismissListener = (PopupWindow.OnDismissListener) targets[0];
            if (target instanceof PopupWindow) {
                final PopupWindow popupWindow = (PopupWindow) target;
                popupWindow.setOnDismissListener(new PopupWindow.OnDismissListener() {
                    @Override
                    public void onDismiss() {
                        WindowHolder.removePopupWindow(popupWindow);
                        dismissListener.onDismiss();
                    }
                });
            }
        }
        return null;
    }
}
