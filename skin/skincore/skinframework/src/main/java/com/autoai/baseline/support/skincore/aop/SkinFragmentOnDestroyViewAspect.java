package com.autoai.baseline.support.skincore.aop;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.viewholders.WindowHolder;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinFragmentOnDestroyViewAspect {
    public static SkinFragmentOnDestroyViewAspect aspectOf() {
        return new SkinFragmentOnDestroyViewAspect();
    }

    @Pointcut("execution(* android.app.DialogFragment+.onDestroyView(..)) " + SkinConfigs.AOP_WITHOUT)
    public void myPointcut() {
    }

    @Around("myPointcut()")
    public Object aroundOnCreateView(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        SkinLogger.i("chucan Fragment+.onDestroyView target = " + target);
        if (target instanceof android.app.DialogFragment) {
            android.app.DialogFragment fragment = (android.app.DialogFragment) target;
            SkinLogger.i("chucan SDK　Fragment.removeFragmentDialog Dialog = " + fragment.getDialog());
            WindowHolder.removeDialog(fragment.getDialog());
        }
        return joinPoint.proceed();
    }
}
