package com.autoai.baseline.support.skincore;

import android.util.Log;

import androidx.annotation.NonNull;

import com.autoai.baseline.support.autoinflater.LogListener;

import java.util.ArrayList;
import java.util.List;

/**
 * 多资源模块日志管理
 *
 * <AUTHOR>
 */
public class SkinLogger {

    /**
     * 日志排除项配置
     */
    public static List<String> excludeList = new ArrayList<>();

    static {
        excludeList.add("navigation_unknown_road");
    }

    /**
     * 日志回调，用于各个应用设置监听，将换肤框架的日志输出到对应的应用日志内部
     * 保证能够使用xLog（或者其他日志）保存日志信息。
     * 如果未指定，则默认使用 android.util.Log 输出日志到logcat。
     */
    @NonNull
    private static LogListener logListener = new LogListener();

    /**
     * 日志回调，用于各个应用设置监听，将换肤框架的日志输出到对应的应用日志内部
     * 保证能够使用xLog（或者其他日志）保存日志信息。
     * 如果未指定，则默认使用 android.util.Log 输出日志到logcat。
     */
    public static void setLogListener(@NonNull LogListener logListener) {
        SkinLogger.logListener = logListener;
    }

    /**
     * 日志是否打印
     */
    private static boolean isLoggable = BuildConfig.isForceShowAllLog;
    private static boolean isLogShowTrace = BuildConfig.isLogShowTrace;
    private static boolean isShowDebuggingLog = BuildConfig.isShowDebuggingLog;
    private static boolean isForceShowAllLog = BuildConfig.isForceShowAllLog;
    public static final String TAG = "SkinFramework";
    private static final String LOG_NAME = SkinLogger.class.getSimpleName() + ".java";

    private SkinLogger() {
    }

    public static boolean isLogShowTrace() {
        return isLogShowTrace;
    }

    public static void setLogShowTrace(boolean isLogShowTrace) {
        SkinLogger.isLogShowTrace = isLogShowTrace;
    }

    public static boolean isShowDebuggingLog() {
        return isShowDebuggingLog;
    }

    public static void setShowDebuggingLog(boolean isShowDebuggingLog) {
        SkinLogger.isShowDebuggingLog = isShowDebuggingLog;
    }

    public static boolean isForceShowAllLog() {
        return isForceShowAllLog;
    }

    public static void setForceShowAllLog(boolean isForceShowAllLog) {
        SkinLogger.isForceShowAllLog = isForceShowAllLog;
    }

    public static void setLoggable(boolean isLoggable) {
        if (isForceShowAllLog) {
            SkinLogger.isLoggable = true;
        } else {
            SkinLogger.isLoggable = isLoggable;
        }

    }

    public static boolean isLoggable() {
        return isLoggable;
    }

    /**
     * 获取当前日志打印位置
     */
    private static String addStackTrace(String msg) {
        if (isLogShowTrace) {
            StackTraceElement[] elements = Thread.currentThread().getStackTrace();
            for (int index = 5; index >= 0; index--) {
                if (elements.length <= index) {
                    continue;
                }
                if (LOG_NAME.equals(elements[index].getFileName())) {
                    StackTraceElement element = elements[index + 1];
                    return BuildConfig.VERSION + "(" + element.getFileName() + ":" + element.getLineNumber() + ")" + msg;
                }
            }
        } else {
            msg = BuildConfig.VERSION + "::" + msg;
        }
        return msg;
    }


    public static void v(String msg) {
        if (isLoggable) {
            logListener.v(TAG, addStackTrace(msg));
        }
    }

    public static void forceV(String msg) {
        logListener.v(TAG, addStackTrace(msg));
    }

    public static void v(String msg, Throwable t) {
        if (isLoggable) {
            logListener.v(TAG, addStackTrace(msg), t);
        }
    }


    public static void d(String msg) {
        if (isLoggable) {
            logListener.d(TAG, addStackTrace(msg));
        }
    }

    public static void forceD(String msg) {
        logListener.d(TAG, addStackTrace(msg));
    }

    public static void d(String msg, Throwable t) {
        if (isLoggable) {
            logListener.d(TAG, addStackTrace(msg), t);
        }
    }


    public static void i(String msg) {
        if (isLoggable) {
            logListener.i(TAG, addStackTrace(msg));
        }
    }

    public static void forceI(String msg) {
        logListener.i(TAG, addStackTrace(msg));
    }

    public static void i(String msg, Throwable t) {
        if (isLoggable) {
            logListener.i(TAG, addStackTrace(msg), t);
        }
    }


    public static void w(String msg) {
        if (isLoggable) {
            logListener.w(TAG, addStackTrace(msg));
        }
    }

    public static void forceW(String msg) {
        logListener.w(TAG, addStackTrace(msg));
    }

    public static void w(String msg, Throwable t) {
        if (isLoggable) {
            logListener.w(TAG, addStackTrace(msg), t);
        }
    }

    public static void e(String msg) {
        if (isLoggable) {
            logListener.e(TAG, addStackTrace(msg));
        }
    }
    public static void forceE(String msg) {
        String log = addStackTrace(msg);
        logListener.e(TAG, log);
    }

    public static void forceE(String msg, Throwable t) {
        String log = addStackTrace(msg);
        logListener.e(TAG, log, t);
    }

    public static void e(String msg, Throwable t) {
        if (isLoggable) {
            logListener.e(TAG, addStackTrace(msg), t);
        }
    }

    /**
     * 打印堆栈信息的剪片方法
     */
    public static void printStackTrace(String msg) {
        String log = Log.getStackTraceString(new Throwable(msg));
        log = log.replace("java.lang.Throwable:", "");
        logListener.v(TAG, log);
    }
}
