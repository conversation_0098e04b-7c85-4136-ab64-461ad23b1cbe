package com.autoai.baseline.support.skincore;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsSeekBar;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.ProgressBar;
import android.widget.Switch;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.autoai.baseline.support.autoinflater.InflaterLogger;
import com.autoai.baseline.support.autoinflater.LogListener;
import com.autoai.baseline.support.skincore.applier.DynamicCodingApplier;
import com.autoai.baseline.support.skincore.applier.ViewApplier;
import com.autoai.baseline.support.skincore.attribute.SkinState;
import com.autoai.baseline.support.skincore.attribute.ViewTagUtil;
import com.autoai.baseline.support.skincore.daynight.DatNightMode;
import com.autoai.baseline.support.skincore.daynight.DayNightUtil;
import com.autoai.baseline.support.skincore.language.DynamicTextApplier;
import com.autoai.baseline.support.skincore.language.StringResManager;
import com.autoai.baseline.support.skincore.res.ResManager;
import com.autoai.baseline.support.skincore.skinpathloader.SkinChanger;
import com.autoai.baseline.support.skincore.skinpathloader.SkinLoader;
import com.autoai.baseline.support.skincore.viewholders.WindowHolder;

import java.util.HashSet;
import java.util.Locale;
import java.util.Set;

/**
 * 换肤框架（多资源加载框架）唯一对外综合管理类
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public class SkinManager {

    /**
     * LayoutFactory 插件，使得能实现换肤功能
     */
    private final SkinPlugin skinPlugin = new SkinPlugin();

    /**
     * 单例
     */
    private static class Holder {
        private static final SkinManager INSTANCE = new SkinManager();
    }

    /**
     * 单例
     */
    private SkinManager() {
    }

    /**
     * 单例
     */
    public static SkinManager getInstance() {
        return Holder.INSTANCE;
    }

    /**
     * 换肤框架的日志输出控制
     */
    public SkinManager setLoggable(boolean isLoggable) {
        SkinLogger.setLoggable(isLoggable);
        return this;
    }

    /**
     * 是否支持 text、hint 等字符串显示内容的 更换，如果是多语言应用可以开始，用于多语言变更控制
     */
    public SkinManager setSupportTextStr(boolean supportTextStr) {
        SkinConfigs.setSupportTextStr(supportTextStr);
        return this;
    }

    /**
     * 英文文言 文字大小差值
     * <p>
     * 英文语言，自动减指定像素值
     * 项目需求: 我们启用了中英文切换功能，但是需求现在需要切换成英文的时候，整体字号小2像素，想问一下咱们这个框架可以支持么？
     *
     * @param dif      英文自定缩小多少个字号，单位px（如果为负数，表示放大字号）；
     * @param markSize 英文大小表示，单位px，以此值为参考，大于或者小于此值做字号缩小（放大）
     * @param isUp     true，大于等于 marSize的才做字号缩小（放大）；false，小于等于 markSize 的做字号缩小（放大）
     */
    public SkinManager setEnTextSizeDif(float dif, int markSize, boolean isUp) {
        SkinConfigs.setEnTextSizeDif(dif, markSize, isUp);
        return this;
    }

    /**
     * 是否忽略前后台: true 忽略，直接换肤；false，只有应用切换到前台的时候才换肤
     *
     * @deprecated 取消前后台配置，所有项目都滥用了，要保证所有项目一起换肤的时候的性能，不要指望用此来协调性能，因为如果没有直接切换，应用从后台切到前台，会看起来闪一下
     */
    @Deprecated
    public SkinManager setIgnoredForeground(boolean ignoreForeground) {
//        SkinConfigs.setIgnoreForeground(ignoreForeground);
        return this;
    }

    /**
     * 换肤是否保持系统同步
     * 需要有系统设置参数{@link android.provider.Settings.System}读取权限
     * <p>
     * 是否支持昼夜模式应用（系统）同步，这个用打包参数再控制一下。
     */
    public SkinManager setSystemSync(boolean isSystemSync) {
        //此接口被各个App理解错误，滥用，注销此接口
        SkinConfigs.setIsSystemSync(isSystemSync);
        return this;
    }


    public SkinManager setCloudTextConfig(CloudTextConfig cloudTextConfig) {
        StringResManager.getInstance().setCloudTextListener(cloudTextConfig);
        return this;
    }

    /**
     * 日志回调，用于各个应用设置监听，将换肤框架的日志输出到对应的应用日志内部
     * 保证能够使用xLog（或者其他日志）保存日志信息。
     * 如果未指定，则默认使用 android.util.Log 输出日志到logcat。
     */
    public SkinManager setLogListener(@NonNull LogListener logListener) {
        SkinLogger.setLogListener(logListener);
        InflaterLogger.setLogListener(logListener);
        return this;
    }

    /**
     * 获取换肤插件
     */
    public SkinPlugin initPlugin() {
        return skinPlugin;
    }

    public Context getApplicationContext() {
        return skinPlugin.getApplicationContext();
    }

    /**
     * 判断当前应用是否在前台
     */
    private boolean isForeground() {
        return SkinActivityLifecycleCallbacks.isForeground();
    }

    /**
     * 添加 不在 Window 上的 View，使其也能跟随换肤
     */
    public void addView(View decorView) {
        WindowHolder.addView(decorView);
    }

    /**
     * 移除{@link SkinManager#addView(View)}添加的 不在Window 上的 View
     */
    public void removeView(View decorView) {
        WindowHolder.removeView(decorView);
    }

    /**
     * 把需要换肤的 View 添加到一个 parent 类中。
     * 比如RecyclerView的Item放在RecyclerView中，用以支持RecyclerView全局换肤；
     * 比如把ViewPager的Item添加到ViewPager上，用以支持ViewPager换肤
     *
     * @param parent 比如 RecyclerView、ViewPager
     * @param child  比如 RecyclerView的Item、ViewPager的Item
     */
    public void tagInnerView(ViewGroup parent, View child) {
        SkinState skinState = ViewTagUtil.getTagViewSkin(parent);
        if (skinState == null) {
            skinState = SkinState.getNewInstance();
            ViewTagUtil.setTagViewSkin(parent, skinState);
        }
        Set<View> viewSet = skinState.getViewSet();
        if (viewSet == null) {
            viewSet = new HashSet<>();
            skinState.setViewSet(viewSet);
        }
        viewSet.add(child);
    }

    /**
     * 使 View 使用当前皮肤
     */
    public void applyView(View view) {
        SkinLogger.i("applyView view = " + view);
        ViewApplier.applyRootView(view);
    }

    /**
     * 皮肤还原成默认的
     */
    public void restore() {
        SkinLoader.restore();
    }

    /**
     * 从Assets资源加载皮肤包
     *
     * @param assetPath    Assets资源的路径
     * @param skinNickName 皮肤别名，用于区分当前使用的皮肤，以后后续判断当前使用的是哪个皮肤
     * @param skinFileName 皮肤包文件名称
     */
    public void loadSkinForAssets(final String skinNickName, final String assetPath, String skinFileName) {
        loadSkinForAssets(assetPath, skinNickName, skinFileName, null);
    }

    /**
     * 从Assets资源加载皮肤包
     *
     * @param assetPath    Assets资源的路径
     * @param skinNickName 皮肤别名，用于区分当前使用的皮肤，以后后续判断当前使用的是哪个皮肤
     * @param skinFileName 皮肤包文件名称
     * @param locale       地区、语言环境
     */
    public void loadSkinForAssets(final String assetPath, final String skinNickName, final String skinFileName, final Locale locale) {
        SkinLoader.loadSkinForAssets(assetPath, skinNickName, skinFileName, locale);
    }

    /**
     * 加载皮肤包
     *
     * @param skinNickName 皮肤别名，用于区分当前使用的皮肤，以及后续判断当前使用的是哪个皮肤
     * @param httpSkinPath 皮肤包地址
     */
    public void loadSkinForNet(final String skinNickName, final String httpSkinPath) {
        loadSkinForNet(skinNickName, httpSkinPath, Locale.getDefault());
    }

    /**
     * 加载皮肤包
     *
     * @param skinNickName 皮肤别名，用于区分当前使用的皮肤，以后后续判断当前使用的是哪个皮肤
     * @param httpSkinPath 皮肤包地址
     */
    public void loadSkinForNet(final String skinNickName, final String httpSkinPath, final Locale locale) {
        SkinLoader.loadSkinForNet(skinNickName, httpSkinPath, locale);
    }

    /**
     * 加载皮肤包
     *
     * @param skinNickName  皮肤别名，用于区分当前使用的皮肤，以后后续判断当前使用的是哪个皮肤
     * @param localSkinPath 皮肤包地址
     */
    public void loadSkinForSdcard(final String skinNickName, String localSkinPath) {
        loadSkinForSdcard(skinNickName, localSkinPath, Locale.getDefault());
    }

    /**
     * 加载皮肤包
     *
     * @param skinNickName  皮肤别名，用于区分当前使用的皮肤，以后后续判断当前使用的是哪个皮肤
     * @param localSkinPath 皮肤包地址
     * @param locale        地区、语言环境
     */
    public void loadSkinForSdcard(final String skinNickName, final String localSkinPath, final Locale locale) {
        SkinLoader.loadSkinForSdcard(skinNickName, localSkinPath, locale);
    }

    public void notifySystemThemeChanged(DatNightMode datNightMode) {
        if (isAuto()) {
            return;
        }
        if (datNightMode == DatNightMode.NIGHT) {
            DayNightUtil.changeToNightMode();
        } else {
            DayNightUtil.changeToDayMode();
        }
    }

    public void notifySystemLanguage(Locale locale) {
        if (isAuto()) {
            return;
        }
        SkinChanger.changeLanguage(locale);
    }

    /**
     * 是否使用换肤框架自身主动跟随系统切换的功能
     * <p>
     * 默认使用
     */
    public SkinManager setAuto(boolean isAuto) {
        SkinConfigs.setAuto(isAuto);
        return this;
    }

    /**
     * 是否使用换肤框架自身主动跟随系统切换的功能
     */
    public boolean isAuto() {
        return SkinConfigs.isAuto();
    }

    /**
     * 切换到黑夜模式
     * <p>
     *
     * @deprecated 各个App自己切昼夜经常出问题，此处改为换肤框架统一处理，此接口不再对外
     */
    @Deprecated
    public void changeToNightMode() {
//        if (isNight()) {
//            SkinLogger.d("当前就是夜间模式");
//            return;
//        }
//        DayNightUtil.changeToNightMode();
    }

    /**
     * 切换到黑夜模式
     * <p>
     *
     * @deprecated 各个App自己切昼夜经常出问题，此处改为换肤框架统一处理，此接口不再对外
     */
    @Deprecated
    public void changeToDayMode() {
//        if (isDay()) {
//            SkinLogger.d("当前就是白天模式");
//            return;
//        }
//        DayNightUtil.changeToDayMode();
    }

    /**
     * 变更语言
     * <p>
     * 支持多语言需要配置 {@link #setSupportTextStr} 设置文本支持为true，才能生效
     *
     * @deprecated 各个App自己切昼夜经常出问题，此处改为换肤框架统一处理，此接口不再对外
     */
    @Deprecated
    public void changeLanguage(Locale locale) {
//        SkinChanger.changeLanguage(locale);
    }

    public Locale getLocal() {
        return ResManager.getInstance().getLocale();
    }

    /**
     * 获取当前皮肤昵称
     */
    public String getCurrentSkinNickName() {
        return SkinConfigs.getSkinNickName();
    }

    /**
     * 获取当前昼夜模式
     *
     * @return 昼夜模式
     */
    public DatNightMode getDayNightMode() {
        return DayNightUtil.getMode();
    }

    /**
     * 当前是否是夜间模式
     */
    public boolean isNight() {
        return DayNightUtil.isNight();
    }

    /**
     * 当前是否是白天模式
     */
    public boolean isDay() {
        return DayNightUtil.isDay();
    }

    /**
     * 根据原资源id匹配出夜间模式中对应的资源id，如果返回值为0，表示没有匹配到
     *
     * @param resId 原资源id
     */
    public int getNightResId(int resId) {
        return ResManager.getInstance().getNightResId(resId);
    }

    /**
     * 根据原资源id匹配出皮肤包中对应的资源id，如果返回值为0，表示没有匹配到
     *
     * @param resId 原资源id
     */
    public int getSkinResId(int resId) {
        return ResManager.getInstance().getSkinResId(resId);
    }

    /**
     * 获取默认的 Resource
     */
    public Resources getDefaultResource() {
        return ResManager.getInstance().getResources();
    }

    /**
     * 获取皮肤包 Resource
     */
    public Resources getSkinResource() {
        return ResManager.getInstance().getSkinResources();
    }

    /**
     * 添加 移除 换肤、昼夜切换前回调监听
     */
    public SkinManager addChangeBeforeListener(ChangeBeforeListener listener) {
        SkinConfigs.addChangeBeforeListener(listener);
        return this;
    }

    /**
     * 移除 换肤、昼夜切换前回调监听
     */
    public SkinManager removeChangeBeforeListener(ChangeBeforeListener listener) {
        SkinConfigs.removeChangeBeforeListener(listener);
        return this;
    }

    /**
     * 换肤、昼夜切换监听，会在 listener 中回掉
     * <p>
     * 可以使用 {@link SkinManager#addSkinChangeUiListener} 替代
     */
    public SkinManager addSkinChangeListener(final SkinChangeListener listener) {
        SkinConfigs.addSkinChangeListener(listener);
        return this;
    }

    /**
     * 移除 换肤、昼夜切换监听，避免内存泄漏
     * <p>
     * 可以使用 {@link SkinManager#removeSkinChangeUiListener}替代
     */
    public SkinManager removeSkinChangeListener(SkinChangeListener listener) {
        SkinConfigs.removeSkinChangeListener(listener);
        return this;
    }

    /**
     * 换肤、昼夜、语言 切换监听，会在 listener 中回掉
     */
    public SkinManager addSkinChangeUiListener(SkinChangeUiListener listener) {
        SkinConfigs.addSkinChangeUiListener(listener);
        return this;
    }

    /**
     * 移除 换肤、昼夜、语言 监听，避免内存泄漏
     */
    public SkinManager removeSkinChangeUiListener(SkinChangeUiListener listener) {
        SkinConfigs.removeSkinChangeUiListener(listener);
        return this;
    }

    /**
     * 换肤框架切换结束的回调操作
     */
    public SkinManager addChangeFinishListener(SkinChangeFinishListener listener) {
        SkinConfigs.addChangeFinishListener(listener);
        return this;
    }

    /**
     * 换肤框架切换结束的回调操作
     */
    public SkinManager removeChangeFinishListener(SkinChangeFinishListener listener) {
        SkinConfigs.removeChangeFinishListener(listener);
        return this;
    }

    /**
     * 换肤应用的时候Activity、Dialog等顺序定制
     */
    public SkinManager setSKinApplyListener(SKinApplyListener listener) {
        WindowHolder.setSKinApplyListener(listener);
        return this;
    }

    public String getString(int resId) {
        return StringResManager.getInstance().getString(resId, true);
    }

    public String getString(int resId, Object... formatArgs) {
        return StringResManager.getInstance().getString(resId, true, formatArgs);
    }

    public String[] getStringArray(int resId) {
        return StringResManager.getInstance().getStringArray(resId, true);
    }

    public Drawable getDrawable(int resId) {
        return ResManager.getInstance().getDrawable(resId);
    }

    public ColorStateList getColorStateList(int resId) {
        return ResManager.getInstance().getColorStateList(null, resId);
    }

    public int getColor(int resId) {
        return ResManager.getInstance().getColor(resId);
    }

    public void setText(TextView view, int resourceId) {
        DynamicTextApplier.setText(view, resourceId);
    }

    public void setText(TextView view, int resourceId, TextView.BufferType bufferType) {
        DynamicTextApplier.setText(view, resourceId, bufferType);
    }

    public void setBackground(View view, int resourceId) {
        DynamicCodingApplier.setBackground(view, resourceId);
    }

    public void setBackgroundColor(View view, int resourceId) {
        DynamicCodingApplier.setBackground(view, resourceId);
    }

    public void setForeground(View view, int resourceId) {
        DynamicCodingApplier.setForeground(view, resourceId);
    }

    public void setImageSrc(ImageView view, int resourceId) {
        DynamicCodingApplier.setImageSrc(view, resourceId);
    }

    public void setImageDrawable(ImageView view, int resourceId) {
        DynamicCodingApplier.setImageSrc(view, resourceId);
    }

    public void setTextColor(TextView view, int resourceId) {
        DynamicCodingApplier.setTextColor(view, resourceId);
    }

    public void setHint(TextView view, int resourceId) {
        DynamicTextApplier.setHint(view, resourceId);
    }

    public void setDrawableLeft(TextView view, int resourceId) {
        DynamicCodingApplier.setDrawableStart(view, resourceId);
    }

    public void setDrawableStart(TextView view, int resourceId) {
        DynamicCodingApplier.setDrawableStart(view, resourceId);
    }

    public void setDrawableTop(TextView view, int resourceId) {
        DynamicCodingApplier.setDrawableTop(view, resourceId);
    }

    public void setDrawableRight(TextView view, int resourceId) {
        DynamicCodingApplier.setDrawableEnd(view, resourceId);
    }

    public void setDrawableEnd(TextView view, int resourceId) {
        DynamicCodingApplier.setDrawableEnd(view, resourceId);
    }

    public void setDrawableBottom(TextView view, int resourceId) {
        DynamicCodingApplier.setDrawableBottom(view, resourceId);
    }

    public void setAbsSeekBarThumb(AbsSeekBar view, int resourceId) {
        DynamicCodingApplier.setAbsSeekBarThumb(view, resourceId);
    }

    public void setProgressDrawable(ProgressBar view, int resourceId) {
        DynamicCodingApplier.setProgressDrawable(view, resourceId);
    }

    public void setProgressIndeterminateDrawable(ProgressBar view, int resourceId) {
        DynamicCodingApplier.setProgressIndeterminateDrawable(view, resourceId);
    }

    public void setCompoundDrawables(TextView view, int leftResourceId, int topResourceId, int rightResourceId, int bottomResourceId) {
        DynamicCodingApplier.setCompoundDrawables(view, leftResourceId, topResourceId, rightResourceId, bottomResourceId);
    }

    public void setCompoundDrawablesWithIntrinsicBounds(TextView view, int leftResourceId, int topResourceId, int rightResourceId, int bottomResourceId) {
        DynamicCodingApplier.setCompoundDrawablesWithIntrinsicBounds(view, leftResourceId, topResourceId, rightResourceId, bottomResourceId);
    }

    public void setCompoundDrawablesRelativeWithIntrinsicBounds(TextView view, int leftResourceId, int topResourceId, int rightResourceId, int bottomResourceId) {
        DynamicCodingApplier.setCompoundDrawablesRelativeWithIntrinsicBounds(view, leftResourceId, topResourceId, rightResourceId, bottomResourceId);
    }

    public void setScrollbarThumbVertical(View view, int resourceId) {
        DynamicCodingApplier.setScrollbarThumbVertical(view, resourceId);
    }

    public void setScrollbarThumbHorizontal(View view, int resourceId) {
        DynamicCodingApplier.setScrollbarThumbHorizontal(view, resourceId);
    }

    public void setScrollbarTrackHorizontal(View view, int resourceId) {
        DynamicCodingApplier.setScrollbarTrackHorizontal(view, resourceId);
    }

    public void setScrollbarTrackVertical(View view, int resourceId) {
        DynamicCodingApplier.setScrollbarTrackVertical(view, resourceId);
    }

    /**
     * 对应 setButtonIcon、setButtonDrawable
     */
    public void setButton(CompoundButton view, int resourceId) {
        DynamicCodingApplier.setButtonDrawable(view, resourceId);
    }

    /**
     * 对应 setThumbDrawable、setThumbIcon、setThumbResource
     */
    public void setThumb(@SuppressLint("UseSwitchCompatOrMaterialCode") Switch view, int resourceId) {
        DynamicCodingApplier.setSwitchThumb(view, resourceId);
    }

    /**
     * 对应 setTrackDrawable、setTrackIcon、setTrackResource
     */
    public void setTrack(@SuppressLint("UseSwitchCompatOrMaterialCode") Switch view, int resourceId) {
        DynamicCodingApplier.setSwitchTrack(view, resourceId);
    }

    /**
     * 对应 setHintTextColor
     */
    public void setHintTextColor(TextView view, int resourceId) {
        DynamicCodingApplier.setHintTextColor(view, resourceId);
    }

    public void setTextColorHighlight(TextView view, int resourceId) {
        DynamicCodingApplier.setTextColorHighlight(view, resourceId);
    }

    public void setTextColorLink(TextView view, int resourceId) {
        DynamicCodingApplier.setTextColorLink(view, resourceId);
    }

    public void setDivider(ListView listView, int resourceId) {
        DynamicCodingApplier.setDivider(listView, resourceId);
    }
}
