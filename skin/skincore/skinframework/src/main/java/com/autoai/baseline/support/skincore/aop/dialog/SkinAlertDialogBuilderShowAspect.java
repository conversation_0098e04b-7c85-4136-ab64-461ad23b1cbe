package com.autoai.baseline.support.skincore.aop.dialog;

import android.app.AlertDialog;

import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.viewholders.WindowHolder;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinAlertDialogBuilderShowAspect {

    public static SkinAlertDialogBuilderShowAspect aspectOf() {
        return new SkinAlertDialogBuilderShowAspect();
    }

    @Pointcut("call(* android.app.AlertDialog.Builder+.show())")
    public void alertDialogBuilderShowPointcut() {
    }

    @Around("alertDialogBuilderShowPointcut()")
    public Object aroundAlertDialogBuilderShow(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = joinPoint.proceed();
        //
        Object target = joinPoint.getTarget();
        if (target instanceof AlertDialog) {
            AlertDialog dialog = (AlertDialog) target;
            SkinLogger.d("SkinAlertDialogBuilderShowAspect dialogList.add(" + dialog + "): ");
            WindowHolder.addDialog(dialog);
        }
        //
        return result;
    }
}
