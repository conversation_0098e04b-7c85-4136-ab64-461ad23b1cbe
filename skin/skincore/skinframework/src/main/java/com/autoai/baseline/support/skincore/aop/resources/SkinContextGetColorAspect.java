package com.autoai.baseline.support.skincore.aop.resources;

import static com.autoai.baseline.support.skincore.SkinConfigs.ID_NULL;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.res.ResManager;
import com.autoai.baseline.support.skincore.SkinLogger;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinContextGetColorAspect {

    public static SkinContextGetColorAspect aspectOf() {
        return new SkinContextGetColorAspect();
    }

    @Pointcut("call(* android.content.Context+.getColor(..)) " + SkinConfigs.AOP_WITHOUT)
    public void getColorPointcut() {
    }

    @Around("getColorPointcut()")
    public Object aroundGetColor(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] params = joinPoint.getArgs();
        int resId = ID_NULL;
        if (params.length > 0) {
            Object param = params[0];
            if (param instanceof Integer) {
                resId = (int) param;
            }
        }
        Object result = joinPoint.proceed();
        SkinLogger.d("AOP Context+.getColor resId = " + resId);
        if (resId != ID_NULL && result != null) {
            ResManager.RES_MAP.put(ResManager.COLOR_KEY_HEAD + result, resId);
        }
        return result;
    }
}
