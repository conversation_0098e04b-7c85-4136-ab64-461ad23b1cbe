package com.autoai.baseline.support.skincore.aop.widget;

import android.graphics.drawable.Drawable;
import android.widget.AbsSeekBar;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.res.ResManager;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.applier.DynamicCodingApplier;
import com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinAbsSeekBarDrawableAspect {

    public static SkinAbsSeekBarDrawableAspect aspectOf() {
        return new SkinAbsSeekBarDrawableAspect();
    }

    /**
     * 任何一个包下面的任何一个类下面的任何一个带有SkinActivityInit的方法，构成了这个切面
     */
    @Pointcut("call(* android.widget.AbsSeekBar+.setThumb(..)) " + SkinConfigs.AOP_WITHOUT)
    public void progressAbsSeekBarThumb() {
    }

    /**
     * 拦截方法
     */
    @Around("progressAbsSeekBarThumb()")
    public Object aroundAbsSeekBarThumb(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        if (target instanceof AbsSeekBar) {
            AbsSeekBar absSeekBar = (AbsSeekBar) target;
            Object[] params = joinPoint.getArgs();
            Object param = params[0];
            if (param instanceof Drawable) {
                String key1 = param.hashCode() + "";
                Integer resId = ResManager.RES_MAP.get(key1);
                SkinLogger.d("AOP AbsSeekBar+.setThumb resId = " + resId);
                if (resId != null && resId != SkinConfigs.ID_NULL) {
                    DynamicCodingApplier.setAbsSeekBarThumb(absSeekBar, resId);
                    ResManager.RES_MAP.remove(key1);
                    return null;
                } else {
                    SkinAttributesUtils.removeViewAttribute(absSeekBar, SkinAttributesUtils.ATTRIBUTE_SWITCH_THUMB);
                }
            } else {
                SkinAttributesUtils.removeViewAttribute(absSeekBar, SkinAttributesUtils.ATTRIBUTE_SWITCH_THUMB);
            }
        }
        //执行拦截方法
        return joinPoint.proceed();
    }
}
