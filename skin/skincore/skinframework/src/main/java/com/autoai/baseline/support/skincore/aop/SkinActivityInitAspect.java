package com.autoai.baseline.support.skincore.aop;

import android.app.Activity;

import com.autoai.baseline.support.autoinflater.AutoInflaterManager;
import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 所有Activity 的 onCreate 拦截
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinActivityInitAspect {

    public static SkinActivityInitAspect aspectOf() {
        return new SkinActivityInitAspect();
    }

    @Pointcut("execution(* android.app.Activity+.onCreate(..)) " + SkinConfigs.AOP_WITHOUT)
    public void initActivityPointcut() {
    }

    @Around("initActivityPointcut()")
    public Object aroundOnCreate(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        SkinLogger.d("SkinActivityInitAspect target :" + target);
        if (target instanceof Activity) {
            Activity activity = (Activity) target;
            AutoInflaterManager.getInstance().register(activity);
        }
        //执行拦截方法
        return joinPoint.proceed();
    }
}
