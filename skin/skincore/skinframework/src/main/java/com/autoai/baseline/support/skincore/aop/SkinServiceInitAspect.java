package com.autoai.baseline.support.skincore.aop;

import android.content.Context;

import com.autoai.baseline.support.autoinflater.AutoInflaterManager;
import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinServiceInitAspect {

    public static SkinServiceInitAspect aspectOf() {
        return new SkinServiceInitAspect();
    }

    @Pointcut("execution(* android.app.Service+.onCreate(..)) " + SkinConfigs.AOP_WITHOUT)
    public void initServicePointcut() {
    }

    @Around("initServicePointcut()")
    public Object aroundOnCreate(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        SkinLogger.d("SkinServiceInitAspect target :" + target);
        if (target instanceof Context) {
            Context context = (Context) target;
            AutoInflaterManager.getInstance().register(context);
        }
        //执行拦截方法
        return joinPoint.proceed();
    }
}
