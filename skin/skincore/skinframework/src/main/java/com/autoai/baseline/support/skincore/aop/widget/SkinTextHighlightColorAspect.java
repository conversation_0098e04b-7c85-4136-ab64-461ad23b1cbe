package com.autoai.baseline.support.skincore.aop.widget;

import android.widget.TextView;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.res.ResManager;
import com.autoai.baseline.support.skincore.applier.DynamicCodingApplier;
import com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinTextHighlightColorAspect {

    public static SkinTextHighlightColorAspect aspectOf() {
        return new SkinTextHighlightColorAspect();
    }

    @Pointcut("call(* android.widget.TextView+.setHighlightColor(..)) " + SkinConfigs.AOP_WITHOUT)
    public void setHighlightColorPointcut() {
    }

    @Around("setHighlightColorPointcut()")
    public Object aroundHighlightColor(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        if (target instanceof TextView) {
            TextView textView = (TextView) target;
            Object[] params = joinPoint.getArgs();
            Object param = params[0];
            if (param instanceof Integer) {
                String key1 = ResManager.COLOR_KEY_HEAD + param;
                Integer resId = ResManager.RES_MAP.get(key1);
                if (resId != null && resId != SkinConfigs.ID_NULL) {
                    DynamicCodingApplier.setTextColorHighlight(textView, resId);
                    ResManager.RES_MAP.remove(key1);
                    return null;
                } else {
                    SkinAttributesUtils.removeViewAttribute(textView, SkinAttributesUtils.ATTRIBUTE_TEXT_COLOR_HIGH_LIGHT);
                }
            } else {
                SkinAttributesUtils.removeViewAttribute(textView, SkinAttributesUtils.ATTRIBUTE_TEXT_COLOR_HIGH_LIGHT);
            }
        }
        return joinPoint.proceed();
    }
}
