package com.autoai.baseline.support.skincore.language.aspect.getstring;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * Resources getString
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinResourcesGetStringAspect {

    public static SkinResourcesGetStringAspect aspectOf() {
        return new SkinResourcesGetStringAspect();
    }

    @Pointcut("call(* android.content.res.Resources+.getString(..)) " + SkinConfigs.AOP_WITHOUT)
    public void getString0Pointcut() {
    }

    @Around("getString0Pointcut()")
    public Object aroundGetString0(ProceedingJoinPoint joinPoint) throws Throwable {
        if (SkinConfigs.isSupportTextStr()) {
            SkinLogger.d("文言排查::AOP getString << SkinResourcesGetStringAspect");
            return SkinStringUtil.getStringJoinPoint(joinPoint);
        }else {
            return joinPoint.proceed();
        }
    }
}
