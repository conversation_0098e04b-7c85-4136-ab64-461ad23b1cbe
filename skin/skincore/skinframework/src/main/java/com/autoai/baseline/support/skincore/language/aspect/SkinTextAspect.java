package com.autoai.baseline.support.skincore.language.aspect;

import static com.autoai.baseline.support.skincore.language.StringResManager.FORMAT_STRING_MAP;

import android.widget.TextView;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils;
import com.autoai.baseline.support.skincore.language.DynamicTextApplier;
import com.autoai.baseline.support.skincore.language.StringResManager;
import com.autoai.baseline.support.skincore.language.bean.StringBean;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

import java.util.Arrays;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinTextAspect {

    public static SkinTextAspect aspectOf() {
        return new SkinTextAspect();
    }

    @Pointcut("call(* android.widget.TextView+.setText(..)) " + SkinConfigs.AOP_WITHOUT)
    public void textPointcut() {
    }

    /**
     * TextView.setText(int resId)
     * TextView.setText(CharSequence text)
     * TextView.setText(int resId, BufferType type)
     * TextView.setText(CharSequence text, BufferType type)
     * TextView.setText(@NonNull char[] text, int start, int len)
     */
    @Around("textPointcut()")
    public Object aroundText(ProceedingJoinPoint joinPoint) throws Throwable {
        if (SkinConfigs.isSupportTextStr()) {
            Object target = joinPoint.getTarget();
            Object[] params = joinPoint.getArgs();
            SkinLogger.d("文言排查::AOP TextView+.setText params = " + Arrays.toString(params));
            if (params != null && params.length > 0 && target instanceof TextView) {
                TextView textView = (TextView) target;
                switch (params.length) {
                    case 1: {
                        //
                        Object params1 = params[0];
                        if (params1 instanceof Integer) {
                            int resId = (Integer) params1;
                            DynamicTextApplier.setText(textView, resId);
                            return null;
                        } else if (params1 instanceof CharSequence) {
                            if (((CharSequence) params1).length() == 0) {
                                return joinPoint.proceed();
                            }
                            String key1 = params1.hashCode() + "";
                            Integer resId = StringResManager.RES_MAP.get(key1);
                            SkinLogger.d("文言排查::AOP::RES_MAP.get --> key = " + key1 + " --> resId = " + resId);
                            //
                            if (resId != null && resId != SkinConfigs.ID_NULL) {
                                StringResManager.RES_MAP.remove(key1);
                                SkinLogger.d("文言排查::AOP::RES_MAP.remove --> key = " + key1);
                                DynamicTextApplier.setText(textView, resId);
                                return null;
                            } else {
                                //可能 resId 不是保存在 StringResManager.RES_MAP，而是保存在 StringResManager.FORMAT_STRING_MAP
                                // 那么，还需要再检测以下 StringResManager.FORMAT_STRING_MAP
                                StringBean stringBean = FORMAT_STRING_MAP.get(key1);
                                SkinLogger.d("文言排查::AOP::FORMAT_STRING_MAP.get key = " + key1 + " --> " + stringBean);
                                //
                                if (stringBean != null) {
                                    FORMAT_STRING_MAP.remove(key1);
                                    SkinLogger.d("文言排查::AOP::FORMAT_STRING_MAP.remove --> key = " + key1);
                                    DynamicTextApplier.setText(textView, stringBean);
                                } else {
                                    SkinAttributesUtils.removeViewAttribute(textView, SkinAttributesUtils.ATTRIBUTE_TEXT);
                                }
                            }
                        } else {
                            SkinLogger.w("文言排查::AOP TextView+.setText resId = " + params1);
                        }
                    }
                    break;
                    case 2: {
                        Object params1 = params[0];
                        Object params2 = params[1];
                        //
                        TextView.BufferType bufferType = null;
                        if (params2 instanceof TextView.BufferType) {
                            bufferType = (TextView.BufferType) params2;
                        }
                        if (params1 instanceof Integer) {
                            int resId = (Integer) params1;
                            DynamicTextApplier.setText(textView, resId, bufferType);
                            return null;
                        } else if (params1 instanceof CharSequence) {
                            if (((CharSequence) params1).length() == 0) {
                                return joinPoint.proceed();
                            }
                            String key1 = params1.hashCode() + "";
                            Integer resId = StringResManager.RES_MAP.get(key1);
                            SkinLogger.d("文言排查::AOP TextView+.setText resId = " + resId);
                            if (resId != null && resId != SkinConfigs.ID_NULL) {
                                DynamicTextApplier.setText(textView, resId, bufferType);
                                StringResManager.RES_MAP.remove(key1);
                                SkinLogger.d("文言排查::AOP::RES_MAP.remove  key = " + key1);
                                return null;
                            } else {
                                //可能 resId 不是保存在 StringResManager.RES_MAP，而是保存在 StringResManager.FORMAT_STRING_MAP
                                // 那么，还需要再检测以下 StringResManager.FORMAT_STRING_MAP
                                StringBean stringBean = FORMAT_STRING_MAP.get(key1);
                                if (stringBean != null) {
                                    DynamicTextApplier.setText(textView, stringBean, bufferType);
                                    FORMAT_STRING_MAP.remove(key1);
                                    SkinLogger.d("文言排查::AOP::FORMAT_STRING_MAP.remove  key = " + key1);
                                } else {
                                    SkinAttributesUtils.removeViewAttribute(textView, SkinAttributesUtils.ATTRIBUTE_TEXT);
                                }
                            }
                        } else {
                            SkinLogger.e("文言排查::AOP TextView+.setText resId = " + params1);
                        }
                    }
                    break;
                    case 3: {
                        //todo setText(@NonNull char[] text, int start, int len)
                    }
                    break;
                    default:
                        SkinAttributesUtils.removeViewAttribute(textView, SkinAttributesUtils.ATTRIBUTE_TEXT);
                        break;
                }
            }
        }
        return joinPoint.proceed();
    }
}
