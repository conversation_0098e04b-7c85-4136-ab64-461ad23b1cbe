package com.autoai.baseline.support.skincore.applier.views;

import static com.autoai.baseline.support.skincore.res.ResManager.TYPE_COLOR;

import android.graphics.drawable.AnimationDrawable;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.view.View;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.res.ResBean;
import com.autoai.baseline.support.skincore.res.ResManager;

/**
 * android.view.View 的一般方法 资源应用
 *
 * <AUTHOR>
 */
public class ViewUtil {
    /**
     * 隐藏构造方法，不允许创建实例
     */
    private ViewUtil() {
    }

    /**
     * 设置背景
     */
    public static void setBackground(final View view, int resId) {
        if (resId == SkinConfigs.ID_NULL) {
            return;
        }
        ResBean resBean = ResManager.getInstance().getResBean(resId);
        String resType = resBean.getResType();
        SkinLogger.v("setBackground -> resType = " + resType);
        if (TYPE_COLOR.equals(resType)) {
            int background = ResManager.getInstance().getColor(resId);
            view.setBackgroundColor(background);
        } else {
            // drawable
            Drawable newDrawable = ResManager.getInstance().getDrawable(resId);
            if (newDrawable != null) {
//                Drawable backgroundDrawable = view.getBackground();
//                updateDrawableState(resId, backgroundDrawable, newDrawable);
                Drawable preDrawable = view.getBackground();
                if (preDrawable instanceof AnimationDrawable) {
                    AnimationDrawable animation = (AnimationDrawable) preDrawable;
                    if (animation.isRunning()) {
                        animation.stop();
                        //SkinLogger.v("setBackground -> newDrawable = " + newDrawable + ", resId = " + resId);
                        view.setBackground(newDrawable);
                        if (newDrawable instanceof AnimationDrawable) {
                            ((AnimationDrawable) newDrawable).start();
                        }
                    } else {
                       // SkinLogger.v("setBackground -> newDrawable = " + newDrawable + ", resId = " + resId);
                        view.setBackground(newDrawable);
                    }
                } else {
                    //SkinLogger.v("setBackground -> newDrawable = " + newDrawable + ", resId = " + resId);
                    view.setBackground(newDrawable);
                }
            } else {
               // SkinLogger.e("setBackground -> newDrawable is null !!!!!!!!!!!!!!!");
            }
        }
    }

    /**
     * 设置前景
     */
    public static void setForeground(final View view, int resId) {
        if (resId == SkinConfigs.ID_NULL) {
            return;
        }
        ResBean resBean = ResManager.getInstance().getResBean(resId);
        String resType = resBean.getResType();
        SkinLogger.v("setForeground -> resType = " + resType);
        if (TYPE_COLOR.equals(resType)) {
            int foreground = ResManager.getInstance().getColor(resId);
            ColorDrawable newDrawable = new ColorDrawable(foreground);
            SkinLogger.v("setForeground -> newDrawable = " + newDrawable);
            view.setForeground(newDrawable);
        } else {
            // drawable
            Drawable foreground = ResManager.getInstance().getDrawable(resId);
            SkinLogger.v("setForeground -> newDrawable = " + foreground);
            if (foreground != null) {
//                Drawable oldDrawable = view.getForeground();
//                updateDrawableState(resId, oldDrawable, foreground);
                view.setForeground(foreground);
            }
        }
    }

    public static void setScrollbarThumbVertical(final View view, int resId) {
        if (resId == SkinConfigs.ID_NULL) {
            return;
        }
        final Drawable drawable = ResManager.getInstance().getDrawable(resId);
        if (drawable != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                view.setVerticalScrollbarThumbDrawable(drawable);
            } else {
                try {
                    SdkVerAdapterUtil.setThumbUnderQ(view, "setVerticalThumbDrawable", drawable);
                } catch (Throwable e) {
                    SkinLogger.w("setScrollbarThumbVertical", e);
                }
            }
        }
    }

    public static void setScrollbarThumbHorizontal(final View view, int resId) {
        if (resId == SkinConfigs.ID_NULL) {
            return;
        }
        final Drawable drawable = ResManager.getInstance().getDrawable(resId);
        if (drawable != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                //fix: 如果没有设置过，view的scrollbar没有设置过，未空：
                // java.lang.NullPointerException: Attempt to invoke virtual method 'android.graphics.drawable.Drawable android.widget.ScrollBarDrawable.getVerticalTrackDrawable()' on a null object reference
                //    at android.view.View.getVerticalScrollbarTrackDrawable(View.java:6863)
//                Drawable oldDrawable = view.getHorizontalScrollbarThumbDrawable();
//                updateDrawableState(resId, oldDrawable, drawable);
                view.setHorizontalScrollbarThumbDrawable(drawable);
            } else {
                try {
                    SdkVerAdapterUtil.setThumbUnderQ(view, "setHorizontalThumbDrawable", drawable);
                } catch (Throwable e) {
                    SkinLogger.w("setScrollbarThumbHorizontal", e);
                }
            }
        }
    }


    public static void setScrollbarTrackHorizontal(final View view, int resId) {
        if (resId == SkinConfigs.ID_NULL) {
            return;
        }
        final Drawable drawable = ResManager.getInstance().getDrawable(resId);
        if (drawable != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                //fix: 如果没有设置过，view的scrollbar没有设置过，未空：
                // java.lang.NullPointerException: Attempt to invoke virtual method 'android.graphics.drawable.Drawable android.widget.ScrollBarDrawable.getVerticalTrackDrawable()' on a null object reference
                //    at android.view.View.getVerticalScrollbarTrackDrawable(View.java:6863)
//                Drawable oldDrawable = view.getHorizontalScrollbarTrackDrawable();
//                updateDrawableState(resId, oldDrawable, drawable);
                view.setHorizontalScrollbarTrackDrawable(drawable);
            } else {
                try {
                    SdkVerAdapterUtil.setTrackUnderQ(view, "setHorizontalTrackDrawable", drawable);
                } catch (Throwable e) {
                    SkinLogger.w("setScrollbarTrackHorizontal", e);
                }
            }
        }
    }

    public static void setScrollbarTrackVertical(final View view, int resId) {
        if (resId == SkinConfigs.ID_NULL) {
            return;
        }
        final Drawable drawable = ResManager.getInstance().getDrawable(resId);
        if (drawable != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                //fix: 如果没有设置过，view的scrollbar没有设置过，未空：
                // java.lang.NullPointerException: Attempt to invoke virtual method 'android.graphics.drawable.Drawable android.widget.ScrollBarDrawable.getVerticalTrackDrawable()' on a null object reference
                //    at android.view.View.getVerticalScrollbarTrackDrawable(View.java:6863)
//                Drawable oldDrawable = view.getVerticalScrollbarTrackDrawable();
//                updateDrawableState(resId, oldDrawable, drawable);
                view.setVerticalScrollbarTrackDrawable(drawable);
            } else {
                try {
                    SdkVerAdapterUtil.setTrackUnderQ(view, "setVerticalTrackDrawable", drawable);
                } catch (Throwable e) {
                    SkinLogger.w("setScrollbarTrackVertical", e);
                }
            }
        }
    }
}
