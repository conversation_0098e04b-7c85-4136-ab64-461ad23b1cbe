package com.autoai.baseline.support.skincore.aop;

import android.app.Dialog;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.viewholders.WindowHolder;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinFragmentOnCreateDialogAspect {
    public static SkinFragmentOnCreateDialogAspect aspectOf() {
        return new SkinFragmentOnCreateDialogAspect();
    }

    @Pointcut("execution(* android.app.DialogFragment+.onCreateDialog(..)) " + SkinConfigs.AOP_WITHOUT)
    public void onCreateViewPointcut() {
    }

    @Around("onCreateViewPointcut()")
    public Object aroundOnCreateView(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        SkinLogger.i("Fragment+.onCreateDialog target = " + target);
        Object result = joinPoint.proceed();
        if (result instanceof Dialog) {
            Dialog dialog = (Dialog) result;
            SkinLogger.i("Fragment+.onCreateDialog addFragmentDialog = " + dialog);
            WindowHolder.addDialog(dialog);
        }
        return result;
    }
}
