package com.autoai.baseline.support.skincore.language;

import android.widget.TextView;

import com.autoai.baseline.support.skincore.R;
import com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils;
import com.autoai.baseline.support.skincore.language.bean.StringBean;

public class DynamicTextApplier {
    public static void setText(final TextView view, final int resId) {
        view.setTag(R.id.tag_skin_text_args, null);
        SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_TEXT, resId);
        LanguageViewUtil.setText(view, resId);
    }

    public static void setText(final TextView view, StringBean bean) {
        SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_TEXT, bean);
        LanguageViewUtil.setText(view, bean);
    }

    public static void setText(final TextView view, final int resId, final TextView.BufferType bufferType) {
        view.setTag(R.id.tag_skin_text_args, null);
        SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_TEXT, resId);
        LanguageViewUtil.setText(view, resId, bufferType);
    }

    public static void setText(final TextView view, StringBean bean, final TextView.BufferType bufferType) {
        SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_TEXT, bean);
        LanguageViewUtil.setText(view, bean, bufferType);
    }

    public static void setHint(final TextView view, final int resourceId) {
        view.setTag(R.id.tag_skin_text_args, null);
        SkinAttributesUtils.updateViewAttribute(view, SkinAttributesUtils.ATTRIBUTE_HINT, resourceId);
        LanguageViewUtil.setHint(view, resourceId);
    }

}
