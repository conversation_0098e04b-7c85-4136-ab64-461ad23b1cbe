package com.autoai.baseline.support.skincore.aop.dialog;

import android.app.Dialog;

import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.viewholders.WindowHolder;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinDialogShowAspect {

    public static SkinDialogShowAspect aspectOf() {
        return new SkinDialogShowAspect();
    }

    @Pointcut("call(* android.app.Dialog+.show())")
    public void dialogShowPointcut() {
    }

    @Around("dialogShowPointcut()")
    public Object aroundDialogShow(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = joinPoint.proceed();
        //
        Object target = joinPoint.getTarget();
        if (target instanceof Dialog) {
            Dialog dialog = (Dialog) target;
            SkinLogger.d("SkinDialogShowAspect dialogList.add(" + dialog + "): ");
            WindowHolder.addDialog(dialog);
        }
        //
        return result;
    }
}
