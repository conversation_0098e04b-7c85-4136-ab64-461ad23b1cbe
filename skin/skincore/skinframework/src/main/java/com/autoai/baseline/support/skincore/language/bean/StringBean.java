package com.autoai.baseline.support.skincore.language.bean;

import androidx.annotation.NonNull;

import java.util.Arrays;

public class StringBean {

    private int resId;
    private Object[] args;

    public StringBean(int resId, Object[] args) {
        this.resId = resId;
        this.args = args;
    }

    public int getResId() {
        return resId;
    }

    public void setResId(int resId) {
        this.resId = resId;
    }

    public Object[] getArgs() {
        return args;
    }

    public void setArgs(Object[] args) {
        this.args = args;
    }

    @NonNull
    @Override
    public String toString() {
        return "StringBean{" +
                "resId=" + resId +
                ", args=" + Arrays.toString(args) +
                '}';
    }
}
