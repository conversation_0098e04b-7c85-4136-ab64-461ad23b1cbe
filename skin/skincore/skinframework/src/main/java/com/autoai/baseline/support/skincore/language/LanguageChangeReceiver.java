//package com.autoai.baseline.support.skincore.language;
//
//import android.content.BroadcastReceiver;
//import android.content.Context;
//import android.content.Intent;
//import android.os.LocaleList;
//
//import com.autoai.baseline.support.autoinflater.AutoInflaterThreadPoolUtil;
//import com.autoai.baseline.support.skincore.SkinLogger;
//import com.autoai.baseline.support.skincore.skinpathloader.SkinChanger;
//
//import java.util.Locale;
//
///**
// * 语言切换监听
// */
//public class LanguageChangeReceiver extends BroadcastReceiver {
//
//    @Override
//    public void onReceive(Context context, Intent intent) {
//        //系统语言切换
//        SkinLogger.d("系统语言切换 intent = " + intent + ", Thread = " + Thread.currentThread());
//        // 增加200ms延时去获取系统语言，防止系统语言切换过快，导致实时获取系统语言错误
//        if (Intent.ACTION_LOCALE_CHANGED.equals(intent.getAction())) {
//            Locale locale = Locale.getDefault();
//            SkinLogger.forceI("切换语言: locale = " + locale);
//            SkinLogger.forceI("切换语言: LocaleList.getDefault().get(0) = " + LocaleList.getDefault().get(0));
//            SkinChanger.changeLanguage(locale);
//        }
//    }
//}
