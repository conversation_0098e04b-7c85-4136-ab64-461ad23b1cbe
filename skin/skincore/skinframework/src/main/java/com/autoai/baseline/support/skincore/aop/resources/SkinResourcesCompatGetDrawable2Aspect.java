package com.autoai.baseline.support.skincore.aop.resources;

import static com.autoai.baseline.support.skincore.SkinConfigs.ID_NULL;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.res.ResManager;
import com.autoai.baseline.support.skincore.SkinLogger;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinResourcesCompatGetDrawable2Aspect {

    public static SkinResourcesCompatGetDrawable2Aspect aspectOf() {
        return new SkinResourcesCompatGetDrawable2Aspect();
    }

    @Pointcut("call(* android.support.v4.content.res.ResourcesCompat+.getDrawable(..)) " + SkinConfigs.AOP_WITHOUT)
    public void getDrawablePointcut() {
    }

    @Around("getDrawablePointcut()")
    public Object aroundGetDrawable(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] params = joinPoint.getArgs();
        int resId = ID_NULL;
        if (params.length > 1) {
            Object param = params[1];
            if (param instanceof Integer) {
                resId = (int) param;
            }
        }
        SkinLogger.d("AOP ResourcesCompat+.getDrawable resId = " + resId);
        Object result = joinPoint.proceed();
        if (resId != ID_NULL && result != null) {
            ResManager.RES_MAP.put(result.hashCode() + "", resId);
        }
        return result;
    }
}
