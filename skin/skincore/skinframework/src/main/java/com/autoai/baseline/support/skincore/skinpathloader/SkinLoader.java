package com.autoai.baseline.support.skincore.skinpathloader;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;

import com.autoai.baseline.support.autoinflater.AutoInflaterThreadPoolUtil;
import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.SkinManager;
import com.autoai.baseline.support.skincore.skinpathloader.files.AssetCopyUtil;
import com.autoai.baseline.support.skincore.skinpathloader.files.FileOperateCallback;
import com.autoai.baseline.support.skincore.skinpathloader.files.NetDownloadUtil;

import java.io.File;
import java.util.Locale;

/**
 * 皮肤包加载类
 *
 * <AUTHOR>
 */
public class SkinLoader {
    private static final String SKIN_SP_NAME = "SkinFramework";

    private SkinLoader() {
    }

    /**
     * 从Assets资源加载皮肤包
     *
     * @param assetPath    Assets资源的路径
     * @param skinNickName 皮肤别名，用于区分当前使用的皮肤，以后后续判断当前使用的是哪个皮肤
     * @param skinFileName 皮肤包文件名称
     */
    public static void loadSkinForAssets(final String assetPath,
                                         final String skinNickName,
                                         final String skinFileName,
                                         final Locale locale) {
        if (TextUtils.equals(SkinConfigs.getSkinPath(), skinFileName)) {
            SkinLogger.v("当前显示的皮肤就是需要切换的皮肤，不需要再换肤");
            return;
        }
        final String spKey = (assetPath + "_" + skinFileName).replace(".", "_");
        //
        Context context = SkinManager.getInstance().getApplicationContext();
        SharedPreferences mSharedPreferences = context.getSharedPreferences(SKIN_SP_NAME, Context.MODE_PRIVATE);
        String skinPath = mSharedPreferences.getString(spKey, "");
        //
        if (!TextUtils.isEmpty(skinPath) && new File(skinPath).exists()) {
            SkinLogger.forceI("loadSkinForAssets applySkin value : skinNickName = " + skinNickName + ", skinPath = " + skinPath);
            SkinChanger.changeSkin(skinNickName, skinPath, locale);
        } else {
            AssetCopyUtil.copyAssetsToSdCard(assetPath, new FileOperateCallback() {
                @Override
                public void onSuccess(String skinDirPath) {
                    final String skinPath = skinDirPath + File.separator + skinFileName;
                    //
                    Context context = SkinManager.getInstance().getApplicationContext();
                    SharedPreferences mSharedPreferences = context.getSharedPreferences(SKIN_SP_NAME, Context.MODE_PRIVATE);
                    mSharedPreferences.edit().putString(spKey, skinPath).apply();
                    //
                    SkinLogger.forceI("loadSkinForAssets applySkin AssetCopy callback : skinNickName = " + skinNickName + ", skinPath = " + skinPath);
                    AutoInflaterThreadPoolUtil.getInstance().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (locale == null) {
                                SkinChanger.changeSkin(skinNickName, skinPath, Locale.getDefault());
                            } else {
                                SkinChanger.changeSkin(skinNickName, skinPath, locale);
                            }
                            SkinConfigs.notifyChangeFinish();
                        }
                    });
                }

                @Override
                public void onFailed(String errMsg) {
                    SkinLogger.e("copyAssetsToSdCard: onFailed: " + errMsg);
                }
            });
        }
    }

    /**
     * 加载皮肤包
     *
     * @param skinNickName     皮肤别名，用于区分当前使用的皮肤，以后后续判断当前使用的是哪个皮肤
     * @param httpSkinFilePath 皮肤包地址
     */
    public static void loadSkinForNet(final String skinNickName,
                                      final String httpSkinFilePath,
                                      final Locale locale) {
        final String spKey = (httpSkinFilePath).replace(":", "")
                .replace("//", "_")
                .replace("/", "_")
                .replace(".", "_");
        //
        Context context = SkinManager.getInstance().getApplicationContext();
        SharedPreferences mSharedPreferences = context.getSharedPreferences(SKIN_SP_NAME, Context.MODE_PRIVATE);
        String skinPath = mSharedPreferences.getString(spKey, "");
        //
        if (!TextUtils.isEmpty(skinPath) && new File(skinPath).exists()) {
            SkinLogger.forceI("loadSkinForNet value : skinNickName = " + skinNickName + ", skinPath = " + skinPath);
            SkinChanger.changeSkin(skinNickName, skinPath, locale);
        } else {
            NetDownloadUtil.download(httpSkinFilePath, new FileOperateCallback() {
                @Override
                public void onSuccess(final String skinPath) {
                    //
                    Context context = SkinManager.getInstance().getApplicationContext();
                    SharedPreferences mSharedPreferences = context.getSharedPreferences(SKIN_SP_NAME, Context.MODE_PRIVATE);
                    mSharedPreferences.edit().putString(spKey, skinPath).apply();
                    //
                    SkinLogger.forceI("loadSkinForNet download callback : skinNickName = " + skinNickName + ", skinPath = " + skinPath);
                    AutoInflaterThreadPoolUtil.getInstance().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (locale == null) {
                                SkinChanger.changeSkin(skinNickName, skinPath, Locale.getDefault());
                            } else {
                                SkinChanger.changeSkin(skinNickName, skinPath, locale);
                            }
                            SkinConfigs.notifyChangeFinish();
                        }
                    });
                }

                @Override
                public void onFailed(String errMsg) {
                    SkinLogger.e("loadSkinForNet: onFailed: " + errMsg);
                }
            });
        }
    }

    /**
     * 加载皮肤包
     *
     * @param skinNickName 皮肤别名，用于区分当前使用的皮肤，以后后续判断当前使用的是哪个皮肤
     * @param skinFilePath 皮肤包地址
     * @param locale       地区、语言环境
     */
    public static void loadSkinForSdcard(final String skinNickName, final String skinFilePath, final Locale locale) {
        SkinLogger.forceI("loadSkinForSdcard value : skinNickName = " + skinNickName + ", skinPath = " + skinFilePath + ", locale = " + locale);
        SkinChanger.changeSkin(skinNickName, skinFilePath, locale);
        SkinConfigs.notifyChangeFinish();
    }

    /**
     * 皮肤还原成默认的
     */
    public static void restore() {
        SkinLogger.forceI("restore value :skinNickName = \"(" + SkinConfigs.SKIN_DEFAULT_NAME + ")\", skinPath = \"\"");
        SkinChanger.changeSkin(SkinConfigs.SKIN_DEFAULT_NAME, SkinConfigs.SKIN_DEFAULT_PATH, Locale.getDefault());
        SkinConfigs.notifyChangeFinish();
    }
}
