package com.autoai.baseline.support.skincore.aop.dialog;

import android.app.AlertDialog;

import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.viewholders.WindowHolder;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinAlertDialogShowAspect {

    public static SkinAlertDialogShowAspect aspectOf() {
        return new SkinAlertDialogShowAspect();
    }

    @Pointcut("call(* android.app.AlertDialog+.show())")
    public void alertDialogShowPointcut() {
    }

    @Around("alertDialogShowPointcut()")
    public Object aroundAlertDialogShow(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = joinPoint.proceed();
        //
        Object target = joinPoint.getTarget();
        if (target instanceof AlertDialog) {
            AlertDialog dialog = (AlertDialog) target;
            SkinLogger.d("SkinDialogShowAspect dialogList.add(" + dialog + "): ");
            WindowHolder.addDialog(dialog);
        }
        //
        return result;
    }
}
