package com.autoai.baseline.support.skincore.skinpathloader;

import static com.autoai.baseline.support.skincore.SkinConfigs.SKIN_DEFAULT_NAME;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;

import com.autoai.baseline.support.autoinflater.AutoInflaterManager;
import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.SkinManager;
import com.autoai.baseline.support.skincore.applier.views.StatusBarUtil;
import com.autoai.baseline.support.skincore.res.ResManager;
import com.autoai.baseline.support.skincore.viewholders.WindowHolder;

import java.util.List;
import java.util.Locale;

/**
 * UI 切换
 *
 * <AUTHOR>
 */
public class SkinChanger {


    private SkinChanger() {
    }

    /**
     * 加载新的皮肤包
     *
     * @param skinNickName 皮肤别名，用于区分当前使用的皮肤，以后后续判断当前使用的是哪个皮肤
     * @param path         皮肤包路径
     * @param locale       地区、语言环境
     */
    public static void changeSkin(final String skinNickName, final String path, final Locale locale) {
        if (TextUtils.equals(path, SkinConfigs.getSkinPath())) {
            SkinLogger.v("当前显示的皮肤就是需要切换的皮肤，不需要再换肤");
            return;
        }
        if (TextUtils.isEmpty(skinNickName)) {
            SkinConfigs.skinNickName = SKIN_DEFAULT_NAME;
        } else {
            SkinConfigs.skinNickName = skinNickName;
        }
        SkinConfigs.skinPath = path;
        //
        ResManager.getInstance().resetResource(path, locale);
//        SkinConfigs.mChangeSkin = true;
        changeUi();
    }

    /**
     * 变更语言
     */
    public static void changeLanguage(Locale locale) {
        Locale currentLocale = ResManager.getInstance().getLocale();
        //相同语言切换一次就好了
        if (currentLocale != null && locale != null) {
            if (TextUtils.equals(currentLocale.getLanguage(), locale.getLanguage())
                    && TextUtils.equals(currentLocale.getCountry(), locale.getCountry())) {
                SkinLogger.w("currentLocale = " + currentLocale + ", locale = " + locale);
                return;
            }
        }
        ResManager.getInstance().resetResource(SkinConfigs.getSkinPath(), locale);
//        SkinConfigs.mChangeSkin = true;
        changeUi();
    }

    /**
     * 应用皮肤包
     */
    public static void changeDayNight() {
//        SkinConfigs.mChangeSkin = true;
        changeUi();
    }

    public static void changeUi() {
        //是否需要切换皮肤
//        boolean canDo = (SkinConfigs.isIgnoreForeground() || SkinActivityLifecycleCallbacks.isForeground()) && SkinConfigs.mChangeSkin;
//        SkinLogger.d("canDo = " + canDo + " ---> isIgnoreForeground = " + SkinConfigs.isIgnoreForeground()
//                + ", isForeground = " + SkinActivityLifecycleCallbacks.isForeground()
//                + ", mChangeSkin = " + SkinConfigs.mChangeSkin);

//        boolean canDo = SkinConfigs.mChangeSkin;
//        SkinLogger.d("canDo = " + canDo + " --->  isForeground = " + SkinActivityLifecycleCallbacks.isForeground()
//                + ", mChangeSkin = " + SkinConfigs.mChangeSkin);
        //
//        if (canDo) {
//            SkinConfigs.mChangeSkin = false;
//            if (SkinActivityLifecycleCallbacks.isForeground()) {
        doChange();
//            } else {
//                //非前台应用延迟1s，避免所有应用一起更换消耗资源
//                //但是这个会导致仪表盘、语音引用出现延迟
//                ThreadPoolUtil.getInstance().getMainHandler().postDelayed(new Runnable() {
//                    @Override
//                    public void run() {
//                        doChange();
//                    }
//                }, 1000);
//            }
//        }
    }

    private static void doChange() {
        SkinLogger.forceI("update applySkin ------> 换肤-do real Thread = " + Thread.currentThread());
        Context context = SkinManager.getInstance().getApplicationContext();
        if (context instanceof Activity) {
            //状态栏和导航栏 换肤处理
            StatusBarUtil.updateStatusBar(((Activity) context));
        }
        SkinLogger.i("----------------------- applyUI Start -----------------------");
        if (SkinLogger.isShowDebuggingLog()) {
            List<String> list = AutoInflaterManager.getInstance().getRegistedList();
            for (String item : list) {
                SkinLogger.w("registed Context --> " + item);
            }
        }
        //处理AutoSize的问题
        SkinConfigs.notifyChangeBefore();
        //更换皮肤
        WindowHolder.applyUi();
        SkinLogger.i("----------------------- notifyUiChange Start -----------------------");
        //换肤回调
        SkinConfigs.notifyUiChange();
        SkinLogger.forceI("----------------------- applySkin End -----------------------");
    }
}
