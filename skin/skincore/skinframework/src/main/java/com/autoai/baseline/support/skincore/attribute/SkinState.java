package com.autoai.baseline.support.skincore.attribute;

import android.view.View;

import androidx.annotation.NonNull;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.daynight.DatNightMode;
import com.autoai.baseline.support.skincore.daynight.DayNightUtil;

import java.util.HashMap;
import java.util.Locale;
import java.util.Set;

public class SkinState extends HashMap<String, Integer> {
    private DatNightMode dayNightMode;
    private String skinNickName;
    private Locale locale;
    private Set<View> viewSet;
    private boolean isSkinEnable = true;

    public static SkinState getNewInstance() {
        return new SkinState(SkinConfigs.getSkinNickName(), DayNightUtil.getMode());
    }

    private SkinState(String skinNickName, DatNightMode dayNightMode) {
        this.skinNickName = skinNickName;
        this.dayNightMode = dayNightMode;
    }

    public Locale getLocale() {
        return locale;
    }

    public void setLocale(Locale locale) {
        this.locale = locale;
    }

    public DatNightMode getDayNightMode() {
        return dayNightMode;
    }

    public void setDayNightMode(DatNightMode dayNightMode) {
        this.dayNightMode = dayNightMode;
    }

    public String getSkinNickName() {
        return skinNickName;
    }

    public void setSkinNickName(String skinNickName) {
        this.skinNickName = skinNickName;
    }

    public Set<View> getViewSet() {
        return viewSet;
    }

    public void setViewSet(Set<View> viewSet) {
        this.viewSet = viewSet;
    }

    public boolean isSkinEnable() {
        return isSkinEnable;
    }

    public void setSkinEnable(boolean skinEnable) {
        isSkinEnable = skinEnable;
    }

    @NonNull
    @Override
    public String toString() {
        return "SkinState{ " +
                "isSkinEnable = " + isSkinEnable
                + ", dayNightMode = " + dayNightMode
                + ", skinNickName = " + skinNickName
                + ", map = " + super.toString()
                + "}";
    }
}
