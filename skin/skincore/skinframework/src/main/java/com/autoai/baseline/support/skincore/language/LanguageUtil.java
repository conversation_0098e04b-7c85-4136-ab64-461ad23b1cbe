//package com.autoai.baseline.support.skincore.language;
//
//import android.content.Context;
//import android.content.res.Configuration;
//import android.content.res.Resources;
//import android.os.Build;
//import android.os.LocaleList;
//import android.util.DisplayMetrics;
//
//
//import com.autoai.baseline.support.skincore.SkinLogger;
//
//import java.util.Locale;
//
///**
// * <AUTHOR>
// * @date 2017/5/17
// */
//
//public class LanguageUtil {
//
//    private static LanguageUtil instance;
//
//    /**
//     * 英文
//     */
//    public static final String EN = "en";
//
//    /**
//     * 中文
//     */
//    private static final String ZH = "zh";
//
//    /**
//     * 粤语
//     */
//    private static final String ZH_YUE = "yue";
//
//    /**
//     * 简体中文
//     */
//    public static final String ZH_CN = "zh_CN";
//
//    /**
//     * 香港繁体
//     */
//    private static final String ZH_HK = "zh_HK_#Hant";
//    /**
//     * 澳门繁体
//     */
//    private static final String ZH_MO = "zh_MO_#Hant";
//    /**
//     * 台湾繁体
//     */
//    private static final String ZH_TW = "zh_TW_#Hant";
//
//    private boolean languageChange;
//
//    private boolean tempLanguage;
//
//    public enum LanguageType {
//        //中文/简体中文
//        CHS,
//        //繁体中文
//        CHT,
//        //英文
//        ENU
//    }
//
//    public static LanguageUtil getInstance() {
//        if (instance == null) {
//            instance = new LanguageUtil();
//        }
//        return instance;
//    }
//
//    public Context setLocal(Context context) {
//        return updateResources(context, getSetLanguageLocale());
//    }
//
//    /**
//     * 设置语言类型
//     */
//    public void setApplicationLanguage(Context context) {
//        Resources resources = context.getApplicationContext().getResources();
//        DisplayMetrics dm = resources.getDisplayMetrics();
//        Configuration config = resources.getConfiguration();
//        Locale locale = getSetLanguageLocale();
//
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
//            config.setLocale(locale);
//            context.createConfigurationContext(config);
//        } else {
//            config.locale = locale;
//            //todo 导致页面布局变大
//            resources.updateConfiguration(config, dm);
//        }
//    }
//
//
//    /**
//     * 比如：横竖屏切换
//     *
//     * @param context
//     */
//    public void onConfigurationChanged(Context context) {
//        setLocal(context);
//        setApplicationLanguage(context);
//    }
//
//
//    /**
//     * 获取选择的语言
//     *
//     * @return
//     */
//    private Locale getSetLanguageLocale() {
////        int value = SettingsUtil.getInstance().getConfigsValue(SettingsType.LANGUAGE_TYPE);
////        switch (value) {
////            case 0:
////                return getSystemLocal();
////            case 1:
////                return Locale.CHINA;
////            case 2:
////                return Locale.TAIWAN;
////            case 3:
////                return Locale.ENGLISH;
////            default:
////                return Locale.ENGLISH;
////        }
//        return getSystemLocal();
//    }
//
//    /**
//     * 更新语言设置
//     *
//     * @param context
//     * @param locale
//     * @return
//     */
//    private Context updateResources(Context context, Locale locale) {
//        Locale.setDefault(locale);
//
//        Resources res = context.getResources();
//        Configuration config = new Configuration(res.getConfiguration());
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
//            config.setLocale(locale);
//            context = context.createConfigurationContext(config);
//        } else {
//            config.locale = locale;
//            res.updateConfiguration(config, res.getDisplayMetrics());
//        }
//        return context;
//    }
//
//    /**
//     * 获取系统语言
//     *
//     * @param newConfig
//     * @return
//     */
//    public Locale getSystemLocal(Configuration newConfig) {
//        Locale locale;
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
//            locale = newConfig.getLocales().get(0);
//        } else {
//            locale = newConfig.locale;
//        }
//        return locale;
//    }
//
//    /**
//     * 获取系统语言
//     *
//     * @return
//     */
//    public Locale getSystemLocal() {
//        Locale locale;
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
//            locale = LocaleList.getDefault().get(0);
//        } else {
//            locale = Locale.getDefault();
//        }
//        return locale;
//    }
//
//    public boolean isZh() {
//        Locale locale = getSystemLocal();
//        String language = locale.getLanguage();
//        SkinLogger.d( "isZh: "+language);
//        if (language.endsWith(ZH)||language.endsWith(ZH_YUE)) {
//            return true;
//        } else {
//            return false;
//        }
//    }
//
//    /**
//     * 获取系统语言设置：目前只支持中文、繁体中文、英文
//     *
//     * @return
//     */
//    public LanguageType getLanguageType() {
//        Locale locale = getSystemLocal();
//        String language = locale.getLanguage();
//        if (language.equals(ZH)||language.equals(ZH_YUE)) {
//            switch (locale.toString()) {
//                case ZH_HK:
//                case ZH_MO:
//                case ZH_TW:
//                    return LanguageType.CHT;
//                case ZH:
//                case ZH_CN:
//                case ZH_YUE:
//                default:
//                    return LanguageType.CHS;
//            }
//        } else if (language.equals(EN)) {
//            return LanguageType.ENU;
//        } else {
//            return LanguageType.CHS;
//        }
//    }
//
//    /**
//     * 系统语言最后是否改变
//     *
//     * @return
//     */
//    public boolean isChange() {
//        return tempLanguage != isZh();
//    }
//
//    public void setTempLanguage(boolean tempLanguage) {
//        this.tempLanguage = tempLanguage;
//    }
//}
