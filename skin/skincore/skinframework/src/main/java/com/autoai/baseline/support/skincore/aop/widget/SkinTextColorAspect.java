package com.autoai.baseline.support.skincore.aop.widget;

import android.widget.TextView;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.res.ResManager;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.applier.DynamicCodingApplier;
import com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinTextColorAspect {

    public static SkinTextColorAspect aspectOf() {
        return new SkinTextColorAspect();
    }

    @Pointcut("call(* android.widget.TextView+.setTextColor(..)) " + SkinConfigs.AOP_WITHOUT)
    public void textColorPointcut() {
    }

    @Around("textColorPointcut()")
    public Object aroundTextColor(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        if (target instanceof TextView) {
            TextView textView = (TextView) target;
            Object[] params = joinPoint.getArgs();
            Object param = params[0];
            if (param instanceof Integer) {
                String key1 = ResManager.COLOR_KEY_HEAD + param;
                Integer resId = ResManager.RES_MAP.get(key1);
                SkinLogger.d("AOP TextView+.setTextColor resId = " + resId + ", view = " + target);
                if (resId != null && resId != SkinConfigs.ID_NULL) {
                    DynamicCodingApplier.setTextColor(textView, resId);
                    ResManager.RES_MAP.remove(key1);
                    return null;
                } else {
                    SkinAttributesUtils.removeViewAttribute(textView, SkinAttributesUtils.ATTRIBUTE_TEXT_COLOR);
                }
            } else {
                SkinAttributesUtils.removeViewAttribute(textView, SkinAttributesUtils.ATTRIBUTE_TEXT_COLOR);
            }
        }
        return joinPoint.proceed();
    }
}
