//package com.autoai.baseline.support.skincore.aop.widget;
//
//import android.view.View;
//
//import com.autoai.baseline.support.skincore.SkinConfigs;
//import com.autoai.baseline.support.skincore.SkinManager;
//
//import org.aspectj.lang.ProceedingJoinPoint;
//import org.aspectj.lang.annotation.Around;
//import org.aspectj.lang.annotation.Aspect;
//import org.aspectj.lang.annotation.Pointcut;
//
///**
// * 切面定义
// *
// * <AUTHOR>
// */
//@SuppressWarnings("unused")
//@Aspect
//public class SkinViewVisibilityAspect {
//    public static SkinViewVisibilityAspect aspectOf() {
//        return new SkinViewVisibilityAspect();
//    }
//
//    @Pointcut("execution(* android.view.View+.setVisibility(..)) " + SkinConfigs.AOP_WITHOUT)
//    public void setVisibilityPointcut() {
//    }
//
//    @Around("setVisibilityPointcut()")
//    public Object aroundViewVisibility(ProceedingJoinPoint joinPoint) throws Throwable {
//        Object target = joinPoint.getTarget();
//        if (target instanceof View) {
//            View view = (View) target;
//            Object[] args = joinPoint.getArgs();
//            if (args != null && args.length > 0) {
//                int visibility = (int) args[0];
//                if (visibility == View.VISIBLE) {
//                    SkinManager.getInstance().applyView(view);
//                }
//            }
//        }
//
//        //执行拦截方法
//        return joinPoint.proceed();
//    }
//}
