package com.autoai.baseline.support.skincore.aop.widget;

import android.graphics.drawable.Drawable;
import android.widget.ListView;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.res.ResManager;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.applier.DynamicCodingApplier;
import com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinListViewDividerAspect {

    public static SkinListViewDividerAspect aspectOf() {
        return new SkinListViewDividerAspect();
    }

    /**
     * 任何一个包下面的任何一个类下面的任何一个带有SkinActivityInit的方法，构成了这个切面
     */
    @Pointcut("call(* android.widget.ListView+.setDivider(..)) " + SkinConfigs.AOP_WITHOUT)
    public void setDividerPointcut() {
    }

    /**
     * 拦截方法
     */
    @Around("setDividerPointcut()")
    public Object aroundDivider(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        if (target instanceof ListView) {
            ListView listView = (ListView) target;
            Object[] params = joinPoint.getArgs();
            Object param = params[0];
            if (param instanceof Integer) {
                int resId = (int) param;
                SkinLogger.d("AOP ListView+.setDivider resId = " + resId);
                if (resId == SkinConfigs.ID_NULL) {
                    SkinAttributesUtils.removeViewAttribute(listView, SkinAttributesUtils.ATTRIBUTE_LIST_VIEW_DIVIDER);
                } else {
                    DynamicCodingApplier.setDivider(listView, resId);
                    return null;
                }
            } else if (param instanceof Drawable) {
                String key1 = param.hashCode() + "";
                Integer resId = ResManager.RES_MAP.get(key1);
                SkinLogger.d("AOP ListView+.setDivider resId = " + resId);
                if (resId != null && resId != SkinConfigs.ID_NULL) {
                    DynamicCodingApplier.setDivider(listView, resId);
                    ResManager.RES_MAP.remove(key1);
                    return null;
                } else {
                    SkinAttributesUtils.removeViewAttribute(listView, SkinAttributesUtils.ATTRIBUTE_LIST_VIEW_DIVIDER);
                }
            } else {
                SkinAttributesUtils.removeViewAttribute(listView, SkinAttributesUtils.ATTRIBUTE_LIST_VIEW_DIVIDER);
            }
        }
        //执行拦截方法
        return joinPoint.proceed();
    }
}
