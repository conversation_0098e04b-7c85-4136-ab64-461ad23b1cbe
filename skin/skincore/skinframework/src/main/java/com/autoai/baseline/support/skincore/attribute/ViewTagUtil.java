package com.autoai.baseline.support.skincore.attribute;

import android.util.TypedValue;
import android.view.View;
import android.widget.TextView;

import com.autoai.baseline.support.skincore.R;
import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.res.ResManager;

/**
 * 换肤相关的内容记录到View中
 *
 * <AUTHOR>
 */
public class ViewTagUtil {

    public static SkinState getTagViewSkin(View view) {
        Object object = view.getTag(R.id.tag_view_skin);
        if (object instanceof SkinState) {
            return (SkinState) object;
        }
        return null;
    }

    public static void setTagViewSkin(View view, SkinState skinState) {
        view.setTag(R.id.tag_view_skin, skinState);
    }

    /**
     * 英文文言 文字大小差值
     * <p>
     * 英文语言，自动减指定像素值
     * 项目需求: 我们启用了中英文切换功能，但是需求现在需要切换成英文的时候，整体字号小2像素，想问一下咱们这个框架可以支持么？
     */
    public static void changeTextSize(TextView view) {
        if (SkinConfigs.configEnTextSizeDif) {
            float textSize = -1;
            Object object = view.getTag(R.id.tag_skin_text_size);
            if (object != null) {
                textSize = (Float) object;
            }
            //未保存过textSize，第一需要保存，避免每次
            if (textSize == -1) {
                textSize = view.getTextSize();
                view.setTag(R.id.tag_skin_text_size, textSize);
            }
            //
            if (ResManager.getInstance().isEnglish()) {
                if (SkinConfigs.enTextSizeDifIsUp) {
                    if (textSize >= SkinConfigs.enTextSizeDifMarkSize) {
                        view.setTextSize(TypedValue.COMPLEX_UNIT_PX, (textSize - SkinConfigs.enTextSizeDif));
                    } else {
                        view.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize);
                    }
                } else {
                    if (textSize <= SkinConfigs.enTextSizeDifMarkSize) {
                        view.setTextSize(TypedValue.COMPLEX_UNIT_PX, (textSize - SkinConfigs.enTextSizeDif));
                    } else {
                        view.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize);
                    }
                }
            } else {
                view.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize);
            }
        }
    }
}
