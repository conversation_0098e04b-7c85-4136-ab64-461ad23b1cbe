package com.autoai.baseline.support.skincore.applier.views;

import static com.autoai.baseline.support.skincore.res.ResManager.TYPE_COLOR;

import android.content.res.ColorStateList;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.widget.EditText;
import android.widget.TextView;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.res.ResBean;
import com.autoai.baseline.support.skincore.res.ResManager;

/**
 * TextView 资源应用
 *
 * <AUTHOR>
 */
public class TextViewUtil {
    /**
     * 隐藏构造方法，不允许创建实例
     */
    private TextViewUtil() {
    }

    /**
     * 设置文本颜色
     */
    public static void setTextColor(final TextView view, int resId) {
        if (resId == SkinConfigs.ID_NULL) {
            return;
        }
        //TextView的 字体色值，即使是单纯的color也会转换成 ColorStateList，所以需要统一使用 ColorStateList
        //并且 普通color 和 selector定义的color 的资源类型都是 “color”，无法区分
        final ColorStateList colorStateList = ResManager.getInstance().getColorStateList(view, resId);
        if (colorStateList != null) {
            view.setTextColor(colorStateList);
        }

    }

    /**
     * 设置hint 文本颜色
     */
    public static void setHintTextColor(final TextView view, int resId) {
        if (resId == SkinConfigs.ID_NULL) {
            return;
        }
        final ColorStateList colorStateList = ResManager.getInstance().getColorStateList(view, resId);
        if (colorStateList != null) {
            view.setHintTextColor(colorStateList);
        }
    }

    /**
     * 设置高亮文本颜色
     */
    public static void setTextColorHighlight(final TextView view, int resId) {
        if (resId == SkinConfigs.ID_NULL) {
            return;
        }

        ResBean resBean = ResManager.getInstance().getResBean(resId);
        String resType = resBean.getResType();
        SkinLogger.v("setTextColorHighlight -> resType = " + resType);
        if (TYPE_COLOR.equals(resType)) {
            final int color = ResManager.getInstance().getColor(resId);
            view.setHighlightColor(color);
        }
    }

    /**
     * 设置 光标
     */
    public static void setTextCursorDrawable(EditText editText, int resId) {
        final Drawable drawable = ResManager.getInstance().getDrawable(resId);
        if (null != drawable) {
            boolean isVersionOK = Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q;
            if (SkinLogger.isLoggable()) {
                ResBean resBean = ResManager.getInstance().getResBean(resId);
                SkinLogger.d("setTextCursorDrawable --> isVersionOK = " + isVersionOK + ", editText = " + editText + ", resBean = " + resBean);
            }
            if (isVersionOK) {
                editText.setCursorVisible(false);
                editText.setTextCursorDrawable(drawable);
                editText.setCursorVisible(true);
            }
        }
    }

    /**
     * 设置Link文本颜色
     */
    public static void setTextColorLink(final TextView view, int resId) {
        if (resId == SkinConfigs.ID_NULL) {
            return;
        }
        ColorStateList colorStateList = ResManager.getInstance().getColorStateList(view, resId);
        if (colorStateList != null) {
            view.setLinkTextColor(colorStateList);
        }
    }

    /**
     * 设置 StartDrawable
     */
    public static void setDrawableStart(final TextView view, int resId) {
        if (resId == SkinConfigs.ID_NULL) {
            return;
        }
        final Drawable drawable = ResManager.getInstance().getDrawable(resId);
        if (null != drawable) {
            final Drawable[] drawables = view.getCompoundDrawables();
//            Drawable oldDrawable = drawables[0];
//            updateDrawableState(resId, oldDrawable, drawable);
            //left, top, right, bottom
            view.setCompoundDrawablesWithIntrinsicBounds(drawable, drawables[1], drawables[2], drawables[3]);
        }
    }

    /**
     * 设置 TopDrawable
     */
    public static void setDrawableTop(final TextView view, int resId) {
        if (resId == SkinConfigs.ID_NULL) {
            return;
        }
        final Drawable drawable = ResManager.getInstance().getDrawable(resId);
        if (null != drawable) {
            final Drawable[] drawables = view.getCompoundDrawables();
//            Drawable oldDrawable = drawables[1];
//            updateDrawableState(resId, oldDrawable, drawable);
            //left, top, right, bottom
            view.setCompoundDrawablesWithIntrinsicBounds(drawables[0], drawable, drawables[2], drawables[3]);
        }
    }

    /**
     * 设置 EndDrawable
     */
    public static void setDrawableEnd(final TextView view, int resId) {
        if (resId == SkinConfigs.ID_NULL) {
            return;
        }
        final Drawable drawable = ResManager.getInstance().getDrawable(resId);
        if (null != drawable) {
            final Drawable[] drawables = view.getCompoundDrawables();
//            Drawable oldDrawable = drawables[2];
//            updateDrawableState(resId, oldDrawable, drawable);
            //left, top, right, bottom
            view.setCompoundDrawablesWithIntrinsicBounds(drawables[0], drawables[1], drawable, drawables[3]);
        }
    }

    /**
     * 设置 BottomDrawable
     */
    public static void setDrawableBottom(final TextView view, int resId) {
        if (resId == SkinConfigs.ID_NULL) {
            return;
        }
        final Drawable drawable = ResManager.getInstance().getDrawable(resId);
        if (null != drawable) {
            final Drawable[] drawables = view.getCompoundDrawables();
//            Drawable oldDrawable = drawables[3];
//            updateDrawableState(resId, oldDrawable, drawable);
            //left, top, right, bottom
            view.setCompoundDrawablesWithIntrinsicBounds(drawables[0], drawables[1], drawables[2], drawable);
        }
    }

    public static void setCompoundDrawablesRelativeWithIntrinsicBounds(final TextView view, int leftResourceId, int topResourceId, int rightResourceId, int bottomResourceId) {
        Drawable[] oldDrawables = view.getCompoundDrawables();
        Drawable leftDrawable = oldDrawables[0];
        Drawable topDrawable = oldDrawables[1];
        Drawable rightDrawable = oldDrawables[2];
        Drawable bottomDrawable = oldDrawables[3];
        if (leftResourceId != SkinConfigs.ID_NULL) {
            leftDrawable = ResManager.getInstance().getDrawable(leftResourceId);
//            if (null != leftDrawable) {
//                Drawable oldDrawable = oldDrawables[0];
//                updateDrawableState(leftResourceId, oldDrawable, leftDrawable);
//            }
        }

        if (topResourceId != SkinConfigs.ID_NULL) {
            topDrawable = ResManager.getInstance().getDrawable(topResourceId);
//            if (null != topDrawable) {
//                Drawable oldDrawable = oldDrawables[1];
//                updateDrawableState(topResourceId, oldDrawable, topDrawable);
//            }
        }
        if (rightResourceId != SkinConfigs.ID_NULL) {
            rightDrawable = ResManager.getInstance().getDrawable(rightResourceId);
//            if (null != rightDrawable) {
//                Drawable oldDrawable = oldDrawables[2];
//                updateDrawableState(rightResourceId, oldDrawable, rightDrawable);
//            }
        }
        if (bottomResourceId != SkinConfigs.ID_NULL) {
            bottomDrawable = ResManager.getInstance().getDrawable(bottomResourceId);
//            if (null != bottomDrawable) {
//                Drawable oldDrawable = oldDrawables[3];
//                updateDrawableState(bottomResourceId, oldDrawable, bottomDrawable);
//            }
        }
        //left, top, right, bottom
        view.setCompoundDrawablesRelativeWithIntrinsicBounds(leftDrawable, topDrawable, rightDrawable, bottomDrawable);
    }

    public static void setCompoundDrawablesWithIntrinsicBounds(final TextView view, int leftResourceId, int topResourceId, int rightResourceId, int bottomResourceId) {
        Drawable[] oldDrawables = view.getCompoundDrawables();
        Drawable leftDrawable = oldDrawables[0];
        Drawable topDrawable = oldDrawables[1];
        Drawable rightDrawable = oldDrawables[2];
        Drawable bottomDrawable = oldDrawables[3];
        if (leftResourceId != SkinConfigs.ID_NULL) {
            leftDrawable = ResManager.getInstance().getDrawable(leftResourceId);
//            if (null != leftDrawable) {
//                Drawable oldDrawable = oldDrawables[0];
//                updateDrawableState(leftResourceId, oldDrawable, leftDrawable);
//            }
        }
        if (topResourceId != SkinConfigs.ID_NULL) {
            topDrawable = ResManager.getInstance().getDrawable(topResourceId);
//            if (null != topDrawable) {
//                Drawable oldDrawable = oldDrawables[1];
//                updateDrawableState(topResourceId, oldDrawable, topDrawable);
//            }
        }
        if (rightResourceId != SkinConfigs.ID_NULL) {
            rightDrawable = ResManager.getInstance().getDrawable(rightResourceId);
//            if (null != rightDrawable) {
//                Drawable oldDrawable = oldDrawables[2];
//                updateDrawableState(rightResourceId, oldDrawable, rightDrawable);
//            }
        }
        if (bottomResourceId != SkinConfigs.ID_NULL) {
            bottomDrawable = ResManager.getInstance().getDrawable(bottomResourceId);
//            if (null != bottomDrawable) {
//                Drawable oldDrawable = oldDrawables[3];
//                updateDrawableState(bottomResourceId, oldDrawable, bottomDrawable);
//            }
        }
        //left, top, right, bottom
        view.setCompoundDrawablesWithIntrinsicBounds(leftDrawable, topDrawable, rightDrawable, bottomDrawable);

    }

    public static void setCompoundDrawables(final TextView view, int leftResourceId, int topResourceId, int rightResourceId, int bottomResourceId) {
        Drawable[] oldDrawables = view.getCompoundDrawables();
        Drawable leftDrawable = oldDrawables[0];
        Drawable topDrawable = oldDrawables[1];
        Drawable rightDrawable = oldDrawables[2];
        Drawable bottomDrawable = oldDrawables[3];
        if (leftResourceId != SkinConfigs.ID_NULL) {
            leftDrawable = ResManager.getInstance().getDrawable(leftResourceId);
//            if (null != leftDrawable) {
//                Drawable oldDrawable = oldDrawables[0];
//                updateDrawableState(leftResourceId, oldDrawable, leftDrawable);
//            }
        }

        if (topResourceId != SkinConfigs.ID_NULL) {
            topDrawable = ResManager.getInstance().getDrawable(topResourceId);
//            if (null != topDrawable) {
//                Drawable oldDrawable = oldDrawables[1];
//                updateDrawableState(topResourceId, oldDrawable, topDrawable);
//            }
        }
        if (rightResourceId != SkinConfigs.ID_NULL) {
            rightDrawable = ResManager.getInstance().getDrawable(rightResourceId);
//            if (null != rightDrawable) {
//                Drawable oldDrawable = oldDrawables[2];
//                updateDrawableState(rightResourceId, oldDrawable, rightDrawable);
//            }
        }
        if (bottomResourceId != SkinConfigs.ID_NULL) {
            bottomDrawable = ResManager.getInstance().getDrawable(bottomResourceId);
//            if (null != bottomDrawable) {
//                Drawable oldDrawable = oldDrawables[3];
//                updateDrawableState(bottomResourceId, oldDrawable, bottomDrawable);
//            }
        }
        //left, top, right, bottom
        view.setCompoundDrawables(leftDrawable, topDrawable, rightDrawable, bottomDrawable);

    }
}
