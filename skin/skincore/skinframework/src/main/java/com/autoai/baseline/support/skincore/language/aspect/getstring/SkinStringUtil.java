package com.autoai.baseline.support.skincore.language.aspect.getstring;

import static com.autoai.baseline.support.skincore.SkinConfigs.ID_NULL;

import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.language.StringResManager;

import org.aspectj.lang.ProceedingJoinPoint;

import java.util.Arrays;

/**
 * getString 方法适配
 *
 * <AUTHOR>
 */
public class SkinStringUtil {

    private SkinStringUtil() {
    }

    static Object getStringJoinPoint(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] params = joinPoint.getArgs();
        if (params == null) {
            return joinPoint.proceed();
        }
        int length = params.length;
        //第一个参数必定是 resId;
        int resId = ID_NULL;
        if (length > 0) {
            Object param = params[0];
            if (param instanceof Integer) {
                resId = (int) param;
            }
        }
        //
        Object result;
        if (resId == ID_NULL) {
            result = joinPoint.proceed();
        } else {
            if (length > 1) {
                //有多个参数，使用的 String getString(int resId, Object... formatArgs)
                Object object = params[1];
                Object[] args;
                //
                if (isArray(object)) {
                    args = (Object[]) params[1];
                    SkinLogger.d("文言排查::AOP getString resId = " + resId + ", args = " + Arrays.toString(args));
                } else {
                    //不是数组的情况下，也可能有多个参数
                    args = new Object[length - 1];
                    System.arraycopy(params, 1, args, 0, length - 1);
                }
                result = StringResManager.getInstance().getString(resId,true, args);
            } else {
                //参数只有一个的时候
                result = StringResManager.getInstance().getString(resId,true);
            }
        }
        SkinLogger.d("文言排查::AOP getString resId = " + resId + ", result = " + result);
        return result;
    }


    /**
     * 判断是不是数组
     */
    private static boolean isArray(Object obj) {
        if (obj == null) {
            return false;
        }
        return obj.getClass().isArray();
    }

//    public static Object formatJoinPoint(ProceedingJoinPoint joinPoint, Locale locale, String str, Object paramArgs) throws Throwable {
//        Integer resId = StringResManager.RES_MAP.get(str.hashCode() + "");
//        //
//        Object[] args = null;
//        Object result;
//        //
//        if (resId != null && resId != ID_NULL) {
//            if (isArray(paramArgs)) {
//                args = (Object[]) paramArgs;
//            } else {
//                args = new Object[]{paramArgs};
//            }
////            FORMAT_STRING_MAP.put(resId + "", args);
//            //
//            result = SkinStringUtil.getString(locale, resId, args);
//        } else {
//            result = joinPoint.proceed();
//            StringResManager.RES_MAP.put(result.hashCode() + "", resId);
//        }
//        //
//        if (SkinLogger.isLoggable()) {
//            SkinLogger.d("AOP StringFormat resId = " + resId + ", args = " + Arrays.toString(args) + ", result = " + result);
//        }
//        return result;
//    }
//
//    private static String getString(Locale locale, int resId, Object[] args) {
//        String str = StringResManager.getInstance().getString(resId);
//        if (SkinLogger.isLoggable()) {
//            ResBean resBean = StringResManager.getInstance().getResBean(resId);
//            SkinLogger.d("AOP StringFormat resId = " + resId + "resName = " + resBean.getResName() + ", args = " + Arrays.toString(args) + ", str = " + str);
//        }
//        String result;
//        if (locale == null) {
//            result = String.format(str, args);
//        } else {
//            result = String.format(locale, str, args);
//        }
//        if (SkinLogger.isLoggable()) {
//            ResBean resBean = StringResManager.getInstance().getResBean(resId);
//            SkinLogger.d("AOP StringFormat resId = " + resId + "resName = " + resBean.getResName() + ", args = " + Arrays.toString(args) + ", result = " + result);
//        }
//        return result;
//    }
}
