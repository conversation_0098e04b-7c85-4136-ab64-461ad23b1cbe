package com.autoai.baseline.support.skincore.aop.widget;

import android.view.View;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.SkinManager;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinAddViewAspect {
    public static SkinAddViewAspect aspectOf() {
        return new SkinAddViewAspect();
    }

    @Pointcut("execution(* android.view.ViewGroup+.addView(..)) " + SkinConfigs.AOP_WITHOUT)
    public void addViewPointcut() {
    }

    @Around("addViewPointcut()")
    public Object aroundAddView(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        Object[] args = joinPoint.getArgs();
        View view = (View) args[0];
        SkinLogger.i("chucan ViewGroup+.addView target = " + target + ", addedView = " + view);
        SkinManager.getInstance().applyView(view);
        //执行拦截方法
        return joinPoint.proceed();
    }
}
