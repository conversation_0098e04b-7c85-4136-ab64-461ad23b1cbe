package com.autoai.baseline.support.skincore.aop;

import android.view.View;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.applier.ViewApplier;
import com.autoai.baseline.support.skincore.viewholders.WindowHolder;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

import java.util.HashMap;
import java.util.Map;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinFragmentOnCreateViewAspect {
    public static SkinFragmentOnCreateViewAspect aspectOf() {
        return new SkinFragmentOnCreateViewAspect();
    }

    private static final Map<String, String> map = new HashMap<>();

    @Pointcut("execution(* android.app.Fragment+.onCreateView(..)) " + SkinConfigs.AOP_WITHOUT)
    public void onCreateViewPointcut() {
    }

    @Around("onCreateViewPointcut()")
    public Object aroundOnCreateView(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        SkinLogger.i("chucan Fragment+.onCreateView target = " + target);
        Object result = joinPoint.proceed();
        SkinLogger.i("chucan Fragment+.onCreateView Fragment = " + target);
        if (result == null) {
            SkinLogger.e("chucan Fragment+.onCreateView rootView is null ");
            return null;
        }
        String key = result.hashCode() + "";
        if (result instanceof View) {
            String isFirst = map.get(key);
            SkinLogger.i("chucan Fragment+.onCreateView isFirst = " + isFirst);
            if (isFirst == null) {
                SkinLogger.i("chucan Fragment+.onCreateView first");
                if (target instanceof android.app.DialogFragment) {
                    android.app.DialogFragment dialogFragment = (android.app.DialogFragment) target;
                    SkinLogger.i("chucan Fragment+.onCreateView addFragmentDialog = " + dialogFragment.getDialog());
                    WindowHolder.addDialog(dialogFragment.getDialog());
                }
            } else {
                SkinLogger.i("chucan Fragment+.onCreateView applyRootView");
                ViewApplier.applyRootView((View) result);
            }
        }
        map.put(key, "false");
        return result;
    }
}
