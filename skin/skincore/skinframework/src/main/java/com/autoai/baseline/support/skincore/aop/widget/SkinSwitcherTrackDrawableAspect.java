package com.autoai.baseline.support.skincore.aop.widget;

import android.graphics.drawable.Drawable;
import android.widget.Switch;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.res.ResManager;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.applier.DynamicCodingApplier;
import com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinSwitcherTrackDrawableAspect {

    public static SkinSwitcherTrackDrawableAspect aspectOf() {
        return new SkinSwitcherTrackDrawableAspect();
    }

    /**
     * 任何一个包下面的任何一个类下面的任何一个带有SkinActivityInit的方法，构成了这个切面
     */
    @Pointcut("call(* android.widget.Switch+.setTrackDrawable(..)) " + SkinConfigs.AOP_WITHOUT)
    public void setTrackDrawablePointcut() {
    }

    /**
     * 拦截方法
     */
    @Around("setTrackDrawablePointcut()")
    public Object aroundTrackDrawable(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        if (target instanceof Switch) {
            Switch aSwitch = (Switch) target;
            Object[] params = joinPoint.getArgs();
            Object param = params[0];
            if (param instanceof Integer) {
                int resId = (int) param;
                SkinLogger.d("AOP Switch+.setTrackDrawable resId = " + resId);
                if (resId == SkinConfigs.ID_NULL) {
                    SkinAttributesUtils.removeViewAttribute(aSwitch, SkinAttributesUtils.ATTRIBUTE_SWITCH_TRACK);
                } else {
                    DynamicCodingApplier.setSwitchTrack(aSwitch, resId);
                    return null;
                }
            } else if (param instanceof Drawable) {
                String key1 = param.hashCode() + "";
                Integer resId = ResManager.RES_MAP.get(key1);
                SkinLogger.d("AOP Switch+.setTrackDrawable resId = " + resId);
                if (resId != null && resId != SkinConfigs.ID_NULL) {
                    DynamicCodingApplier.setSwitchTrack(aSwitch, resId);
                    ResManager.RES_MAP.remove(key1);
                    return null;
                } else {
                    SkinAttributesUtils.removeViewAttribute(aSwitch, SkinAttributesUtils.ATTRIBUTE_SWITCH_TRACK);
                }
            } else {
                SkinAttributesUtils.removeViewAttribute(aSwitch, SkinAttributesUtils.ATTRIBUTE_SWITCH_TRACK);
            }
        }
        //执行拦截方法
        return joinPoint.proceed();
    }
}
