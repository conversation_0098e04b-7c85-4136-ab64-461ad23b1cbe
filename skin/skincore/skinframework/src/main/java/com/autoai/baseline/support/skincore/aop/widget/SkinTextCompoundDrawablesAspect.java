package com.autoai.baseline.support.skincore.aop.widget;

import android.graphics.drawable.Drawable;
import android.widget.TextView;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.res.ResManager;
import com.autoai.baseline.support.skincore.applier.DynamicCodingApplier;
import com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 切面定义
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class SkinTextCompoundDrawablesAspect {

    public static SkinTextCompoundDrawablesAspect aspectOf() {
        return new SkinTextCompoundDrawablesAspect();
    }

    @Pointcut("call(* android.widget.TextView+.setCompoundDrawables(..)) " + SkinConfigs.AOP_WITHOUT)
    public void compoundDrawablesPointcut() {
    }

    /**
     * 拦截方法
     */
    @Around("compoundDrawablesPointcut()")
    public Object aroundCompoundDrawables(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        if (target instanceof TextView) {
            Object[] params = joinPoint.getArgs();
            TextView textView = (TextView) target;
            if (params.length > 0) {
                Object params1 = params[0];
                Object params2 = null, params3 = null, params4 = null;
                if (params.length > 1) {
                    params2 = params[1];
                    if (params.length > 2) {
                        params3 = params[2];
                        if (params.length > 3) {
                            params4 = params[3];
                        }
                    }
                }
                int leftResourceId = SkinConfigs.ID_NULL,
                        topResourceId = SkinConfigs.ID_NULL,
                        rightResourceId = SkinConfigs.ID_NULL,
                        bottomResourceId = SkinConfigs.ID_NULL;
                //第一个参数
                if (params1 instanceof Integer) {
                    leftResourceId = (Integer) params1;
                    if (leftResourceId == SkinConfigs.ID_NULL) {
                        SkinAttributesUtils.removeViewAttribute(textView, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_START);
                    }
                } else if (params1 instanceof Drawable) {
                    String key1 = params1.hashCode() + "";
                    Integer resId = ResManager.RES_MAP.get(key1);
                    if (resId != null && resId != SkinConfigs.ID_NULL) {
                        leftResourceId = resId;
                        ResManager.RES_MAP.remove(key1);
                    } else {
                        SkinAttributesUtils.removeViewAttribute(textView, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_START);
                    }
                } else {
                    SkinAttributesUtils.removeViewAttribute(textView, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_START);
                }
                //第二个参数
                if (params2 instanceof Integer) {
                    topResourceId = (Integer) params2;
                    if (topResourceId == SkinConfigs.ID_NULL) {
                        SkinAttributesUtils.removeViewAttribute(textView, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_TOP);
                    }
                } else if (params2 instanceof Drawable) {
                    String key2 = params2.hashCode() + "";
                    Integer resId = ResManager.RES_MAP.get(key2);
                    if (resId != null && resId != SkinConfigs.ID_NULL) {
                        topResourceId = resId;
                        ResManager.RES_MAP.remove(key2);
                    } else {
                        SkinAttributesUtils.removeViewAttribute(textView, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_TOP);
                    }
                } else {
                    SkinAttributesUtils.removeViewAttribute(textView, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_TOP);
                }

                //
                if (params3 instanceof Integer) {
                    rightResourceId = (Integer) params3;
                    if (rightResourceId == SkinConfigs.ID_NULL) {
                        SkinAttributesUtils.removeViewAttribute(textView, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_END);
                    }
                } else if (params3 instanceof Drawable) {
                    String key3 = params3.hashCode() + "";
                    Integer resId = ResManager.RES_MAP.get(key3);
                    if (resId != null && resId != SkinConfigs.ID_NULL) {
                        rightResourceId = resId;
                        ResManager.RES_MAP.remove(key3);
                    } else {
                        SkinAttributesUtils.removeViewAttribute(textView, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_END);
                    }
                } else {
                    SkinAttributesUtils.removeViewAttribute(textView, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_END);
                }

                //
                if (params4 instanceof Integer) {
                    bottomResourceId = (Integer) params4;
                    if (bottomResourceId == SkinConfigs.ID_NULL) {
                        SkinAttributesUtils.removeViewAttribute(textView, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_BOTTOM);
                    }
                } else if (params4 instanceof Drawable) {
                    String key4 = params4.hashCode() + "";
                    Integer resId = ResManager.RES_MAP.get(key4);
                    if (resId != null && resId != SkinConfigs.ID_NULL) {
                        bottomResourceId = resId;
                        ResManager.RES_MAP.remove(key4);
                    } else {
                        SkinAttributesUtils.removeViewAttribute(textView, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_BOTTOM);
                    }
                } else {
                    SkinAttributesUtils.removeViewAttribute(textView, SkinAttributesUtils.ATTRIBUTE_DRAWABLE_BOTTOM);
                }
                if (leftResourceId != SkinConfigs.ID_NULL || topResourceId != SkinConfigs.ID_NULL
                        || rightResourceId != SkinConfigs.ID_NULL || bottomResourceId != SkinConfigs.ID_NULL) {
                    DynamicCodingApplier.setCompoundDrawables(textView, leftResourceId, topResourceId, rightResourceId, bottomResourceId);
                    return null;
                }
            }
        }
        //执行拦截方法
        return joinPoint.proceed();
    }
}
