package com.autoai.baseline.support.skincore.res;

import android.content.res.Configuration;
import android.content.res.Resources;
import android.util.DisplayMetrics;

import androidx.annotation.NonNull;

import java.util.Locale;

/**
 * 语言切换
 *
 * <AUTHOR>
 */
public class LocaleUtil {

//    /**
//     * 变更语言--需要重启界面
//     *
//     * @param loc 区域与语言类型
//     */
//    public static void setByRestart(Locale loc, String action) {
//        Context context = SkinManager.getInstance().getApplicationContext();
//        if (context == null) {
//            return;
//        }
//        changeLocaleConfig(loc);
//        //重启App界面
//        Intent intent = new Intent();
//        intent.setAction(action);
//        SkinLogger.i("setByRestart packageName: " + context.getApplicationInfo().packageName);
//        intent.setPackage(context.getApplicationInfo().packageName);
//        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
//        context.startActivity(intent);
//    }
//
//    /**
//     * 修改当前Context Resource的本地环境
//     */
//    public static void changeLocaleConfig(Locale loc) {
//        Context context = SkinManager.getInstance().getApplicationContext();
//        if (context != null) {
//            changeLocaleConfig(context, loc);
//        }
//    }

    public static Resources changeLocale(Resources resources, @NonNull Locale locale) {
        //local变更，语言处理
        Locale currentLocale = resources.getConfiguration().getLocales().get(0);
        if (!currentLocale.getLanguage().equals(locale.getLanguage())) {
            Configuration configuration = resources.getConfiguration();
            DisplayMetrics displayMetrics = resources.getDisplayMetrics();
            configuration.setLocale(locale);
            //更新配置
            resources.updateConfiguration(configuration, displayMetrics);
        }
        return resources;
    }

//    /**
//     * 根据旧Context 与 Locale 获取新的Context
//     */
//    public static Context getNewLocaleContext(Context sourceContext, Locale loc) {
//        Resources resources = sourceContext.getResources();
//        DisplayMetrics displayMetrics = resources.getDisplayMetrics();
//        Configuration configuration = resources.getConfiguration();
//        // 应用用户选择语言
//        configuration.setLocale(loc);
//        //更新配置
//        sourceContext.getResources().updateConfiguration(configuration, displayMetrics);
//        //
//        return sourceContext.createConfigurationContext(configuration);
//    }

//    /**
//     * 获取当前的语言类型
//     */
//    public static Locale getCurrentLanguage(Context context) {
//        Locale currentLocale;
//        currentLocale = context.getResources().getConfiguration().getLocales().get(0);
//        return currentLocale;
//    }

}
