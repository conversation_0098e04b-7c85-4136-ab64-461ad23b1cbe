package com.autoai.baseline.support.skincore.androidxsupport;

import androidx.appcompat.app.AlertDialog;

import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.viewholders.WindowHolder;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

@Aspect
public class SkinDialogBuilderShowAspect {

    public static SkinDialogBuilderShowAspect aspectOf() {
        return new SkinDialogBuilderShowAspect();
    }

    @Pointcut("call(* androidx.appcompat.app.AlertDialog.Builder+.show())")
    public void dialogShowPointcut() {
    }

    @Around("dialogShowPointcut()")
    public Object aroundGetColor(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = joinPoint.proceed();
        //
        Object target = joinPoint.getTarget();
        if (target instanceof AlertDialog) {
            AlertDialog dialog = (AlertDialog) target;
            SkinLogger.d("SkinDialogBuilderShowAspect dialogList.add(" + dialog + "): ");
            WindowHolder.addDialog(dialog);
        }
        //
        return result;
    }
}
