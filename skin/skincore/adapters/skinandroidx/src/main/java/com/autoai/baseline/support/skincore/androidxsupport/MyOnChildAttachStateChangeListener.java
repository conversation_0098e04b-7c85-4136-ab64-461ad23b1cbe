package com.autoai.baseline.support.skincore.androidxsupport;

import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.autoai.baseline.support.skincore.SkinManager;

public class MyOnChildAttachStateChangeListener implements RecyclerView.OnChildAttachStateChangeListener {

    public static final MyOnChildAttachStateChangeListener HOLDER = new MyOnChildAttachStateChangeListener();
    /**
     * Called when a view is attached to the RecyclerView.
     *
     * @param view The View which is attached to the RecyclerView
     */
    @Override
    public void onChildViewAttachedToWindow(@NonNull View view) {
//        SkinLogger.forceV("RecyclerView+.onChildViewAttachedToWindow --> " + view);
        SkinManager.getInstance().applyView(view);
    }

    /**
     * Called when a view is detached from RecyclerView.
     *
     * @param view The View which is being detached from the RecyclerView
     */
    @Override
    public void onChildViewDetachedFromWindow(@NonNull View view) {
//        SkinLogger.forceV("RecyclerView+.onChildViewDetachedFromWindow --> " + view);
    }
}
