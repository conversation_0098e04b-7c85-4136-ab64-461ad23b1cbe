package com.autoai.baseline.support.skincore.androidxsupport;

import android.content.Context;
import android.util.AttributeSet;
import android.view.InflateException;
import android.view.View;
import android.view.ViewStub;
import android.webkit.WebView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatAutoCompleteTextView;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatCheckBox;
import androidx.appcompat.widget.AppCompatCheckedTextView;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatImageButton;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView;
import androidx.appcompat.widget.AppCompatRadioButton;
import androidx.appcompat.widget.AppCompatRatingBar;
import androidx.appcompat.widget.AppCompatSeekBar;
import androidx.appcompat.widget.AppCompatSpinner;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.appcompat.widget.AppCompatToggleButton;
import androidx.collection.SimpleArrayMap;

import com.autoai.baseline.support.autoinflater.BaseLayoutFactory;

import java.lang.reflect.Constructor;

/**
 * 自定义LayoutInflater.Factory2，用于拦截属性加载皮肤
 *
 * <AUTHOR>
 */
public class SkinAndroidXLayoutFactory extends BaseLayoutFactory {
    //androidx.appcompat.app.AppCompatViewInflater

    @Override
    protected View createViewFromTag(@NonNull String name, @NonNull Context context, @NonNull AttributeSet attrs) throws Exception {
        if (name.contains(SPLIT_POINT)) {
            //包含了 . 自定义控件
            return viewNewInstance(name, context, attrs);
        } else if (VIEW.equals(name)) {
            return new View(context, attrs);
        } else if (VIEW_STUB.equals(name)) {
            return new ViewStub(context, attrs);
        } else if (WEB_VIEW.equals(name)) {
            //WebView
            return new WebView(context, attrs);
        } else {
            //其他 View
            switch (name) {
                case "TextView":
                    return new AppCompatTextView(context, attrs);
                case "ImageView":
                    return new AppCompatImageView(context, attrs);
                case "Button":
                    return new AppCompatButton(context, attrs);
                case "EditText":
                    return new AppCompatEditText(context, attrs);
                case "Spinner":
                    return new AppCompatSpinner(context, attrs);
                case "ImageButton":
                    return new AppCompatImageButton(context, attrs);
                case "CheckBox":
                    return new AppCompatCheckBox(context, attrs);
                case "RadioButton":
                    return new AppCompatRadioButton(context, attrs);
                case "CheckedTextView":
                    return new AppCompatCheckedTextView(context, attrs);
                case "AutoCompleteTextView":
                    return new AppCompatAutoCompleteTextView(context, attrs);
                case "MultiAutoCompleteTextView":
                    return new AppCompatMultiAutoCompleteTextView(context, attrs);
                case "RatingBar":
                    return new AppCompatRatingBar(context, attrs);
                case "SeekBar":
                    return new AppCompatSeekBar(context, attrs);
                case "ToggleButton":
                    return new AppCompatToggleButton(context, attrs);
                default:
                    return viewNewInstance(name, context, attrs);
            }
        }
    }

    private final Object[] mConstructorArgs = new Object[2];
    private static final String[] sClassPrefixList = {"android.widget.", "android.view.", "android.webkit."};

    @Override
    protected View viewNewInstance(String name, Context context, AttributeSet attrs) throws Exception {
        if (name.equals("view")) {
            name = attrs.getAttributeValue(null, "class");
        }

        try {
            mConstructorArgs[0] = context;
            mConstructorArgs[1] = attrs;

            if (-1 == name.indexOf('.')) {
                for (int i = 0; i < sClassPrefixList.length; i++) {
                    final View view = createViewByPrefix(context, name, sClassPrefixList[i]);
                    if (view != null) {
                        return view;
                    }
                }
                return null;
            } else {
                return createViewByPrefix(context, name, null);
            }
        } catch (Exception e) {
            // We do not want to catch these, lets return null and let the actual LayoutInflater
            // try
            return null;
        } finally {
            // Don't retain references on context.
            mConstructorArgs[0] = null;
            mConstructorArgs[1] = null;
        }
    }

    private static final Class<?>[] sConstructorSignature = new Class<?>[]{Context.class, AttributeSet.class};
    private static final SimpleArrayMap<String, Constructor<? extends View>> sConstructorMap = new SimpleArrayMap<>();

    private View createViewByPrefix(Context context, String name, String prefix) throws ClassNotFoundException, InflateException {
        Constructor<? extends View> constructor = sConstructorMap.get(name);
        try {
            if (constructor == null) {
                // Class not found in the cache, see if it's real, and try to add it
                Class<? extends View> clazz = Class.forName(prefix != null ? (prefix + name) : name, false, context.getClassLoader()).asSubclass(View.class);

                constructor = clazz.getConstructor(sConstructorSignature);
                sConstructorMap.put(name, constructor);
            }
            constructor.setAccessible(true);
            return constructor.newInstance(mConstructorArgs);
        } catch (Exception e) {
            // We do not want to catch these, lets return null and let the actual LayoutInflater
            // try
            return null;
        }
    }
}
