package com.autoai.baseline.support.skincore.androidxsupport;

import android.view.View;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.SkinManager;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

@SuppressWarnings("unused")
@Aspect
public class SkinRecyclerViewAddViewAspect {

    public static SkinRecyclerViewAddViewAspect aspectOf() {
        return new SkinRecyclerViewAddViewAspect();
    }

    @Pointcut("execution(* androidx.recyclerview.widget.RecyclerView.addView(..)) " + SkinConfigs.AOP_WITHOUT)
    public void addViewPointcut() {
    }

    @Around("addViewPointcut()")
    public Object aroundAddView(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        if (args.length > 0) {
            Object args1 = args[0];
            SkinLogger.d("SkinRecyclerViewAddViewAspect args1 :" + args1);
            if (args1 instanceof View) {
                View view = (View) args1;
                SkinManager.getInstance().applyView(view);
            }
        }
        return null;
    }
}
