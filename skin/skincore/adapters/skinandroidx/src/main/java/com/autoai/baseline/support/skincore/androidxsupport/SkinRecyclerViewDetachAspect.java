//package com.autoai.baseline.support.skincore.androidxsupport;
//
//import android.view.View;
//
//import androidx.recyclerview.widget.RecyclerView;
//
//import com.autoai.baseline.support.skincore.SkinLogger;
//import com.autoai.baseline.support.skincore.attribute.SkinState;
//import com.autoai.baseline.support.skincore.attribute.ViewTagUtil;
//
//import org.aspectj.lang.ProceedingJoinPoint;
//import org.aspectj.lang.annotation.Around;
//import org.aspectj.lang.annotation.Aspect;
//import org.aspectj.lang.annotation.Pointcut;
//
//import java.util.Set;
//
//@SuppressWarnings("unused")
//@Aspect
//public class SkinRecyclerViewDetachAspect {
//
//    public static SkinRecyclerViewDetachAspect aspectOf() {
//        return new SkinRecyclerViewDetachAspect();
//    }
//
//    @Pointcut("execution(* androidx.recyclerview.widget.RecyclerView+.dispatchChildDetached(..)) ")
//    public void recyclerviewOnViewDetachedFromWindowPointcut() {
//    }
//
//    @Around("recyclerviewOnViewDetachedFromWindowPointcut()")
//    public Object aroundRecyclerviewOnViewDetachedFromWindow(ProceedingJoinPoint joinPoint) throws Throwable {
//        Object target = joinPoint.getTarget();
//        if (target instanceof RecyclerView) {
//            RecyclerView recyclerView = (RecyclerView) target;
//            Object[] argsArray = joinPoint.getArgs();
//            if (argsArray != null && argsArray.length > 0) {
//                Object args = argsArray[0];
//                if (args instanceof View) {
//                    View view = (View) args;
//                    SkinLogger.forceV("tagInnerView--> RecyclerView+.detached --> " + view);
//                    SkinState skinState = ViewTagUtil.getTagViewSkin(recyclerView);
//                    if (skinState != null) {
//                        Set<View> viewSet = skinState.getViewSet();
//                        if (viewSet != null) {
//                            viewSet.remove(view);
//                        }
//                    }
//                }
//            }
//        }
//
//        return joinPoint.proceed();
//    }
//}
