package com.autoai.baseline.support.skincore.androidxsupport;

import android.widget.AbsListView;
import android.widget.ScrollView;

import androidx.recyclerview.widget.RecyclerView;

import com.autoai.baseline.support.skincore.SkinConfigs;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

@Aspect
public class SkinJudgeViewAspect {

    public static SkinJudgeViewAspect aspectOf() {
        return new SkinJudgeViewAspect();
    }

    @Pointcut("execution(* com.autoai.baseline.support.skincore.aop.SkinAopMethod.judgeView(*)) ")
    public void judgeViewPointcut() {
    }

    @Around("judgeViewPointcut()")
    public Object aroundJudgeView(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        return (args[0] instanceof AbsListView) || (args[0] instanceof ScrollView) || (args[0] instanceof RecyclerView);
    }
}
