package com.autoai.baseline.support.skincore.androidxsupport;

import androidx.appcompat.app.AlertDialog;

import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.viewholders.WindowHolder;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

@Aspect
public class SkinDialogDismissAspect {

    public static SkinDialogDismissAspect aspectOf() {
        return new SkinDialogDismissAspect();
    }

    @Pointcut("call(* androidx.appcompat.app.AlertDialog+.dismiss())")
    public void dialogDismissPointcut() {
    }

    @Around("dialogDismissPointcut()")
    public Object aroundGetColor(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = joinPoint.proceed();
        //
        Object target = joinPoint.getTarget();
        if (target instanceof AlertDialog) {
            AlertDialog dialog = (AlertDialog) target;
            SkinLogger.d("SkinDialogDismissAspect dialogList.remove(" + dialog + "): ");
            WindowHolder.removeDialog(dialog);
        }
        //
        return result;
    }
}
