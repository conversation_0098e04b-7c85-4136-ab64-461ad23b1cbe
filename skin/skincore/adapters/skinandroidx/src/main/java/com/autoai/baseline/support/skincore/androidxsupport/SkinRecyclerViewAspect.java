package com.autoai.baseline.support.skincore.androidxsupport;

import androidx.recyclerview.widget.RecyclerView;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinManager;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

@SuppressWarnings("unused")
@Aspect
public class SkinRecyclerViewAspect {

    public static SkinRecyclerViewAspect aspectOf() {
        return new SkinRecyclerViewAspect();
    }

    @Pointcut("execution(* androidx.recyclerview.widget.RecyclerView.Adapter+.onCreateViewHolder(..)) " + SkinConfigs.AOP_WITHOUT)
    public void setAdapterPointcut() {
    }

    @Around("setAdapterPointcut()")
    public Object aroundSetAdapter(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        //
        RecyclerView recyclerView = (RecyclerView) args[0];
        Object  listener = recyclerView.getTag(R.id.tag_skin_recycler_view_listener);
        if (!(listener instanceof MyOnChildAttachStateChangeListener)) {
            recyclerView.addOnChildAttachStateChangeListener(MyOnChildAttachStateChangeListener.HOLDER);
            recyclerView.setTag(R.id.tag_skin_recycler_view_listener,MyOnChildAttachStateChangeListener.HOLDER);
        }
        //
        Object object = joinPoint.proceed();
        if (object instanceof RecyclerView.ViewHolder) {
            RecyclerView.ViewHolder viewHolder = (RecyclerView.ViewHolder) object;
            //fix：TOYOTA25MM-19246【1A】【系统测试】【机型-L】【1M】【AppStore】【Bench】应用商店停止服务后应用按钮和文言颜色不一致
            SkinManager.getInstance().applyView(viewHolder.itemView);
//            SkinManager.getInstance().tagInnerView(recyclerView, viewHolder.itemView);
        }
        return object;
    }
}
