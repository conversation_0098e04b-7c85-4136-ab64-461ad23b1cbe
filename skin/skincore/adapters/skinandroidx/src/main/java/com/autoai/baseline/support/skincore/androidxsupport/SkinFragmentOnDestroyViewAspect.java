package com.autoai.baseline.support.skincore.androidxsupport;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.viewholders.WindowHolder;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

@Aspect
public class SkinFragmentOnDestroyViewAspect {
    public static SkinFragmentOnDestroyViewAspect aspectOf() {
        return new SkinFragmentOnDestroyViewAspect();
    }

    @Pointcut("execution(* androidx.fragment.app.DialogFragment+.onDestroyView(..)) " + SkinConfigs.AOP_WITHOUT)
    public void myPointcut() {
    }

    @Around("myPointcut()")
    public Object aroundOnCreateView(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        SkinLogger.i("chucan Fragment+.onDestroyView target = " + target);
        if (target instanceof androidx.fragment.app.DialogFragment) {
            androidx.fragment.app.DialogFragment fragment = (androidx.fragment.app.DialogFragment) target;
            SkinLogger.i("chucan androidx　Fragment.removeFragmentDialog Dialog = " + fragment.getDialog());
            WindowHolder.removeDialog(fragment.getDialog());
        }
        return joinPoint.proceed();
    }
}
