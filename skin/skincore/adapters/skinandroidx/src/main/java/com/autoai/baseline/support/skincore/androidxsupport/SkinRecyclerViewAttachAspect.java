//package com.autoai.baseline.support.skincore.androidxsupport;
//
//import android.view.View;
//import android.widget.ListView;
//
//import androidx.recyclerview.widget.RecyclerView;
//import androidx.viewpager.widget.ViewPager;
//import androidx.viewpager2.widget.ViewPager2;
//
//import com.autoai.baseline.support.skincore.SkinLogger;
//import com.autoai.baseline.support.skincore.SkinManager;
//import com.autoai.baseline.support.skincore.attribute.SkinState;
//import com.autoai.baseline.support.skincore.attribute.ViewTagUtil;
//
//import org.aspectj.lang.ProceedingJoinPoint;
//import org.aspectj.lang.annotation.Around;
//import org.aspectj.lang.annotation.Aspect;
//import org.aspectj.lang.annotation.Pointcut;
//
//@SuppressWarnings("unused")
//@Aspect
//public class SkinRecyclerViewAttachAspect {
//
//    public static SkinRecyclerViewAttachAspect aspectOf() {
//        return new SkinRecyclerViewAttachAspect();
//    }
//
//    @Pointcut("execution(* androidx.recyclerview.widget.RecyclerView+.dispatchChildAttached(..)) ")
//    public void recyclerviewOnViewAttachedToWindowPointcut() {
//    }
//
//    @Around("recyclerviewOnViewAttachedToWindowPointcut()")
//    public Object aroundRecyclerviewOnViewAttachedToWindow(ProceedingJoinPoint joinPoint) throws Throwable {
//        Object target = joinPoint.getTarget();
//        if (target instanceof RecyclerView) {
//            RecyclerView recyclerView = (RecyclerView) target;
//            Object[] argsArray = joinPoint.getArgs();
//            if (argsArray != null && argsArray.length > 0) {
//                Object args = argsArray[0];
//                if (args instanceof View) {
//                    View view = (View) args;
//                    SkinLogger.forceV("tagInnerView--> RecyclerView+.attached --> " + view);
//                    SkinManager.getInstance().tagInnerView(recyclerView, view);
//                }
//            }
//        }
//        return joinPoint.proceed();
//    }
//}
