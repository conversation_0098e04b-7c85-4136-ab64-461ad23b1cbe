package com.autoai.baseline.support.skincore.androidxsupport;

import android.view.View;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinManager;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

@SuppressWarnings("unused")
@Aspect
public class SkinPagerAdapterAddAspect {
    //androidx.viewpager.widget.PagerAdapter

    public static SkinPagerAdapterAddAspect aspectOf() {
        return new SkinPagerAdapterAddAspect();
    }

    @Pointcut("execution(* androidx.viewpager.widget.PagerAdapter+.instantiateItem(..)) " + SkinConfigs.AOP_WITHOUT)
    public void setAdapterPointcut() {
    }

    @Around("setAdapterPointcut()")
    public Object aroundSetAdapter(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = joinPoint.proceed();
        if (result instanceof View) {
            SkinManager.getInstance().addView((View) result);
        }
        //
        return result;
    }
}
