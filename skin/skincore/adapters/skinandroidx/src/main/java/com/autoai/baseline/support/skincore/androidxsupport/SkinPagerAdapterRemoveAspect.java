package com.autoai.baseline.support.skincore.androidxsupport;

import android.view.View;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinManager;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

@SuppressWarnings("unused")
@Aspect
public class SkinPagerAdapterRemoveAspect {
    //androidx.viewpager.widget.PagerAdapter

    public static SkinPagerAdapterRemoveAspect aspectOf() {
        return new SkinPagerAdapterRemoveAspect();
    }

    @Pointcut("execution(* androidx.viewpager.widget.PagerAdapter+.destroyItem(..)) " + SkinConfigs.AOP_WITHOUT)
    public void setAdapterPointcut() {
    }

    @Around("setAdapterPointcut()")
    public Object aroundSetAdapter(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = joinPoint.proceed();
        Object[] args = joinPoint.getArgs();
        if (args.length >= 3) {
            Object param = args[2];
            if (param instanceof View) {
                SkinManager.getInstance().removeView((View) param);
            }
        }
        //
        return result;
    }
}
