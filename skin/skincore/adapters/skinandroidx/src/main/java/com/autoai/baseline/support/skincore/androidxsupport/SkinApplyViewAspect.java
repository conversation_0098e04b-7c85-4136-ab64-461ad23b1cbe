//package com.autoai.baseline.support.skincore.androidxsupport;
//
//import android.widget.ListView;
//
//import androidx.recyclerview.widget.RecyclerView;
//import androidx.viewpager.widget.ViewPager;
//import androidx.viewpager2.widget.ViewPager2;
//
//import com.autoai.baseline.support.skincore.SkinLogger;
//import com.autoai.baseline.support.skincore.attribute.SkinState;
//import com.autoai.baseline.support.skincore.attribute.ViewTagUtil;
//
//import org.aspectj.lang.ProceedingJoinPoint;
//import org.aspectj.lang.annotation.Around;
//import org.aspectj.lang.annotation.Aspect;
//import org.aspectj.lang.annotation.Pointcut;
//
//import java.util.Map;
//
//@SuppressWarnings("unused")
//@Aspect
//public class SkinApplyViewAspect {
//
//    public static SkinApplyViewAspect aspectOf() {
//        return new SkinApplyViewAspect();
//    }
//
//    @Pointcut("execution(* com.autoai.baseline.support.skincore.aop.SkinAopMethod.applyView(*)) ")
//    public void applyViewPointcut() {
//    }
//
//    @Around("applyViewPointcut()")
//    public Object aroundApplyView(ProceedingJoinPoint joinPoint) throws Throwable {
//        Object[] argsArray = joinPoint.getArgs();
//        if (argsArray != null && argsArray.length > 0) {
//            Object args = argsArray[0];
//            if (args instanceof RecyclerView) {
//                SkinLogger.v("SkinAopMethod.applyView --> RecyclerView");
////                RecyclerView recyclerView = (RecyclerView) args;
////                recyclerView.getAdapter().getItemCount();
////
////                recyclerView.getAdapter().createViewHolder()
//
//
////                RecyclerView.RecycledViewPool recycledViewPool = recyclerView.getRecycledViewPool();
//
////                SkinState skinState = ViewTagUtil.getTagViewSkin(recyclerView);
////                if (skinState != null) {
////                    for (Map.Entry<String, Integer> entry : skinState.entrySet()) {
////                        String key = entry.getKey();
////                        if (key.startsWith("ViewType_")) {
////                            Integer value = entry.getValue();
////                            if (value != null) {
////                              RecyclerView.ViewHolder viewHolder = recycledViewPool.getScrapDataForType(value);
////                            }
////                        }
////                    }
////                }
//            } else if (args instanceof ListView) {
////                ListView listView= (ListView) args;
//                SkinLogger.v("SkinAopMethod.applyView --> ListView");
//            } else if (args instanceof ViewPager) {
////                ViewPager viewPager= (ViewPager) args;
//                SkinLogger.v("SkinAopMethod.applyView --> ViewPager");
//            } else if (args instanceof ViewPager2) {
////                ViewPager2 viewPager2= (ViewPager2) args;
//                SkinLogger.v("SkinAopMethod.applyView --> ViewPager2");
//            }
//        }
//
//        return joinPoint.proceed();
//    }
//}
