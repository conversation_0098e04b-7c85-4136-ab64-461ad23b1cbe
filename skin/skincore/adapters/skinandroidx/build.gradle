plugins {
    id 'com.android.library'
}
apply from: 'maven-publish.gradle'
android {
    compileSdk rootProject.ext.targetSdkVersion
    defaultConfig {
        minSdk rootProject.ext.minSdkVersion
        targetSdk rootProject.ext.targetSdkVersion
        //
        versionCode rootProject.ext.skinandroidxVersionCode
        versionName rootProject.ext.skinandroidxVersionName
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            // 启用代码压缩、优化及混淆
            minifyEnabled false
            // 启用资源压缩，需配合 minifyEnabled=true 使用
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            //Instrumentation代码覆盖率测试报告开关
            testCoverageEnabled false
        }
        debug {
            // 启用代码压缩、优化及混淆
            minifyEnabled false
            // 启用资源压缩，需配合 minifyEnabled=true 使用
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            //Instrumentation代码覆盖率测试报告开关
            testCoverageEnabled false
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    compileOnly project(':skin:autoinflater')
//    releaseCompileOnly "com.autoai.baseline.skincore:autoinflater:${rootProject.ext.autoinflaterVersionName}"

    compileOnly project(':skin:skincore:skinframework')
//    releaseCompileOnly "com.autoai.baseline.skincore:skinframework:${rootProject.ext.skinframeworkVersionName}"

    if (rootProject.hasProperty("aspectjrt")) {
        implementation rootProject.ext.aspectjrt
    }
    compileOnly('androidx.appcompat:appcompat:1.4.0')
    compileOnly('androidx.recyclerview:recyclerview:1.2.1')
    compileOnly('com.google.android.material:material:1.4.0') {
        exclude group: 'androidx.recyclerview', module: 'recyclerview'
    }
    compileOnly('androidx.constraintlayout:constraintlayout:2.1.1')
}