plugins {
    id 'com.android.library'
}
apply from: 'maven-publish.gradle'
android {
    compileSdk rootProject.ext.targetSdkVersion
    defaultConfig {
        minSdk rootProject.ext.minSdkVersion
        targetSdk rootProject.ext.targetSdkVersion
        //
        versionCode rootProject.ext.glideadapterVersionCode
        versionName rootProject.ext.glideadapterVersionName
        //
        resourcePrefix "skin_glideadapter_"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            // 启用代码压缩、优化及混淆
            minifyEnabled false
            // 启用资源压缩，需配合 minifyEnabled=true 使用
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            //Instrumentation代码覆盖率测试报告开关
            testCoverageEnabled false
        }
        debug {
            // 启用代码压缩、优化及混淆
            minifyEnabled false
            // 启用资源压缩，需配合 minifyEnabled=true 使用
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            //Instrumentation代码覆盖率测试报告开关
            testCoverageEnabled false
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    compileOnly project(':skin:skincore:skinframework')
//    releaseCompileOnly "com.autoai.baseline.skincore:skinframework:${rootProject.ext.skinframeworkVersionName}"

    if (rootProject.hasProperty("aspectjrt")) {
        implementation rootProject.ext.aspectjrt
    }
    compileOnly('com.github.bumptech.glide:glide:4.12.0')
}
