package com.autoai.baseline.support.skincore.aop.glideadapter;

import android.annotation.SuppressLint;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.Nullable;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils;
import com.bumptech.glide.RequestBuilder;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.target.Target;
import com.bumptech.glide.request.target.ViewTarget;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

@SuppressWarnings("unused")
@Aspect
public class SkinGlideIntoAspect {

    public static SkinGlideIntoAspect aspectOf() {
//        SkinLogger.d("SkinGlideAspect SkinGlideIntoAspect aspectOf");
        return new SkinGlideIntoAspect();
    }

    @Pointcut("call(* com.bumptech.glide.RequestBuilder+.into(*)) " + SkinConfigs.AOP_WITHOUT)
    public void glideIntoPointcut() {
//        SkinLogger.d("SkinGlideAspect glideIntoPointcut");
    }

    @SuppressLint("CheckResult")
    @Around("glideIntoPointcut()")
    public Object aroundGlideInto(ProceedingJoinPoint joinPoint) throws Throwable {
        ImageView imageView = null;
        Object target = joinPoint.getTarget();
        if (target instanceof RequestBuilder) {
            RequestBuilder<?> requestBuilder = (RequestBuilder<?>) target;
            Object[] args = joinPoint.getArgs();
            if (args.length > 0) {
                Object param = args[0];
                if (param instanceof ImageView) {
//                    SkinLogger.d("SkinGlideAspect 排查glide options.into(imageView)");
                    imageView = (ImageView) param;
                } else if (param instanceof ViewTarget) {
                    ViewTarget<?, ?> paramTarget = (ViewTarget<?, ?>) param;
//                    SkinLogger.d("SkinGlideAspect 排查glide options.into(ViewTarget)");
                    View view = paramTarget.getView();
                    if (view instanceof ImageView) {
                        imageView = (ImageView) view;
                    }
                }
            }
            ImageView finalImageView = imageView;
            requestBuilder.addListener(new RequestListener() {
                @Override
                public boolean onLoadFailed(@Nullable GlideException e, Object model, Target target, boolean isFirstResource) {
                    return false;
                }

                @Override
                public boolean onResourceReady(Object resource, Object model, Target target, DataSource dataSource, boolean isFirstResource) {
                    if (finalImageView != null) {
//                        SkinLogger.d("SkinGlideAspect 排查glide removeViewAttribute imageView = " + finalImageView);
                        SkinAttributesUtils.removeViewAttribute(finalImageView, SkinAttributesUtils.ATTRIBUTE_SRC);
                    }
                    return false;
                }
            });
        }
        return joinPoint.proceed();
    }
}
