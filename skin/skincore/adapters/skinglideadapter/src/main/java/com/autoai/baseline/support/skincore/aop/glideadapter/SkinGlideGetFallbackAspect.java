package com.autoai.baseline.support.skincore.aop.glideadapter;

import android.graphics.drawable.Drawable;
import android.widget.ImageView;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.res.ResManager;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils;
import com.bumptech.glide.request.BaseRequestOptions;
import com.bumptech.glide.request.SingleRequest;
import com.bumptech.glide.request.target.Target;
import com.bumptech.glide.request.target.ViewTarget;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;

import java.lang.reflect.Field;

@SuppressWarnings("unused")
@Aspect
public class SkinGlideGetFallbackAspect {

    public static SkinGlideGetFallbackAspect aspectOf() {

        SkinLogger.d("SkinGlideAspect SkinGlideGetFallbackAspect aspectOf");
        return new SkinGlideGetFallbackAspect();
    }

    @Pointcut("call(private * com.bumptech.glide.request.SingleRequest+.getFallbackDrawable(..)) " + SkinConfigs.AOP_WITHOUT + " && !within(com.autoai.baseline.support.skincore.aop.glideadapter.SkinGlideGetFallbackAspect) ")
    public void glideGetFallbackPointcut() {
        SkinLogger.d("SkinGlideAspect glideGetFallbackPointcut");
    }

    @Before("glideGetFallbackPointcut()")
    public void beforePrivateMethod(JoinPoint jp) {

        SkinLogger.d("SkinGlideAspect Before SingleRequest+.getFallbackDrawable..." + jp);
    }

    @Around("glideGetFallbackPointcut()")
    public Object aroundGlideGetFallback(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        SingleRequest<?> singleRequest = (SingleRequest<?>) target;
        SkinLogger.d("SkinGlideAspect 排查glide 0 start");
        //
        BaseRequestOptions<?> requestOptions = null;
        ImageView imageView = null;
        try {
            Class<?> obj = Class.forName("com.bumptech.glide.request.SingleRequest");
            Field requestOptionsField = obj.getDeclaredField("requestOptions");
            requestOptionsField.setAccessible(true);
            requestOptions = (BaseRequestOptions<?>) requestOptionsField.get(singleRequest);
            //
            Field targetField = obj.getDeclaredField("target");
            targetField.setAccessible(true);
            Target<?> target1 = (Target<?>) targetField.get(singleRequest);
            if (target1 instanceof ViewTarget) {
                ViewTarget<?, ?> viewTarget = (ViewTarget<?, ?>) target1;
                imageView = (ImageView) viewTarget.getView();
            }
        } catch (Throwable e) {
            SkinLogger.e("排查glide Glide Fallback 获取内容", e);
        }
        if (requestOptions != null) {
            int id = SkinConfigs.ID_NULL;
            Drawable drawable = requestOptions.getFallbackDrawable();
            if (drawable != null) {
                String key1 = drawable.hashCode() + "";
                Integer resId = ResManager.RES_MAP.get(key1);
                if (resId != null && resId != SkinConfigs.ID_NULL) {
                    id = resId;
                }
            }
            SkinLogger.d("SkinGlideAspect 排查glide 1 fallbackId = " + id + ", imageView = " + imageView);
            if (id == SkinConfigs.ID_NULL) {
                id = requestOptions.getErrorId();
            }
            SkinLogger.d("SkinGlideAspect 排查glide 2 fallbackId = " + id + ", imageView = " + imageView);
            if (id != SkinConfigs.ID_NULL) {
                if (imageView != null) {
                    SkinLogger.d("SkinGlideAspect 排查glide 3 fallbackId imageView = " + imageView);
                    SkinAttributesUtils.updateViewAttribute(imageView, SkinAttributesUtils.ATTRIBUTE_SRC, id);
                }
                return ResManager.getInstance().getDrawable(id);
            } else {
                return joinPoint.proceed();
            }
        } else {

            SkinLogger.d("SkinGlideAspect 排查glide 4 requestOptions is null");
            return joinPoint.proceed();
        }
    }
}
