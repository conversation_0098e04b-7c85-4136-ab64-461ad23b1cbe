package com.autoai.baseline.support.skincore.aop.glideadapter;

import android.graphics.drawable.Drawable;
import android.widget.ImageView;

import com.autoai.baseline.support.skincore.SkinConfigs;
import com.autoai.baseline.support.skincore.SkinLogger;
import com.autoai.baseline.support.skincore.attribute.SkinAttributesUtils;
import com.autoai.baseline.support.skincore.res.ResManager;
import com.bumptech.glide.request.BaseRequestOptions;
import com.bumptech.glide.request.SingleRequest;
import com.bumptech.glide.request.target.Target;
import com.bumptech.glide.request.target.ViewTarget;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;

import java.lang.reflect.Field;

@SuppressWarnings("unused")
@Aspect
public class SkinGlideGetPlaceholderAspect {

    public static SkinGlideGetPlaceholderAspect aspectOf() {
        SkinLogger.d("SkinGlideAspect SkinGlideGetPlaceholderAspect aspectOf");
        return new SkinGlideGetPlaceholderAspect();
    }

    @Pointcut("call(private * com.bumptech.glide.request.SingleRequest+.getPlaceholderDrawable(..)) " + SkinConfigs.AOP_WITHOUT + " && !within(com.autoai.baseline.support.skincore.aop.glideadapter.SkinGlideGetPlaceholderAspect) ")
    public void glideGetPlaceholderPointcut() {
//        SkinLogger.d("SkinGlideAspect glideGetPlaceholderPointcut");
    }

    @Before("glideGetPlaceholderPointcut()")
    public void beforePrivateMethod(JoinPoint jp) {
//        SkinLogger.d("SkinGlideAspect Before SingleRequest+.getPlaceholderDrawable..." + jp);
    }

    @Around("glideGetPlaceholderPointcut()")
    public Object aroundGlideGetPlaceholder(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        SingleRequest<?> singleRequest = (SingleRequest<?>) target;
//        SkinLogger.d("SkinGlideAspect 排查glide 0 start");
        //
        BaseRequestOptions<?> requestOptions = null;
        ImageView imageView = null;
        try {
            Class<?> obj = Class.forName("com.bumptech.glide.request.SingleRequest");
            Field requestOptionsField = obj.getDeclaredField("requestOptions");
            requestOptionsField.setAccessible(true);
            requestOptions = (BaseRequestOptions<?>) requestOptionsField.get(singleRequest);
            //
            Field targetField = obj.getDeclaredField("target");
            targetField.setAccessible(true);
            Target<?> target1 = (Target<?>) targetField.get(singleRequest);
            if (target1 instanceof ViewTarget) {
                ViewTarget<?, ?> viewTarget = (ViewTarget<?, ?>) target1;
                imageView = (ImageView) viewTarget.getView();
            }
        } catch (Throwable e) {
            SkinLogger.e("排查glide Glide Placeholder 获取内容", e);
        }
        if (requestOptions != null) {
            int id = SkinConfigs.ID_NULL;
            Drawable drawable = requestOptions.getPlaceholderDrawable();
            if (drawable != null) {
                String key1 = drawable.hashCode() + "";
                Integer resId = ResManager.RES_MAP.get(key1);
                if (resId != null && resId != SkinConfigs.ID_NULL) {
                    id = resId;
                }
            }
//            SkinLogger.d("SkinGlideAspect 排查glide 1 placeholderId = " + id + ", imageView = " + imageView);
            if (id == SkinConfigs.ID_NULL) {
                id = requestOptions.getErrorId();
            }
//            SkinLogger.d("SkinGlideAspect 排查glide 2 placeholderId = " + id + ", imageView = " + imageView);
            if (id != SkinConfigs.ID_NULL) {
                if (imageView != null) {
//                    SkinLogger.d("SkinGlideAspect 排查glide 3 placeholder imageView = " + imageView);
                    SkinAttributesUtils.updateViewAttribute(imageView, SkinAttributesUtils.ATTRIBUTE_SRC, id);
                }
                return ResManager.getInstance().getDrawable(id);
            } else {
                return joinPoint.proceed();
            }
        } else {
//            SkinLogger.d("SkinGlideAspect 排查glide 4 requestOptions is null");
            return joinPoint.proceed();
        }
    }
}
