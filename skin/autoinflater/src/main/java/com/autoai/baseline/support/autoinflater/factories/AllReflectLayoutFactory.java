package com.autoai.baseline.support.autoinflater.factories;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.NonNull;

import com.autoai.baseline.support.autoinflater.BaseLayoutFactory;

/**
 * 自定义LayoutInflater.Factory2，用于拦截属性加载皮肤
 *
 * <AUTHOR>
 */
public class AllReflectLayoutFactory extends BaseLayoutFactory {

    @Override
    protected View createViewFromTag(@NonNull String name, @NonNull Context context, @NonNull AttributeSet attrs) throws Exception {
        if (name.contains(SPLIT_POINT)) {
            //包含了 . 自定义控件
            return viewNewInstance(name, context, attrs);
        } else if (VIEW.equals(name) || VIEW_STUB.equals(name)) {
            //view 和 ViewStub
            return viewNewInstance("android.view." + name, context, attrs);
        } else if (WEB_VIEW.equals(name)) {
            //WebView
            return viewNewInstance("android.webkit." + name, context, attrs);
        } else {
            //其他 View
            return viewNewInstance("android.widget." + name, context, attrs);
        }
    }
}
