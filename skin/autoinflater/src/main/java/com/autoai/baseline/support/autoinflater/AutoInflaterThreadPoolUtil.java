package com.autoai.baseline.support.autoinflater;

import android.os.Handler;
import android.os.Looper;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程池管理类
 *
 * <AUTHOR>
 */
public class AutoInflaterThreadPoolUtil {
    private static final int SIZE_CORE_POOL = 1;
    private static final int SIZE_MAX_POOL = 1;
    private static final int TIME_KEEP_ALIVE = 5000;
    private static final int SIZE_WORK_QUEUE = 1000;
    private final Handler handler = new Handler(Looper.getMainLooper());
    private final ThreadPoolExecutor mThreadPool = new ThreadPoolExecutor(SIZE_CORE_POOL, SIZE_MAX_POOL,
            TIME_KEEP_ALIVE, TimeUnit.SECONDS,
            new ArrayBlockingQueue<Runnable>(SIZE_WORK_QUEUE),
            new ThreadFactory() {
                @Override
                public Thread newThread(Runnable runnable) {
                    Thread thread = new Thread(runnable);
                    thread.setName("AutoInflaterThread");
                    // 设置为守护线程，避免阻塞应用关闭
                    thread.setDaemon(true);
                    return thread;
                }
            });

    private static final class ThreadPoolUtilHandler {
        private static final AutoInflaterThreadPoolUtil INSTANCE = new AutoInflaterThreadPoolUtil();
    }

    /**
     * 要确保该类只有一个实例对象，避免产生过多对象消费资源，所以采用单例模式
     */
    private AutoInflaterThreadPoolUtil() {
    }

    public static AutoInflaterThreadPoolUtil getInstance() {
        return ThreadPoolUtilHandler.INSTANCE;
    }

    /**
     * Returns true if the current thread is the UI thread.
     */
    public boolean isMainThread() {
        return Thread.currentThread() == Looper.getMainLooper().getThread();
    }

//    /**
//     * Checks that the current thread is the UI thread. Otherwise throws an exception.
//     */
//    public void ensureMainThread() {
//        if (!isMainThread()) {
//            throw new RuntimeException("Must be called on the UI thread");
//        }
//    }

    public Handler getMainHandler() {
        return handler;
    }

    /**
     * 主线程执行
     */
    public void runOnUiThread(Runnable runnable) {
        handler.post(runnable);
    }

    /**
     * 主线程延迟执行
     */
    public void runOnUiThreadDelayed(Runnable runnable, long delayMillis) {
        handler.postDelayed(runnable, delayMillis);
    }

    public void singExecute(Runnable runnable) {
        mThreadPool.execute(runnable);
//        runnable.run();
    }

    /**
     * 关闭线程池，用于应用退出时清理资源
     */
    public void shutdown() {
        if (mThreadPool != null && !mThreadPool.isShutdown()) {
            mThreadPool.shutdown();
        }
    }

    /**
     * 立即关闭线程池，用于应用强制退出时清理资源
     */
    public void shutdownNow() {
        if (mThreadPool != null && !mThreadPool.isShutdown()) {
            mThreadPool.shutdownNow();
        }
    }

}
