package com.autoai.baseline.support.autoinflater;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;

import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 自定义LayoutInflater.Factory2，用于拦截属性加载皮肤
 *
 * <AUTHOR>
 */
public abstract class BaseLayoutFactory implements LayoutInflater.Factory2 {
    /**
     * 自定义View都包含完整的包名，所以必应包含.
     */
    protected static final String SPLIT_POINT = ".";
    protected static final String FRAGMENT_TAG = "fragment";
    protected static final String FRAGMENT_CONTAINER_VIEW_TAG = "FragmentContainerView";
    /**
     * xml tag就是View，则，完整路径是： android.view.View
     */
    protected static final String VIEW = "View";
    protected static final String VIEW_STUB = "ViewStub";
    /**
     * xml tag就是WebView，则，完整路径是：android.webkit.WebView
     */
    protected static final String WEB_VIEW = "WebView";

    protected static final Class<?>[] M_CONSTRUCTOR_SIGNATURE = new Class[]{Context.class, AttributeSet.class};
    protected static final Map<String, Constructor<? extends View>> CONSTRUCTOR_MAP = new HashMap<>();

    private final List<Plugin> plugins = new ArrayList<>();

    /**
     * Set whether view should have sound effects enabled for events such as clicking and touching.
     * You may wish to disable sound effects for view if you already play sounds, for instance, a dial key that plays dtmf tones.
     */
    private boolean soundEffectsEnabled = false;

    public BaseLayoutFactory() {
        InflaterLogger.v("use LayoutFactory : " + this);
    }

    /**
     * Set whether view should have sound effects enabled for events such as clicking and touching.
     * You may wish to disable sound effects for view if you already play sounds, for instance, a dial key that plays dtmf tones.
     */
    public void setSoundEffectsEnabled(boolean soundEffectsEnabled) {
        this.soundEffectsEnabled = soundEffectsEnabled;
    }

    /**
     * 添加拦截器
     */
    public void addPlugin(String key, Plugin plugin) {
        InflaterLogger.v("addInterceptor : " + key + " --> " + plugin);
        plugins.add(plugin);
    }

    @Override
    public View onCreateView(@NonNull String name, @NonNull Context context, @NonNull AttributeSet attrs) {
        if (BuildConfig.isShowDebuggingLog) {
            InflaterLogger.d("onCreateView 1 name: " + name);
        }
        return createView(name, context, attrs);
    }

    @Override
    public View onCreateView(final View parent, @NonNull final String name, @NonNull final Context context, @NonNull final AttributeSet attrs) {
        if (BuildConfig.isShowDebuggingLog) {
            InflaterLogger.d("onCreateView 2 name: " + name);
        }
        return createView(name, context, attrs);
    }

    /**
     * 根据名称创建View对象
     */
    protected abstract View createViewFromTag(@NonNull String name, @NonNull Context context, @NonNull AttributeSet attrs) throws Exception;

    /**
     * 根据名称反射创建 View 对象
     */
    protected View viewNewInstance(String name, Context context, AttributeSet attrs) throws Exception {
        InflaterLogger.i("reflect View name = " + name);
        Constructor<? extends View> constructor = CONSTRUCTOR_MAP.get(name);
        if (constructor == null) {
            Class<? extends View> aClass = context.getClassLoader().loadClass(name).asSubclass(View.class);
            constructor = aClass.getConstructor(M_CONSTRUCTOR_SIGNATURE);
            CONSTRUCTOR_MAP.put(name, constructor);
        }
        return constructor.newInstance(context, attrs);
    }


    private View createView(@NonNull String name, @NonNull Context context, @NonNull AttributeSet attrs) {
        //反射 classloader
        if (FRAGMENT_TAG.equals(name) || name.contains(FRAGMENT_CONTAINER_VIEW_TAG)) {
            return null;
        }
        View view = null;
        try {
            view = createViewFromTag(name, context, attrs);
        } catch (Exception e) {
            InflaterLogger.w("onCreateView name = " + name, e);
        }

        if (view != null) {
            view.setSoundEffectsEnabled(soundEffectsEnabled);
            //筛选符合属性的View
            if (BuildConfig.isShowDebuggingLog) {
                InflaterLogger.i("onCreateView::Plugin:: plugin.size = " + plugins.size());
            }
            for (int index = 0; index < plugins.size(); index++) {
                Plugin plugin = plugins.get(index);
                if (BuildConfig.isShowDebuggingLog) {
                    InflaterLogger.i("onCreateView::Plugin:: index = " + index + ", value = " + plugin);
                }
                plugin.task(context, attrs, view);
            }
        } else {
            if (BuildConfig.isShowDebuggingLog) {
                InflaterLogger.i("onCreateView::view is null ");
            }
        }
        return view;
    }
}
