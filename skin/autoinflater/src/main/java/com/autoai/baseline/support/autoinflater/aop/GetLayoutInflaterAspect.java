package com.autoai.baseline.support.autoinflater.aop;

import android.view.LayoutInflater;

import com.autoai.baseline.support.autoinflater.AutoInflaterManager;
import com.autoai.baseline.support.autoinflater.InflaterLogger;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 所有Activity 的 onCreate 拦截
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Aspect
public class GetLayoutInflaterAspect {

    public static GetLayoutInflaterAspect aspectOf() {
        return new GetLayoutInflaterAspect();
    }

    @Pointcut("call(* android.app.Activity+.getLayoutInflater(..)) ")
    public void getLayoutInflaterPointcut() {
    }

    @Around("getLayoutInflaterPointcut()")
    public Object aroundGetLayoutInflater(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = joinPoint.proceed();
        InflaterLogger.d("GetLayoutInflaterAspect result :" + result);
        if (result instanceof LayoutInflater) {
            LayoutInflater layoutInflater = (LayoutInflater) result;
            AutoInflaterManager.getInstance().register(layoutInflater);
        }
        return result;
    }
}
