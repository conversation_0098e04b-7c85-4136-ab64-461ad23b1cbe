package com.autoai.baseline.support.autoinflater;

import android.app.Application;
import android.content.Context;
import android.view.LayoutInflater;

import com.autoai.baseline.support.autoinflater.factories.DefaultLayoutFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * LayoutFactory 模块对外统一接口
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public class AutoInflaterManager {
    /**
     * Set whether view should have sound effects enabled for events such as clicking and touching.
     * You may wish to disable sound effects for view if you already play sounds, for instance, a dial key that plays dtmf tones.
     */
    private boolean soundEffectsEnabled = false;

    /**
     * 配置的 自定义 LayoutInflater.Factory。
     */
    private BaseLayoutFactory baseLayoutFactory;

    private List<Plugin> list;

    /**
     * 标记已经注册了的类（Activity、Service、Application等）
     */
    private final List<String> registedList = new ArrayList<>();

    /**
     * 单例
     */
    private static class Holder {
        private static final AutoInflaterManager INSTANCE = new AutoInflaterManager();
    }

    /**
     * 单例
     */
    private AutoInflaterManager() {
    }

    /**
     * 单例
     */
    public static AutoInflaterManager getInstance() {
        return Holder.INSTANCE;
    }

    /**
     * 日志输出控制
     */
    public AutoInflaterManager setLoggable(boolean isLoggable) {
        InflaterLogger.setLoggable(isLoggable);
        return this;
    }

    /**
     * 获取已注册的内容
     */
    public List<String> getRegistedList() {
        return registedList;
    }

    public void removeRegisted(Context context) {
        if (context != null) {
            registedList.remove(context.toString());
        }
    }

    /**
     * 配置自定义 LayoutInflater.Factory。
     * 如果没配置，默认使用{@link DefaultLayoutFactory}
     */
    public <T extends BaseLayoutFactory> AutoInflaterManager setLayoutFactory(T layoutFactory) {
        this.baseLayoutFactory = layoutFactory;
        return this;
    }

    /**
     * Set whether view should have sound effects enabled for events such as clicking and touching.
     * You may wish to disable sound effects for view if you already play sounds, for instance, a dial key that plays dtmf tones.
     */
    @SuppressWarnings("unused")
    public AutoInflaterManager setSoundEffectsEnabled(boolean soundEffectsEnabled) {
        this.soundEffectsEnabled = soundEffectsEnabled;
        return this;
    }

    public <T extends Plugin> AutoInflaterManager addPlugin(T plugin) {
        if (list == null) {
            list = new ArrayList<>();
        }
        list.add(plugin);
        return this;
    }

    public void init(Application application) {
        if (baseLayoutFactory == null) {
            //默认使用 DefaultLayoutFactory
            baseLayoutFactory = new DefaultLayoutFactory();
        }
        for (Plugin item : list) {
            item.init(application);
            baseLayoutFactory.addPlugin(item.getClass().getSimpleName(), item);
        }
        register(application);
    }

    /**
     * 注册LayoutFactory、FactoryInterceptor
     */
    public void register(Context context) {
        if (context != null) {
            registedList.add(context.toString());
            LayoutInflater layoutInflater = LayoutInflater.from(context);
            InflaterLogger.d("LayoutFactory register :" + context);
            register(layoutInflater);
        }
    }

    /**
     * 注册LayoutFactory、FactoryInterceptor
     */
    public void register(LayoutInflater layoutInflater) {
        if (layoutInflater != null) {
            LayoutInflater.Factory2 factory2 = layoutInflater.getFactory2();
            if (factory2 != null) {
                InflaterLogger.d("LayoutFactory register : Factory2 --> " + factory2);
                return;
            }
            if (baseLayoutFactory == null) {
                InflaterLogger.d("LayoutFactory register : baseLayoutFactory is null");
                return;
            }
            baseLayoutFactory.setSoundEffectsEnabled(soundEffectsEnabled);
            layoutInflater.setFactory2(baseLayoutFactory);
        }
    }
}
