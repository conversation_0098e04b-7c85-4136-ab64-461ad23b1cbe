package com.autoai.baseline.support.autoinflater.factories;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewStub;
import android.webkit.WebView;
import android.widget.AutoCompleteTextView;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.GridLayout;
import android.widget.GridView;
import android.widget.HorizontalScrollView;
import android.widget.ImageButton;
import android.widget.ImageSwitcher;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.ProgressBar;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.RatingBar;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.SeekBar;
import android.widget.Switch;
import android.widget.TextSwitcher;
import android.widget.TextView;
import android.widget.ViewFlipper;
import android.widget.ViewSwitcher;

import androidx.annotation.NonNull;

import com.autoai.baseline.support.autoinflater.BaseLayoutFactory;

/**
 * 自定义LayoutInflater.Factory2，用于拦截属性加载皮肤
 *
 * <AUTHOR>
 */
public class DefaultLayoutFactory extends BaseLayoutFactory {

    @Override
    protected View createViewFromTag(@NonNull String name, @NonNull Context context, @NonNull AttributeSet attrs) throws Exception {
        if (name.contains(SPLIT_POINT)) {
            //包含了 . 自定义控件
            return viewNewInstance(name, context, attrs);
        } else if (VIEW.equals(name)) {
            return new View(context, attrs);
        } else if (VIEW_STUB.equals(name)) {
            return new ViewStub(context, attrs);
        } else if (WEB_VIEW.equals(name)) {
            //WebView
            return new WebView(context, attrs);
        } else {
            //其他 View
            switch (name) {
                case "TextView":
                    return new TextView(context, attrs);
                case "ImageView":
                    return new ImageView(context, attrs);
                case "Button":
                    return new Button(context, attrs);
                case "FrameLayout":
                    return new FrameLayout(context, attrs);
                case "LinearLayout":
                    return new LinearLayout(context, attrs);
                case "RelativeLayout":
                    return new RelativeLayout(context, attrs);
                case "ScrollView":
                    return new ScrollView(context, attrs);
                case "ProgressBar":
                    return new ProgressBar(context, attrs);
                case "GridLayout":
                    return new GridLayout(context, attrs);
                case "SeekBar":
                    return new SeekBar(context, attrs);
                case "CheckBox":
                    return new CheckBox(context, attrs);
                case "EditText":
                    return new EditText(context, attrs);
                case "ListView":
                    return new ListView(context, attrs);
                case "GridView":
                    return new GridView(context, attrs);
                case "Switch":
                    return new Switch(context, attrs);
                case "ImageButton":
                    return new ImageButton(context, attrs);
                case "RadioButton":
                    return new RadioButton(context, attrs);
                case "RadioGroup":
                    return new RadioGroup(context, attrs);
                case "ViewFlipper":
                    return new ViewFlipper(context, attrs);
                case "HorizontalScrollView":
                    return new HorizontalScrollView(context, attrs);
                case "RatingBar":
                    return new RatingBar(context, attrs);
                case "TextSwitcher":
                    return new TextSwitcher(context, attrs);
                case "ViewSwitcher":
                    return new ViewSwitcher(context, attrs);
                case "ImageSwitcher":
                    return new ImageSwitcher(context, attrs);
                case "AutoCompleteTextView":
                    return new AutoCompleteTextView(context, attrs);
                default:
                    return viewNewInstance("android.widget." + name, context, attrs);
            }
        }
    }

}
