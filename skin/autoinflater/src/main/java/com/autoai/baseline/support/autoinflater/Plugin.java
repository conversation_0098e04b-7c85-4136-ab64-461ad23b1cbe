package com.autoai.baseline.support.autoinflater;

import android.app.Application;
import android.content.Context;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.NonNull;

/**
 * 页面创建拦截器
 *
 * <AUTHOR>
 */
public abstract class Plugin {

    /**
     * Application对象
     */
    protected Context applicationContext;

    /**
     * 构造方法
     * */
    public Plugin() {
    }

    public Context getApplicationContext() {
        return applicationContext;
    }

    /**
     * 初始化
     *
     * @param application Application
     */
    public void init(Application application) {
        applicationContext = application.getApplicationContext();
    }

    /**
     * 拦截
     *
     * @param context 上下文环境
     * @param attrs   当前View定义的属性
     * @param view    当前解析的View
     */
    public abstract void task(@NonNull final Context context, @NonNull final AttributeSet attrs, @NonNull View view);
}
