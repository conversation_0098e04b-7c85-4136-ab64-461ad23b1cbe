package com.autoai.baseline.support.autoinflater;

import android.util.Log;

/**
 * 日志回调，用于各个应用设置监听，将换肤框架的日志输出到对应的应用日志内部
 * 保证能够使用xLog（或者其他日志）保存日志信息。
 * 如果未指定，则默认使用 android.util.Log 输出日志到logcat。
 */
public class LogListener {
    public void v(String tag, String log) {
        Log.v(tag, log);
    }

    public void v(String tag, String log, Throwable e) {
        Log.v(tag, log, e);
    }

    public void d(String tag, String log) {
        Log.d(tag, log);
    }

    public void d(String tag, String log, Throwable e) {
        Log.d(tag, log, e);
    }

    public void i(String tag, String log) {
        Log.i(tag, log);
    }

    public void i(String tag, String log, Throwable e) {
        Log.i(tag, log, e);
    }

    public void w(String tag, String log) {
        Log.w(tag, log);
    }

    public void w(String tag, String log, Throwable e) {
        Log.w(tag, log, e);
    }

    public void e(String tag, String log) {
        Log.e(tag, log);
    }

    public void e(String tag, String log, Throwable e) {
        Log.e(tag, log, e);
    }
}
