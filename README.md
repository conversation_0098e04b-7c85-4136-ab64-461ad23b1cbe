# SGMW 全民K歌

## 项目概述
这是一个基于 Android 平台的 KTV 歌曲应用项目。

## 项目团队
- 项目负责人：张彦峰
- 产品负责人：王鑫
- UI设计：谢子暄

## 环境要求
- JDK 版本：17
- Gradle 版本：7.3
- Android Studio：最新稳定版本
- 屏幕分辨率：1920 * 1080 (dpi:141)

## 项目资源
### 文档链接
- K歌 SDK 文档：[K歌SDK文档](https://karaoketv.coding.net/p/ktvsdk3/wiki/10)
- 详细设计文档：五菱F710S项目-K歌详细设计文档
- UI设计图：[Mockplus设计图](https://app.mockplus.cn/app/QTbxH2s0I/design)
- PRD文档：[Ones PRD](https://ones.autoai.com/wiki/#/team/TEakUst8/space/4on9m1S7/page/GekJHzm8)
- UE文档：[Ones UE](https://ones.autoai.com/wiki/#/team/TEakUst8/space/4on9m1S7/page/43k7ETzx)
- 项目管理：[Ones项目](https://ones.autoai.com/project/#/team/TEakUst8/project/KuuHmEeTXwQydRVx/component/IjfO2GSf/view/KtYWbJXc/task/J6ptJE3CN63sYOU9?isHideDialog=1)
- 刷机文档：单Android代码下载编译刷机

### 开发资源
- 开发分支：QL_DEV
- 代码仓库：[Gerrit](https://gerrit-znzd.autoai.com/admin/repos/autoai/app/sgmw/sgmwksongs,general)
- 排期文档：五菱F710S系统软件功能排期

### 沟通渠道
- 微信沟通群：五菱F710SK歌沟通群

## 项目配置

### 1. 环境变量配置
项目支持多种环境配置，通过环境变量进行切换：

- 开发环境：默认环境
- RDM环境：设置环境变量 `rdmEnv`
- DevOps环境：设置环境变量 `DEVOPS_ENV`

### 2. 本地配置
在项目根目录创建 `local.properties` 文件，配置以下参数：
```properties
KTV_SDK_APP_ID=your_app_id
KTV_SDK_APP_KEY=your_production_app_key
KTV_SDK_TEST_APP_KEY=your_test_app_key
```

### 3. 依赖仓库配置
项目使用以下仓库：
- JCenter
- Maven Central
- Google Maven
- 阿里云 Maven 镜像
- JitPack
- 公司内部仓库（需要配置认证信息）

## 开发规范
### Commit提交规则
请参考：[Commit规范文档](https://ones.autoai.com/wiki/#/team/TEakUst8/space/UwjQohpd/page/6rfnJnBg)

### UI规范
- 按钮文字置灰：40%透明度
- 按钮按压状态：70%透明度
- 图标置灰：在控件画板中设置，缺图请联系UI设计师

## 版本管理
项目使用以下版本控制：
- 自动构建版本号：基于环境变量 `VERSION_CODE` 和 `VERSION_NAME`
- 构建时间戳：自动添加到版本号中

## 注意事项
1. 确保正确配置 `local.properties` 文件中的密钥信息
2. 切换环境时，需要正确设置相应的环境变量
3. 首次构建时，可能需要较长时间下载依赖
4. 如果遇到网络问题，可以尝试使用阿里云镜像源
5. 五菱Jira账号：hS2500002，密码：Sgmw5050
6. VPN使用请参考：EasyConnect VPN使用方法（五菱）

## 技术支持
如有问题，请联系项目维护团队。 