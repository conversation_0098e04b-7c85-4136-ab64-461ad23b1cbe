
import com.autoai.car.buildsrc.Versions

ext {
    minSdkVersion = Versions.MIN_SDK
    targetSdkVersion = Versions.TARGET_SDK
    //layout处理
    autoinflaterVersionCode = 18
    autoinflaterVersionName = "1.0.14"
    //换肤框架核心
    skinframeworkVersionCode = 301
//    skinframeworkVersionName = "2.2.7"
    skinframeworkVersionName = "2.2.8-debug-01"
    //换肤 androidx 适配
    skinandroidxVersionCode = 44
    skinandroidxVersionName = "2.1.7"
    //
//        skinAndroidSupportVersionCode = 28
//        skinAndroidSupportVersionName = "1.0.7"
    //换肤 glide 适配
    glideadapterVersionCode = 49
    glideadapterVersionName = "2.1.2"


    //换肤框架：性能检测工具
    performancetestingVersionCode = 6
    performancetestingVersionName = '0.0.6'
    //
    aspectjrt = 'org.aspectj:aspectjrt:1.9.5'
}

println("//核心框架")
println("implementation 'com.autoai.baseline.skincore:autoinflater:${rootProject.ext.autoinflaterVersionName}'")
println("implementation 'com.autoai.baseline.skincore:skinframework:${rootProject.ext.skinframeworkVersionName}'")

println("//AndroidX适配")
println("implementation 'com.autoai.baseline.skincore:skinAndroidx:${rootProject.ext.skinandroidxVersionName}'")
//    println("implementation 'com.autoai.baseline.skincore:skinAndroidSupport:${rootProject.ext.skinAndroidSupportVersionName}'")

println("//对 Glide 适配")
println("implementation 'com.autoai.baseline.skincore:glideadapter:${rootProject.ext.glideadapterVersionName}'")

//    println("//耗时验证脚本")
//    println("implementation 'com.autoai.baseline.skincore:performancetesting:${rootProject.ext.performancetestingVersionName}'")
