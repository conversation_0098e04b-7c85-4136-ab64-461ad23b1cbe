package com.autoai.car.buildsrc

/**
 * @description:
 * @author: sunzp
 * @date:  2021/11/24 16:31
 * @mail: <EMAIL>
 * @copyright: www.navinfo.com Inc. All rights reserved.
 */
object Libs {
    object ProjectPluginManager {
        const val buildGradle = "com.android.tools.build:gradle:7.1.0"
//        const val buildGradle = "com.android.tools.build:gradle:8.1.1"
        const val kotlinGradlePlugin = "org.jetbrains.kotlin:kotlin-gradle-plugin:1.7.20"

        const val HiltPlugin = "com.google.dagger:hilt-android-gradle-plugin:2.38.1"
        const val AspectjxPlugin = "io.github.wurensen:gradle-android-plugin-aspectjx:3.3.2"
        const val NavigationSafeArgs = Navigation.NavigationSafeArgs
        const val sensorsDataAnalytics = "com.sensorsdata.analytics.android:android-gradle-plugin2:3.1.9"
    }

    const val material = "com.google.android.material:material:1.3.0"

    object Androidx {
        const val coreKtx = "androidx.core:core-ktx:1.7.0"
        const val appcompat = "androidx.appcompat:appcompat:1.3.1"
        const val constraintlayout = "androidx.constraintlayout:constraintlayout:2.1.3"
        const val Cardview = "androidx.cardview:cardview:1.0.0"
        const val MultiDex = "androidx.multidex:multidex:2.0.1"
        const val ActivityKtx = "androidx.activity:activity-ktx:1.4.0"
        const val FragmentKtx = "androidx.fragment:fragment-ktx:1.4.1"
    }

    object AndroidTest {
//        const val junit = "junit:junit:4.+"
//        const val androidxTestExtJunit = "androidx.test.ext:junit:1.1.2"
//        const val androidxTestEspresso = "androidx.test.espresso:espresso-core:3.3.0"

        // 基础
        // 漏洞扫描报告， 需要 junit4 的 4.13.1 以下有漏洞。
        const val junit = "junit:junit:4.13.1"
        const val androidxTestExtJunit = "androidx.test.ext:junit:1.1.5"
        const val androidxTestEspresso = "androidx.test.espresso:espresso-core:3.5.1"

        // 拓展
        const val mockito_junit = "org.mockito:mockito-junit-jupiter:3.+"
        const val robolectric = "org.robolectric:robolectric:4.4"
        const val robolectric_492=  "org.robolectric:robolectric:4.12.2"
        const val shadows_multidex = "org.robolectric:shadows-multidex:4.12.2"
        const val mockito_lib = "org.mockito:mockito-core:5.2.0"
        const val mockito_inline = "org.mockito:mockito-inline:5.2.0"
        const val test_fragment = "androidx.fragment:fragment-testing:1.4.1"

        // android  实际不会用
        const val mockito_android_lib = "org.mockito:mockito-android:5.2.0"

        // mockk
        const val mockk_version = "1.12.8"
        const val mockk_lib = "io.mockk:mockk:$mockk_version"
        const val mockk_android_lib = "io.mockk:mockk-android:$mockk_version"
        const val mockk_agent_jvm_lib = "io.mockk:mockk-agent-jvm:$mockk_version"


        const val testing_core = "androidx.arch.core:core-testing:2.1.0"

    }

//    object Arouter {
//        const val version = "1.5.2"
//        const val arouterApi = "com.alibaba:arouter-api:$version"
//        const val arouterCompiler = "com.alibaba:arouter-compiler:$version"
//    }

    const val utilcodex = "com.blankj:utilcodex:1.30.6"

    object Retrofit {
        const val Okhttp3 = "com.squareup.okhttp3:okhttp:3.12.0"
        const val Okhttp3Logging = "com.squareup.okhttp3:logging-interceptor:3.11.0"

        //        const val Okio = "com.squareup.okio:okio:2.0.0"
        const val Retrofit = "com.squareup.retrofit2:retrofit:2.4.0"
        const val RetrofitConverterGson = "com.squareup.retrofit2:converter-gson:2.6.0"

        //        const val RetrofitConverterScalars = "com.squareup.retrofit2:converter-scalars:2.4.0"
//        const val RetrofitAdapterRxjava = "com.squareup.retrofit2:adapter-rxjava2:2.4.0"
        const val Okhttp3_new = "com.squareup.okhttp3:okhttp:4.10.0"
        const val Okhttp3Logging_new = "com.squareup.okhttp3:logging-interceptor:4.10.0"
    }


//    object Rx {
//        const val RxJava = "io.reactivex.rxjava2:rxjava:2.2.10"
//        const val RxAndroid = "io.reactivex.rxjava2:rxandroid:2.1.1"
//        const val RxBinding = "com.jakewharton.rxbinding3:rxbinding:3.0.0"
//    }

    const val MMKV = "com.tencent:mmkv:1.3.0"

    object Glide {
        const val glide = "com.github.bumptech.glide:glide:4.16.0"
        const val glideProcessor = "com.github.bumptech.glide:compiler:4.16.0"
        const val glideTransform = "jp.wasabeef:glide-transformations:3.3.0"

    }

    const val BaseRecyclerViewAdapterHelper =
        "io.github.cymchad:BaseRecyclerViewAdapterHelper:3.0.11"

    // Gson 解析容错 ： https://github.com/getActivity/GsonFactory
//    const val GsonFactory = "com.github.getActivity:GsonFactory:5.2"

    const val Gson = "com.google.code.gson:gson:2.10.1"

    const val Kotlin = "org.jetbrains.kotlin:kotlin-stdlib:1.6.21"
    //Test related third party libraries
    object LifeCycle {
        private const val lifecycle_version = "2.2.0"

        //        const val LifeCycle = "androidx.lifecycle:lifecycle-extensions:$lifecycle_version"
//        const val LifeCycleRuntime = "androidx.lifecycle:lifecycle-runtime:$lifecycle_version"
//        const val LifeCycleLivedataKtx =
//            "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycle_version"
        const val LifeCycleViewmodelKtx =
            "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version"
        const val LifeCycleProcess =
            "androidx.lifecycle:lifecycle-process:$lifecycle_version"
    }

    //kotlin coroutines
    object Coroutines {
        private const val coroutines_version = "1.6.1"
        const val CoroutinesTest = "org.jetbrains.kotlinx:kotlinx-coroutines-test:$coroutines_version"
        const val Coroutines = "org.jetbrains.kotlinx:kotlinx-coroutines-core:$coroutines_version"
        const val CoroutinesAndroid =
            "org.jetbrains.kotlinx:kotlinx-coroutines-android:$coroutines_version"

    }

    const val EventBus = "org.greenrobot:eventbus:3.2.0"
    // 车机系统提供 车载相关jar包
    const val AndroidCar = "com.sw.car:android.car:2.0.36-SNAPSHOT"
    // 权限申请库
    const val PermissionClient = "com.sgmw.permissionclient:permissionclient:1.0.3@aar"
    // AutoSize 屏幕适配库
    const val androidAutoSize = "com.github.JessYanCoding:AndroidAutoSize:v1.2.1"
//    object Hilt {
//        private const val hilt_version = "2.38.1"
//        const val HiltCore = "com.google.dagger:hilt-android:${hilt_version}"
//        const val HiltApt = "com.google.dagger:hilt-compiler:${hilt_version}"
//    }

//    object AutoService {
//        private const val autoservice_version = "1.0"
//        const val AutoService = "com.google.auto.service:auto-service:${autoservice_version}"
//        const val AutoServiceAnnotations =
//            "com.google.auto.service:auto-service-annotations:${autoservice_version}"
//    }

    object Navigation {
        private const val navigation_version = "2.5.3"
        const val NavigationFragment = "androidx.navigation:navigation-fragment-ktx:$navigation_version"
        const val NavigationUiKtx = "androidx.navigation:navigation-ui-ktx:$navigation_version"
        const val NavigationSafeArgs = "androidx.navigation:navigation-safe-args-gradle-plugin:$navigation_version"
    }

    const val Banner = "com.github.zhpanvip:bannerviewpager:3.5.11"

//    const val Coil = "io.coil-kt:coil:2.2.2"

    const val FlexBox = "com.google.android.flexbox:flexbox:3.0.0"

    const val pinyin = "com.github.promeg:tinypinyin:2.0.3" // TinyPinyin核心包，约80KB

    const val pinyin_android = "com.github.promeg:tinypinyin-lexicons-android-cncity:2.0.3"// 可选，适用于Android的中国地区词典

    const val pinyin_java=  "com.github.promeg:tinypinyin-lexicons-java-cncity:2.0.3" // 可选，适用于Java的中国地区词典

    object Autoai {
        const val SkinFramework = "com.autoai.baseline.skincore:skinframework:2.2.6"
        const val AutoInflater = "com.autoai.baseline.skincore:autoinflater:1.0.12"
        const val SkinAndroidx = "com.autoai.baseline.skincore:skinAndroidx:2.1.7"
        const val SkinAndroidSupport = "com.autoai.baseline.skincore:skinAndroidSupport:1.0.7"
        const val SkinGlideAdapter = "com.autoai.baseline.skincore:glideadapter:2.1.2"

    }

    const val Aspectj = "org.aspectj:aspectjrt:1.9.5"

    object Room {
        private const val version = "2.5.0"

        // 数据库
        const val roomRuntime = "androidx.room:room-runtime:$version"
        const val roomKtx = "androidx.room:room-ktx:$version"
        const val roomCompiler = "androidx.room:room-compiler:$version"
    }

    const val WORK_MANAGER = "androidx.work:work-runtime-ktx:2.7.1"

    object Media {
        private const val exoplayerversion = "2.18.5"
        const val media = "androidx.media:media:1.6.0"

        //Exoplayer
        const val exoplayerCore = "com.google.android.exoplayer:exoplayer-core:$exoplayerversion"
        const val exoplayerDash = "com.google.android.exoplayer:exoplayer-dash:$exoplayerversion"
        const val exoplayerHls = "com.google.android.exoplayer:exoplayer-hls:$exoplayerversion"
        const val exoplayerUi = "com.google.android.exoplayer:exoplayer-ui:$exoplayerversion"
        const val exoplayerSmoothstreaming =
            "com.google.android.exoplayer:exoplayer-smoothstreaming:$exoplayerversion"
        const val exoplayerRtmp = "com.google.android.exoplayer:extension-rtmp:$exoplayerversion"
        const val exoplayerRtsp = "com.google.android.exoplayer:exoplayer-rtsp:$exoplayerversion"
        const val exoplayerOkhttp =
            "com.google.android.exoplayer:extension-okhttp:$exoplayerversion"

        //Collections
        const val collection = "androidx.collection:collection:1.1.0"

        //relinker
        const val relinker = "com.getkeepsafe.relinker:relinker:1.3.1"

        //1.0.7.0及以后版本新增
        const val webkit = "androidx.webkit:webkit:1.4.0"
    }

    const val zxing = "com.google.zxing:core:3.5.3"

    object Common {
        const val commonVersion = "2.0.5.1"
        const val commonProjectVersion = "2.0.5.2"

        //日志系统
        const val log = "com.autoai.avs.common:log:$commonVersion"
        const val sign = "com.autoai.avs.common:sign:$commonVersion"
        const val base = "com.autoai.avs.common:base:$commonVersion"
        const val deviceInfo = "com.autoai.avs.common:device-info:$commonVersion"
        const val config = "com.autoai.avs.common:config:$commonVersion"
        const val ui = "com.autoai.avs.common:common-ui:$commonVersion"
        const val user = "com.autoai.avs.common:user:$commonVersion"
        const val audio = "com.autoai.avs.common:audio:$commonVersion"
        const val carSystem = "com.autoai.avs.common:carSystem:$commonVersion"
        const val carMode = "com.autoai.avs.common:carMode:$commonVersion"
        const val gbook = "com.autoai.avs.common:gbook:$commonVersion"
        const val phone = "com.autoai.avs.common:phone:$commonVersion"
        const val carData = "com.autoai.avs.common:carData:$commonVersion"
        const val carDiagTrace = "com.autoai.avs.common:carDiagTrace:$commonVersion"
        const val carTraceHub = "com.autoai.avs.common:carTraceHub:$commonVersion"
        // 注意，这个common 库的版本，和上面几个 common 库的版本可能不一致。以 common发布为准。
        // 参考文档 https://navinfo.feishu.cn/docx/YdwedFiCcoYdwcxRHZscp4nfnzg?from=from_copylink
        const val commonLexusProject = "com.autoai.avs.common:project_lexus:${commonProjectVersion}" //project包
        const val commonToyotaProject = "com.autoai.avs.common:project_toyota:${commonProjectVersion}" //project包
        const val visualVoice = "com.autoai.vr:visual_voice:0.9.9"
    }


    object ByteDanceAppLog {
        // 如您不需要全埋点采集、圈选功能，仅需要自定义埋点，可集成Lite版本
        const val appLog = "com.bytedance.applog:RangersAppLog-Lite-cn:6.16.2"

        // DevTools是Debug环境下辅助开发者或测试人员进行应用内埋点验证和SDK接入问题排查的组件。
        //const val devTools = "com.bytedance.applog:RangersAppLog-DevTools:3.3.2"

        // 实时埋点检测和圈选功能
        //const val scheme = "com.bytedance.applog:RangersAppLog-All-scheme:6.16.2"
    }

    object SmartRefreshLayout {
        const val base = "io.github.scwang90:refresh-layout-kernel:2.0.6"      //核心必须依赖
        const val classicsHeader = "io.github.scwang90:refresh-header-classics:2.0.6"    //经典刷新头
        const val classicFooter = "io.github.scwang90:refresh-footer-classics:2.0.6"    //经典加载
        const val horizontal = "com.scwang.smart:refresh-layout-horizontal:2.0.0" // 横向
    }

    object Skeleton {
        const val skeleton = "com.ethanhua:skeleton:1.1.2"
        const val skeleton_shimmer = "io.supercharge:shimmerlayout:2.1.0"
    }

    const val viewpager_indicator = "com.github.zhpanvip:viewpagerindicator:1.2.3"
    // 页面加载状态库
//    const val loadsir  = "com.kingja.loadsir:loadsir:1.3.8"

    const val sw_framework = "com.sw.framework:android:2.0.1"

    const val VrSdk = "com.sgmw.vrsdk:vrsdk:1.1.4-SNAPSHOT@aar"
    const val leakcanary = "com.squareup.leakcanary:leakcanary-android:2.14"
}

//版本相关
object Versions {
    //编译版本
    const val COMPILE_SDK = 33
    //最小版本
    const val MIN_SDK = 29
    // 目标版本
    const val TARGET_SDK = 33
    //versionCode
    const val VERSION_CODE = 202501141
    // versionName
    const val VERSION_NAME = "1.0.0"
   // Application id
    const val APPLICATION_ID = "com.sgmw.ksongs"

    const val KTV_SDK_APP_ID = 100117

    const val KTV_SDK_TEST_APP_KEY = "3600824fd368f3029783cd40480e54f8"
}