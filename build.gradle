import com.autoai.car.buildsrc.Libs

import java.text.SimpleDateFormat
apply from: "${project.rootDir}/functions.gradle"

// Top-level build file where you can add configuration options common to all sub-projects/modules.


ext {
    isRdmEnv = System.getenv("rdmEnv") != null
    isDevopsEnv = System.getenv("DEVOPS_ENV") != null
    releaseMark = (isRdmEnv || isDevopsEnv)
    versionCodeDevOps = System.getenv("VERSION_CODE") != null ? Integer.parseInt(System.getenv("VERSION_CODE")) :
            1
    versionNameDevOps = System.getenv("VERSION_NAME") != null ? System.getenv("VERSION_NAME") :
            "1.0.0.0"
    println("KTV_DEVOPS isRdmEnv: $isRdmEnv, isDevopsEnv:$isDevopsEnv")
    def date = new Date()
    def sdf = new SimpleDateFormat("yyyy_MM_dd HH:mm:ss")
    def dateString = sdf.format(date)
    sdkBuildVersion = versionNameDevOps + "_" + System.getenv("BK_CI_BUILD_NUM") + "_" + dateString

    //读取本地配置
    def localPropFile = project.rootProject.file('local.properties')
    ktvSdkAppId = ''
    ktvSdkAppKey = ''
    ktvSdkTestAppKey=''
    if (localPropFile.exists()) {
        Properties properties = new Properties()
        properties.load(localPropFile.newDataInputStream())
        ktvSdkAppId = properties.getProperty("KTV_SDK_APP_ID", "").trim()
        ktvSdkAppKey = properties.getProperty("KTV_SDK_APP_KEY", "").trim()  //正式环境appkey
        ktvSdkTestAppKey = properties.getProperty("KTV_SDK_TEST_APP_KEY", "").trim()    //测试环境appkey
    }
}

buildscript {
    apply from: "${project.rootDir}/versions.gradle"
    repositories {
        jcenter()
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://www.jitpack.io' }
        maven {
            allowInsecureProtocol true
            url 'http://syshome.autoai.com/artifactory/repository/sw-release/'
            credentials {
                username 'swdeveloper'
                password 'swdeveloper'
            }
        }
        maven {
            allowInsecureProtocol true
            url 'http://syshome.autoai.com/artifactory/repository/sw-snapshot/'
            credentials {
                username 'swdeveloper'
                password 'swdeveloper'
            }
        }
        mavenCentral()
        google()
    }

    dependencies {
        // gradle 插件
        classpath Libs.ProjectPluginManager.buildGradle
        // kotlin 插件
        classpath Libs.ProjectPluginManager.kotlinGradlePlugin
        // AspectJ 插件
        classpath Libs.ProjectPluginManager.AspectjxPlugin
        // Navigation Safe Args 插件
        classpath Libs.ProjectPluginManager.NavigationSafeArgs
        //添加 Sensors Analytics android-gradle-plugin 依赖
        classpath Libs.ProjectPluginManager.sensorsDataAnalytics

    }
}

//apply from: "${project.rootDir}/config.gradle"

task clean(type: Delete) {
    delete rootProject.buildDir
}